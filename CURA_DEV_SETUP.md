# Cura 开发环境搭建记录

## 项目概述
Cura是Ultimaker公司开发的开源3D打印切片软件，基于Python和Qt框架构建。本文档记录在macOS系统上搭建Cura开发环境的完整过程。

## 系统环境
- 操作系统: macOS
- Python版本: 3.13.5 (系统已安装)
- IDE: PyCharm (推荐使用)

## 官方文档参考
- [Getting Started](https://github.com/Ultimaker/Cura/wiki/Getting-Started)
- [Running Cura from Source](https://github.com/Ultimaker/Cura/wiki/Running-Cura-from-Source)

## 系统依赖检查

### 当前系统状态
```bash
# Python版本检查
python3 --version  # Python 3.13.5 ✓

# CMake版本检查  
cmake --version    # cmake version 3.27.9 ✓

# Ninja版本检查
ninja --version    # 1.11.1 ✓

# Conan版本检查
conan --version    # Conan version 1.60.2 ❌ (需要2.7.0)
```

### 系统要求对比
根据官方文档，macOS系统需要：
- ✅ macOS 11 or higher
- ✅ xcode 12 or higher  
- ✅ apple-clang-12.0 or higher
- ❌ Python 3.12 or higher (当前3.13.5，版本过高可能有兼容性问题)
- ✅ venv (Python)
- ❌ sip (Python) 6.5.0 or higher (需要安装)
- ✅ altool
- ✅ automake
- ✅ CMake 3.23 or higher (当前3.27.9)
- ✅ Ninja 1.10 or higher (当前1.11.1)
- ❌ Conan >=2.7.0 <3.0.0 (当前1.60.2，版本过低)

## 环境搭建步骤

### 第一步：创建Python虚拟环境
目的：隔离Cura开发环境，避免与系统Python环境冲突

```bash
# 创建虚拟环境
python3 -m venv cura_venv

# 激活虚拟环境
source cura_venv/bin/activate
```

### 第二步：安装正确版本的Conan
目的：Cura使用Conan 2.x作为包管理器，需要安装指定版本

问题：当前系统安装的是Conan 1.60.2，但Cura需要Conan >=2.7.0 <3.0.0
解决方案：在虚拟环境中安装正确版本的Conan

### 第三步：配置Conan
目的：设置Conan的配置文件和profile

### 第四步：克隆Cura源码
目的：获取最新的Cura源代码（已完成）

### 第五步：初始化开发环境
目的：使用Conan安装所有依赖项

### 第六步：运行Cura
目的：验证环境搭建是否成功

## 遇到的问题和解决方案

### 问题1：Python版本兼容性
- **问题描述**：系统Python版本为3.13.5，而Cura要求Python 3.12+
- **分析**：虽然版本满足最低要求，但可能存在兼容性问题
- **解决思路**：先尝试使用当前版本，如有问题再考虑降级

### 问题2：Conan版本不匹配
- **问题描述**：系统安装的Conan版本为1.60.2，Cura需要2.7.0+
- **分析**：Conan 1.x和2.x有重大差异，必须使用正确版本
- **解决思路**：在虚拟环境中安装Conan 2.7.0

## 下一步操作
1. 激活虚拟环境
2. 安装Conan 2.7.0
3. 配置Conan
4. 运行conan install命令
5. 测试Cura启动
