# Cura
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.1\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-21 15:37+0000\n"
"PO-Revision-Date: 2020-03-24 09:43+0100\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: AT-VLOG\n"
"Language: hu_HU\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n != 1);\n"
"X-Generator: Poedit 2.2.4\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr ""

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr ""

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr ""

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "A modell szélétől tartandó távolság. Ha a vasalás kifutna a test külső éleihez, az egyenetlenséget okozhatna ott."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr ""

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr ""

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Azon egész vonalirányok listája, amelyeket akkor kell használni, amikor a felső felületi rétegek vonal, vagy cikcakk mintáját használjuk.A lista elemeit egymás után használjuk a rétegek előre haladtával, és amikor a végére ér, előlről kezdi. A lista elemit vesszővel választjuk el, és a teljes lista szögletes zárójelben van. Az alapértelmezett lista üres ami azt is jelenti, hogy az alapértelmezett 45 és 135 fokos szögeket hasznájuk."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Az egész vonal-irányok listája, amelyet akkor kell használni, ha az alsó/felső rétegek a vonalas vagy cikcakk mintákat használják.A lista elemeit egymás után használják a rétegek előrehaladtával, és amikor a lista vége eléri, akkor újra előlről kezdi.A lista elemeit vesszők választják el, és a teljes listát szögletes zárójelben tartalmazza. Az Alapértelmezés egy üres lista, amely azt jelenti, hogy a hagyományos alapértelmezett szögeket (45 és 135 fok) kell használni."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Ez egy lista a teljes vonalak irányáról. A lista elemeit egymás után használják a rétegek, s mikor a lista a végére ért, elkezdi előlről. A lista elemeit vesszővel választja el, és a teljes lista sz9gletes zárójelben van.Az alapértelmezett az üres lista, ami azt is jelenti, hogy az alapértelmezett 0 fokos szöget használja."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "A használt teljes vonalaknak a listája. Az elemeket vesszővel választjuk el, és az egész lista szögletes zárójelek között van.Az alapértelmezett lista üres. Ebben az esetben a 45 és a 135 fok között változik az irányszög. A listát az elejéről kezdi, és rétegenként veszi az irányokat. Ha a lista végére ér, akkor előlről kezdi."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "A használt teljes vonalaknak a listája. Az elemeket vesszővel választjuk el, és az egész lista szögletes zárójelek között van.Az alapértelmezett lista üres. Ebben az esetben a 45 és a 135 fok között változik az irányszög. A listát az elejéről kezdi, és rétegenként veszi az irányokat. Ha a lista végére ér, akkor előlről kezdi."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "A használt teljes vonalaknak a listája. Az elemeket vesszővel választjuk el, és az egész lista szögletes zárójelek között van.Az alapértelmezett lista üres. Ebben az esetben a 45 és a 135 fok között változik az irányszög. A listát az elejéről kezdi, és rétegenként veszi az irányokat. Ha a lista végére ér, akkor előlről kezdi."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Azoknak a teljes vonalaknak az iránya, amiket kitöltéskor használunk.A lista elemeit egymás után használják fel a rétegek, és előlről kezdik, ha a lista a végére ért. A lista elemeit vesszők választják el, míg a teljes lista szögletes zárójelben van. Az alapértelmezett esetbe a lista üres, ilyenkor az alapértelmezett 45 és 135 fokos szögeket használjuk a vonalas, és a cikcakk kitöltési mintakor, míg 45 fokot az összes többi esetben."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "A sokszögek listája azon területekkel, ahová a fúvóka nem léphet be."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "A sokszögek listája azon területekkel, ahová a nyomtatófej nem léphet be."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr ""

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Abszolút kezdő pozíció"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Adaptív rétegek maximális variációja"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr ""

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Lépésméret"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Az adaptív rétegek kiszámítják a szükséges rétegmagasságokat a modell alakjától függően."

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr ""

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"További falakat ad a kitöltési terület köré. Ezek a falak segíthetik az alsó/felső kéreg vonalak tapadását, így azok kevésbé tudnak elválni a kitöltéstől.Ennek előnye lehet, hogy kevesebb kéreg rétegre van szükség ugyanazon minőség eléréséhez. Ez azonban növelheti az anyagköltséget. \n"
"Kombinálható a kitöltési sokszögek csatlakozása beállítással, hogy az összes kitöltés egyetlen útvonalba kapcsolja anélkül, hogy felesleges fej utaztatás vagy szálvisszahúzás lenne."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Tapadás"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tapadási jellemző"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "A falak és a kéregek középvonalai közötti átfedés mértékét határozza meg, százalékos értékben. A kéreg vonalak és a legbelsőbb fal szélességéből számítjuk. Az enyhe átfedés lehetővé teszi, hogy a falak szorosan kapcsolódjanak a kéreg vonalakhoz. Figyeljünk rá, hogy az egyenlő kéreg és falvonal szélesség esetén az 50% fölötti érték már átüthet a falon, mivel ezen a ponton a kéreg extruder fúvóka pozíciója már a fal középvonalát eléri."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Az átfedés mértékét állítja be a falak és a felszíni vonalak középpontjainak végpontjai között. Az enyhe átfedés lehetővé teszi a falak szoros kapcsolódását  a felszínhez. Vegye figyelembe, hogy ha a kéreg és a fal vonalszélessége egyenlő, akkor a fal szélességének felét meghaladó érték bármilyen felszín esetén  áthaladhat a falon, mert ezen a ponton a kéreg-extruder fúvóka pozíciója már elérheti a fal közepén."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Beállítja a nyomtatás kitöltési sűrűségét."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Beállítja a támasz interfész sűrűségét a támasz alsó és a felső felületein.A magasabb érték jobb minőségű túlnyúlás nyomtatást tesz lehetővém viszont a támaszt nehezebb lesz eltávolítani."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr ""

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "A támaszok belső sűrűségének beállítása. A magasabb érték jobb alátámasztást nyújt a kinyúlásokhoz, viszont nehezebb lesz a támaszokat eltávolítani."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Nyomtatószál átmérő beállítása. Itt állítsd be a te általad használt nyomtatószál átmérőt. Ennek egyeznie kell a gép paramétereivel."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "A támaszok elhelyezésének beállítása. A támaszokat elhelyezhetjük úgy, hogy azokcsak az asztalon támaszkodhatnak, azaz azoknak érinteniük kell az asztalt.Ha azonban a mindenhol beállítást használjuk, akkor a támaszok a modell egyéb felületein is felépülhetnek."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Miután kinyomtatta az előtornyot a gép, az inaktív fúvókán esetlegesen kicsöppenő anyagot letörli róla az előtoronyba."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Extruder váltás után a Z tengely és így a fej megemelkedik, így ez megakadályozza, hogy a fúvókából esetlegesen kicsöppenő anyag a nyomtatás külső felületére kenődjön."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Minden"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Egyidőben"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr ""

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Az összes beállítás, ami befolyásolja a nyomtatvány felbontását és minőségét. Ezekek a beállítások hatással vannak a minőségre és a nyomtatási sebességre."

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr ""

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alternatív extra fal"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alternatív háló eltávolítása"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr ""

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr ""

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Alumínium"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr ""

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Minden esetben, mikor a külső fal nyomtatása fog történni, a pozicionáláskor szál visszahúzás fog történni."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Az egyes rétegek sokszögeire alkalmazott bővítés mennyisége. A pozitív értékek kompenzálhatják a túl nagy lyukakat, míg a negatív értékek a túl kicsi lyukakat képesek kompenzálni."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "A kezdő réteg sokszögeire alkalmazott bővítés mennyisége. A negatív érték kompenzálhatja az első réteg túlömlését, amit úgy is neveznek, hogy \"elefánt láb\"."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Az egyes rétegek minden támasz poligonjára alkalmazott eltolás mennyisége. A pozitív értékek kiegyenlíthetik a támasz területeket, és erősebb támasztást eredményezhetnek."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "A támaszaljzat interész sokszögeire alkalmazott eltolás összege."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "A támaszfedél interész sokszögeire alkalmazott eltolás összege."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "A támasz interész sokszögeire alkalmazott eltolás összege."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "A visszahűzandó anyagmennyiség azért, hogy a törlési művelet során ne legyen anyagcsöppenés a fúvókából."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Az egyes kitöltési kockák közepétől számított sugár hozzáadásával ellenőrizni kell a modell határát, hogy eldönthesse a szeletelő, hogy ezt a kockát fel kell-e osztan, vagy sem.A nagyobb értékek a kocka vastagabb héjához vezetnek a modell szélének közelében."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Túlnyúlás gátló háló"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Szivárgásgátló visszahúzási helyzet"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Szivárgásgátló visszahúzás sebesség"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr ""

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr ""

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Kinyomtatott részek kerülése utazáskor"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Támasz elkerülése utazáskor"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Hátra"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Hátra balra"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Hátra jobbra"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes (BFB)"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Mindkettő"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr ""

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Alsó rétegek"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Alsó kezdő réteg mintázata"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Alsó kéreg bővítési távolság"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Alsó kéreg eltávolítási szélesség"

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr ""

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr ""

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr ""

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr ""

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr ""

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr ""

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr ""

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr ""

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr ""

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr ""

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr ""

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr ""

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr ""

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr ""

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr ""

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr ""

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr ""

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Alsó vastagság"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr ""

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr ""

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr ""

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Szakadás előállítás visszahúzott helyzetben"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Szakadás előállítás visszahúzási sebeség"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr ""

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Sazakdás visszahúzási helyzet"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Szakítás visszahúzási sebesség"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Szakítási hőmérséklet"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Támasz tördelhetősége"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Híd hűtési sebesség"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Többrétegű híd"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Második hídréteg sűrűség"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Második hídréteg hűtési sebessége"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Második hídréteg adagolás"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Második hídréteg sebessége"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Hídfelszín sűrűsége"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Hídfelszín adagolás"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Hídfelszín sebesség"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Felület támasz küszöb"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr ""

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Harmadik hídréteg sűrűség"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Harmadik hídréteg hűtési sebessége"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Harmadik hídréteg adagolás"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Harmadik hídréteg sebesség"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Híd fal kifutás"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Hídfal adagolás"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Hídfal sebesség"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Perem"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr ""

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr ""

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Perem vonalszám"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr ""

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Perem támasz helyett"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Perem szélesség"

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr ""

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr ""

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Tárgyasztal tapadás"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Tapadás extruder"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Tárgyasztal tapadási típus"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Tárgyasztal anyaga"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Tárgyasztal alakja"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Tárgyasztal hőmérséklete"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Tárgyasztal hőmérséklet a kezdő rétegnél"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Építési tér hőmérséklete"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr ""

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr ""

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr ""

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr ""

msgctxt "center_object label"
msgid "Center Object"
msgstr "Tárgy középpontba"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "A modell geometriáját fogja megváltoztatni oly módon, hogy minimális támasz legyen szükséges a nyomtatáshoz. A meredek kinyúlások sekélyessé fognak válni, míg a túlnyúló területek függőlegesebbé."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr ""

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Kifutási sebesség"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Kifutási mérték"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "A kifutás során, az adott rész nyomtatásakor, az extrudálási út vége előtt, egy utazási mozgársa váltás történik. Idő előtt megszüntetjük az extrudálást és a fejből még kicsöppenő anyagmaradványt használjuk fel a rész fennmaradó részének nyomtatásához. Így a szálazást, és az utazás közbeni csöppenést tudjuk csökkenteni."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Fésülés mód"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "A fésülés a fúvókát a már nyomtatott területeken tartja utazás közben.Ez kissé hosszabb utazási eredményeket eredményez, de csökkenti a visszahúzás szükségességét. Ha a fésülés ki van kapcsolva, akkor az anyag visszahúzódik, és a fúvóka egyenes vonalban mozog a következő pontra.Az is elkerülhető, hogy a felső / alsó kéregfelületeken fésülést végezzen, vagy csak a kitöltés belsejében mozogjon."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Parancssor beállításai"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr ""

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Körkörös"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Kúpszög"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Kúptámasz minimális szélesség"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Kitöltő vonalak csatlakozása"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Kitöltési sokszögek csatlakozása"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Támasz vonalak összekötése"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Cikcakk támasz összekötése"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Alsó/felső poligonok kapcsolása"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr ""

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Kösse össze a cikcakk támasz vonalait. Ez növeli a támasz szerkezeti erősségét."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Kösse össze a támaszvonalak végeit. Ha ez engedélyezve van, akkor a támaszok erősebbé válhatnak, csökkenthető az alulextrudálás, viszont ez több anyagba kerül."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Összeköti a kitöltő vonalakat ott, ahol a mintázat megfelelő egy olyan vonallal,ami a belső fal alakját követi. Így jobban fog kapcsolódni a kitöltés a falakhoz, és csükkenthető az a negatív hatás, hogy kitöltési vonalak torzítják a külső felületet. Ha ezt a beállítást nem használjuk, akkor csökken a felhasznált anyagmennyiség."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Az alsó/felső rétegpályákat kapcsolja össze, ahol egymás mellett futnak.Ha ezt a beállítást engedélyezzük a körkörös mintázatnál, jelentősen csökkenthetjük a fej átemelési időt, mivel a kapcsolódások félúton terténhetnek meg. Ez azonban ronthatja a felső felület minőségét."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Ellenőrzi, hogy a modell körvonalai mennyire befolyásolják a varrat helyzetét.Ha azt választjuk, hogy nincs, akkor a sarkok nincsenek hatással a varrás helyzetére.A varrat rejtés esetén a varrás legvalószínűbb helyzete, valamelyik belső sarokban lesz.A külső varrat esetén a megjelenés valószínűleg egy külső sarkon lesz.A külső/belső varrat esetén a varrat vagy külső, vagy belső sarokban lesz.Az okos rejtés esetén ugyanaz, mint a külső/belső varrat, de törekszik arra, hogy a varrat inkább a belső sarkokon legyen, rejtve."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Átkonvertálja az összes kitöltési sort, erre az értékre.Az így keletkező extra vonalak nem fogják egymást keresztezni, hanem elkerülik egymást. Ez növelni fogja a kitöltés erősségét, de a nyomtatási idő, és az anyagköltség is nőni fog."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Visszahűlési sebesség"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Hűtés"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Hűtés"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr ""

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Kereszt"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Kereszt"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "3D kereszt"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Kereszt 3D üreg méret"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Kereszt támasz kitöltési kép"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Kereszt kitöltési kép"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Kristályos anyag"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Kocka"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Osztott kocka"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Osztott kocka héj"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Háló vágás"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "A nyomtatószál adagolást (mm3/mp), és a hőmérsékletet (Celsius) összekötő adatok."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Alapértelmezett gyorsulás"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Alapértelmezett tárgyasztal hőmérséklet"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Alapértelmezett E löket"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Alapértelmezett nyomtatási hőmérséklet"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Alapértelmezett X-Y löket"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Alapértelmezett Z löket"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Alapértelmezett löket a vízszintes síkon történő mozgáskor."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Alapértelmezett löket a Z tengelyen."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Alapértelmezett extrudálási löket."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Érzékelje a hidakat, és módosítsa a nyomtatási sebességet, az adagolást és a ventilátorbeállításokat, a nyomtatásuk idejére."

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr ""

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr ""

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr ""

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr ""

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr ""

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr ""

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Átmérő"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr ""

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr ""

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Itt különböző lehetőségek közül választhatunk, amelyek elősegítik a nyomtatvány tárgyasztalhoz való tapadását. A peremek egyrétegű sík felületek, amik a modell alapja körül nyomtatódnak úgy, hogy megakadályozzák a deformációt.A tutaj egy vastag rácsot hoz létre egy fedéllel a modell alatt.A szoknya egy vonal, ami a modell körül van nyomtatva, de az a modellhez ne kapcsolódik."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Tiltott területek"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "A nyomtatott kitöltő vonalak távolsága. Ez egy számított érték, amit a kitöltési sűrűségből, és a kitöltő vonal szélességéből számol ki."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "A támaszok belső szerkezetében lévő vonalak távolsága az első rétegben.Ez egy számított érték a támasz sűrűségből."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "A támasz interfész aljzatvonalainak távolsága. Ezt a beállítást a támasz aljának a sűrűségét számítja ki, de külön is megadható."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "A támasz interfész tetővonalainak távolsága. Ezt a beállítást a támasz fedél sűrűségét számítja ki, de külön is megadható."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "A támaszok belső szerkezetében lévő vonalak távolsága.Ez egy számított érték a támasz sűrűségből."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr ""

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "A támasz teteje és a fölé épített nyomtatvány közötti távolság."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr ""

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "A kitöltési sor nyomtatása után egy extra törlőmozgást végez a fej.Ez a távolság határozza meg a törlési mozgás távolságát.Az opció hasonlít a szimpla kitöltés átfedéséhez, azonban itt a mozgás extrudálás nélkül történik, és csak a kitöltő sor egyik végén."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "A külső fal nyomtatása után, beilleszt egy fej átemelést, a meghatározott távolságra. Ez segít elrejteni a Z varratot."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "A modell és a huzatpajzs közötti távolság X/Y irányban."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "A pajzs távolsága a nyomtatványtól X/Y irányban értve."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr ""

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "A támasz szerkezete és a nyomtatvány közötti távolság X/Y irányban."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr ""

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Az ettől kisebb területekre nem generál kitöltést."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Huzatpajzs magasság"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Huzatpajzs korlátozás"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Huzatpajzs X/Y távolság"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Támaszháló ledobás"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Duál extrudálás"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr ""

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Elliptikus (kör)"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Gyorsulás vezérlés engedélyezés"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Híd beállítások engedélyezése"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Kifutás engedélyezés"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Kúpos támasz engedélyezése"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Huzatpajzs engedélyezése"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr ""

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Vasalás engedélyezés"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Löket vezérlés engedélyezése"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "A fúvóka hőmérséklet-szabályozásának engedélyezése"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Szivárgáspajzs engedélyezés"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Előnyomás engedélyezése"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Előtorony engedélyezése"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Tárgyhűtés engedélyezés"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr ""

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Visszahúzás engedélyezés"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Támasz perem engedélyezése"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Alsó interfész engedélyezés"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Támasz interfész engedélyezés"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Felső interfész engedélyezés"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr ""

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr ""

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Engedélyezi a szivárgáspajzsot. Ez létrehoz egy héjat a modell körül, úgy, hogy az nem ér a modellhez, azonban a fej visszaálláskor, az esetlegesen fúvókából kicsöppenő anyagmaradványokat 'letörli' ebben a héjban."

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr ""

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr ""

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr ""

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Engedéylezi a nyomtatófej X és Y tengelyen való löketének (sebesség) változásának vezérlését. Ha a löketet növeljük, az csökkenti a nyomtatási időt a minőség terhére."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "A nyomtatófej mozgási gyorsulás szabályzás engedélyezése. Ha növeljük a gyorsulást, csökken a nyomtatási idő, viszont a nyomtatás minősége is."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Engedélyezi a tárgyhűtést nyomtatás közben. A hűtés javíthatja a rétegek nyomtatási minőségét, főleg a kicsi rétegeknél, és az áthidaló, túlnyúló részeknél."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "Záró G-kód"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr ""

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr ""

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Kényszerítő Perem nyomtatás a tárgy körül, még azokon a helyeken is, ahol egyébként támaszt kellene nyomtatni. Ezeken a helyeken a támasz első rétege helyett a perem lesz nyomtatva."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr ""

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Mindenhol"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Kizáró"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Kísérleti funkciók"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Külső varrat"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Kiterjedt felfűzés"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "A kiterjedt felfűzés megkísérli felfűzni a nyílt lyukakat a hálóban úgy, hogy a lyukakat érintő poligonokat bezárja. Ez a funkció jelentősen növelheti a feldolgozási időt."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr ""

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Extra kitöltési falszám"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Extra felületi falszám"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Egy extra anyagmennyiség, amivel több anyagot tol vissza a fejbe fúvókaváltás után."

msgctxt "variant_name"
msgid "Extruder"
msgstr ""

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Extruder kezdő X helyzet"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Extruder kezdő Y helyzet"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Kezdő Z pozíció"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr ""

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr ""

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Adagolási visszahűlés sebesség kompenzáció"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr ""

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Hűtés sebesség"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Hűtés felülbírálás"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Ettől a hosszúságtól rövidebb részek körvonalait a Kis funkció sebességgel kerülnek kinyomtatásra."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr ""

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Az adagolókerék átmérője"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Befejező nyomtatási hőmérséklet"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Firmware visszahúzás"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Első réteg támasz extruder"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Áramlás"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr ""

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr ""

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr ""

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr ""

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Adagolás hőmérséklet diagram"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr ""

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Az első réteg áramlási kompenzációja: az eredeti rétegre extrudált anyag mennyiségét megszorozzuk ezzel az értékkel."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr ""

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr ""

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Áramláskompenzálás a kitöltés nyomtatásánál."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Áramláskompenzálás a támasz alsó/felső rétegének nyomtatásánál."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr ""

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Áramláskompenzálás a felső kéreg réteg nyomtatásánál."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Áramláskompenzáció az előtorony vonalakon."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Áramláskompenzálás a Szoknya/perem nyomtatásánál."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Áramláskompenzálás a támasz alsó rétegének nyomtatásánál."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Áramláskompenzálás a támasz felső rétegének nyomtatásánál."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Áramláskompenzálás a támasz nyomtatásánál."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr ""

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr ""

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Áramláskompenzálás a külső falvonalak nyomtatásánál."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Áramlási kompenzáció a felső felület legkülső falvonalán."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Áramlás kompenzáció a felső felület falvonalain az összes falvonal kivételével a legkülsőn."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Áramláskompenzálás az alsó/felső rétegek nyomtatásánál."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr ""

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Áramláskompnezáció minden falvonalon, kivéve a legkülsőbb falnál."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Áramláskompenzálás a fal vonalak nyomtatásánál."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Áramláskompenzáció: az extrudált anyag mennyiségét megszorozzuk ezzel az értékkel."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr ""

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr ""

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr ""

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr ""

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr ""

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr ""

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr ""

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Előre"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Balra előre"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Jobbra előre"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Teljes"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Rücskös felszín"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Rücsök sűrűség"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr ""

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Rücsök távolság"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Rücsök vastagság"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "G-kód típus"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""
"Olyan g-kód parancsok, amiket a nyomtatás legvégén kell végrehajtani \n"
" -al elválasztva."

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"Olyan g-kód parancsok, amiket a nyomtatás legelején kell végrehajtani \n"
" -al elválasztva."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr ""

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Állványzat magasság"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr ""

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Támaszték készítés"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Generáljon egy peremet az első rétegben a támaszok kitöltéseiben. Ezt a karimát a támaszok alá, és nem körülötte nyomtatják. Ennek a beállításnak a bekapcsolása növelhetjük a támaszok tapadását az tálcához."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Egy sűrű felületet generál a modell és a támasz közé. Ez egy héjat hoz létre a támasz tetején, amelyre a modell jól nyomtatódik, vagy az alján, ahová a támasz épülni fog."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Egy sűrű felület generálása a támasz aljára és a modell között. Ez egy átmeneti csatlakozási felületet fog teremteni a modell és a támasz közé."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Egy sűrű felület generálása a támasz teteje és a modell között. Ez egy átmeneti csatlakozási felületet fog teremteni a modell és a támasz közé."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Olyan szerkezeti részeket készít a nyomtatványhoz, ami segít alátámasztani azokat a részeket, amik a levegőben lógnak, vagy kinyúlnak a tárgyból.E nélkül ezeket a részeket nem lehet kinyomtatni, mivel nincs mire építenie az adott részt a nyomtatónak."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Üveg"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "A felső felületeken a fej mégegyszer átmegy, miközben egy nagyon kis mennyiségű alapanyagot extrudál. Ennek a célja az, hogy a tárgy teteje még jobban összeolvad, simábbá válik. A fúvóka kamrában a nyomás magasan van tartva, így a felszínen lévő gyűrődéseket anyaggal tölti fel."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Fokozatos kitöltési lépésmagasság"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Fokozatos kitöltési lépések"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Fokozatos támaszkitöltési lépésmagasság"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Fokozatos támasz kitöltési lépések"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr ""

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr ""

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr ""

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr ""

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Rács"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Rács"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Rács"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Rács"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Rács"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr ""

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Külső falak csoportosítása"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Van építési tér hőmérséklet szabályzás"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Van tárgyasztal fűtés"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Felfűtési sebesség"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Olvadókamra hossza"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "A huzatpajzs magassága. Csak eddig a magasságig fogja a pajzsot nyomtatni."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Varrat rejtés"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Külső, belső varrat"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr ""

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr ""

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Az ennél kisebb átmérőjű lyukakat és részek körvonalait a Kis funkciósebesség használatával nyomtatják ki."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Vízszintes kiegészítés"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Mennyire húzható ki a szál melegítés közben, szakadás nélkül."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Mennyire kell visszahúzni a szálat, hogy az anyagszivárgás leálljon."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr ""

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Mennyire kell visszahúzni a nyomtatószálat, hogy az tisztán megszakadjon."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Milyen gyorsan kell visszahúzni a szálat, mielőtt az megszakadna."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Milyen gyorsan kell visszahúzni a szálat, hogy meggátoljuk a szivárgást."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr ""

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr ""

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr ""

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Hány lépést kell a motornak megtenni ahhoz, hogy 1 mm mozgás történjen X irányban."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Hány lépést kell a motornak megtenni ahhoz, hogy 1 mm mozgás történjen Y irányban."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Hány lépést kell a motornak megtenni ahhoz, hogy 1 mm mozgás történjen Z irányban."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr ""

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr ""

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr ""

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr ""

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr ""

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr ""

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Ha a felületi régió területe kevesebb, mint ez a megadott százalékos érték, nyomtassa a híd beállításokkal, egyébként normál felületi beállításokkal nyomtasson."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr ""

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Ha engedélyezve van ez az opció, akkor a híd második és harmadik rétegét is a híd beállításával nyomtatja ki. Egyébként ezek a rétgeke már a normál beállítással nyomtatódnának ki."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr ""

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Ha az extra margót engedélyezzük, akkor a modell alá nyomtatott tutajt ki vogja egészíteni, és bővíteni kifelé irányban. Ez egy erősebb tutajt fog létrehozni, viszont több alapanyagot igényel, és csökkenti a használható nyomtatási területet."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr ""

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Figyelmen kívül hagyja a hálóban lévő, átfedő térfogatokból származó belső geometriai alakzatokat, és a szintet egyben nyomtatja ki. Ez a nem kívánt belső üregek eltűnését eredményezheti."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Tartalmazza a tárgyasztal hőmérsékleteket"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Tartalmazza az anyaghőmérsékleteket"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Befoglaló"

msgctxt "infill description"
msgid "Infill"
msgstr "Kitöltés"

msgctxt "infill label"
msgid "Infill"
msgstr "Kitöltés"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Kitöltés gyorsulás"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Kitöltés a falak előtt"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Kitöltési sűrűség"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Kitöltő extruder"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Kitöltési áramlás"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Kitöltés löket"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Kitöltő réteg vastagság"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Kitöltési vonal irányok"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Kitöltő vonal távolság"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Kitöltési sor szorzó"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Kitöltési vonalszélesség"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Kitöltés háló"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Kitöltés túlnyúlási szög"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Kitöltési átfedés"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Kitöltési átfedés aránya"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Kitöltési Minta"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Kitöltési sebesség"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Kitöltés támaszként"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Kitöltési utazás optimalizáció"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Kitöltés törlési távolság"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Kitöltés X eltolás"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Kitöltés Y eltolás"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr ""

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Kezdő hűtési sebesség"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Kezdő réteg gyorsulás"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr ""

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr ""

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Kezdő réteg áramlás"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Kezdő réteg magasság"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Kezdő réteg vízszintes kiegészítése"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr ""

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Kezdő réteg löket"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Kezdő réteg vonalszélesség"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr ""

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Kezdő réteg nyomtatási gyorsulás"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Kezdő réteg nyomtatási löket"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Kezdő réteg nyomtatási sebessége"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Kezdő réteg sebessége"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Kezdő réteg támasz vonal távolság"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Kezdő réteg utazási gyorsulás"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Kezdő réteg utazás löket"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Kezdő réteg utazási sebessége"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Első réteg Z átfedés"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Kezdeti nyomtatási hőmérséklet"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr ""

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Belső fal gyorsulás"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Belső fali extruder"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Belső fal löket"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Belső fal sebesség"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Belső fal)akÖ áramlása"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Belső fal(-ak) vonalszélessége"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Eltolás a külső fal útvonalára. Ha a külső fal kisebb, mint a fúvóka, és a belső falak után nyomtatódik, akkor ezt az eltolást használjuk, hogy a fúvóka furata a belső falakon nyúljon túl, a modell külseje helyett."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr ""

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr ""

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr ""

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr ""

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr ""

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr ""

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr ""

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr ""

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr ""

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr ""

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr ""

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Vasalás csak a legfelső rétegen"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Vasalási gyorsulás"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Vasalási adagolás"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Vasalás behúzás"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Vasalási löket"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Vasalási távolság"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Vasalási minta"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Vasalási sebesség"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Origó a középpontban"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr ""

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Az anyag olyan típusú-e, ami melegítve tiszta módon, kikristályosodva bomlik le, vagy olyan, ami nem kristályos, összefonódott polimer láncokat hoz létre?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr ""

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr ""

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Nyílt poligonok megtartása"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Réteg magasság"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Réteg X kezdőpont"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Réteg Y kezdőpont"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Az alap tutajréteg rétegvastagsága. Ennek vastag rétegnek kell lennie, mert erősen tapadnia kell a nyomtató tárgyasztalához."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "A középső tutajréteg rétegvastagsága."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Azoknak a tutajrétegeknek a vastagsága, ami a tutaj tetején van."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "A támaszvonalak között ennyi mm -ként hagy el egy vonalat, a könnyebb törhetőség miatt."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Balra"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Fej emelés"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr ""

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr ""

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr ""

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr ""

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr ""

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr ""

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr ""

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr ""

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr ""

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr ""

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr ""

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr ""

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr ""

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Korlátozza ennek a hálónak a térfogatát más hálókon belül. Ezt fel tudjuk használni egy háló nyomtatásának bizonyos területeire, különböző beállításokkal, vagy akát teljesen eltérő extruderrel."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Részleges"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Vonalvastagság"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr ""

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Vonalak"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Vonalas"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Vonalas"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Gép"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Nyomtatási mélység"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "A nyomtatófej és ventillátor ábrázolása"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Nyomtatási magasság"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Géptípus"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Nyomtatási szélesség"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Gép specifikus beállítások"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Nyomtatható túlnyúlások"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Egyesíti az egymással érintkező hálórészeket. Ez jobb kötést hoz létre a testben."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "A támaszok alja kisebb méretű lesz, mint az alátámasztandó rész."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Készítsen mindenütt támasztást a támaszháló alatt úgy, hogy ne lehessen alátámasztatlan kinyúlás a támaszhálóban."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "A nyomtatófej kezdeti pozíciója legyen abszolút, és ne a fej utolsó ismert helyzetéhez viszonyítson."

msgctxt "layer_0_z_overlap description"
msgid ""
"Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\n"
"It may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr ""

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr ""

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr ""

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Térfogat)"

msgctxt "material description"
msgid "Material"
msgstr "Alapanyag"

msgctxt "material label"
msgid "Material"
msgstr "Alapanyag"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr ""

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "Alapanyag GUID"

msgctxt "material_type label"
msgid "Material Type"
msgstr ""

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Anyagmennyiség törlések között"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Maximum fésű táv visszahúzás nélkül"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Maximális X gyorsulás"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Maximális Y gyorsulás"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Maximális Z gyorsulás"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr ""

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Maximális eltérés"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr ""

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Maximális hűtési sebesség"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Maximális E gyorsulás"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Maximális túlnyúlási szög"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr ""

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr ""

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Maximális felbontás"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Visszahúzások maximális száma"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "A kéreg bővítés maximális szöge"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr ""

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Maximum X sebesség"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Maximum Y sebesség"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Maximum Z sebesség"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Maximális toronnyal támasztott átmérő"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Maximális utazási felbontás"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr ""

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "Maximális gyorsulás az X tengelyen"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Maximális gyorsulás az Y tengelyen."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Maximális gyorsulás a Z tengelyen."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Maximális extrudálási gyorsulás."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr ""

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Annak a kis területnek a legnagyobb átmérője, amit speciális támasz toronnyalkell alátámasztani."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr ""

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Átfedések egyesítése"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Háló korrekciók"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Háló X pozíció"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Háló Y pozíció"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Háló Z pozíció"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr ""

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Háló elforgatás mátrix"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Középső"

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr ""

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Minimális formaszélesség"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Minimális készenléti hőmérséklet idő"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Minimális híd falhossz"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr ""

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Minimális extrudálási távolság ablak"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr ""

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Minimális sebesség"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr ""

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Minimális kitöltési terület"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Minimális rétegidő"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr ""

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr ""

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr ""

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Minimális sokszög kerület"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "A minimális kéregszélesség kibővítéshez"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Minimális sebesség"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Minimális támasz terület"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Minimális alsó interfész terület"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Minimális interfész terület"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Minimális felső interfész terület"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Minimális támasz X/Y távolság"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr ""

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Minimális mennyiség a kifutás előtt"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr ""

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "A támasz poligonok minimális területe. Ha ettől kisebb a terület, ottnem lesz támasz generálva."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr ""

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr ""

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr ""

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "A kúptámasz alapjának minimális mérete. Ha nagyon kicsi ez a szélesség, akkor a támasz instabil lehet."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Öntőforma"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Forma szög"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Forma fedél magasság"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr ""

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr ""

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr ""

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr ""

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr ""

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "A szoknyavonalak számának növelése, kisméretű tárgyak esetén segíthet az extruderben a megvelelő olvadókamra nyomás előállításában.Ha az érték 0, akkor a szoknya letiltódik."

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr ""

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Az első réteg vonalszélességének szorzója. Ennek a növelésével javíthatjuk a tapadást a tárgyasztalhoz."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr ""

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Felület nélküli Z hézag"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr ""

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Nincs"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr ""

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Nincs"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normál"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr ""

msgctxt "support_structure option normal"
msgid "Normal"
msgstr ""

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "A Cura általában megkísérli összefűzni a kis lyukakat a hálóban, és eltávolítja ezeket egy nagy üregben. Ennek az opciónak a bekapcsolásával megtarthatók azok a részek, amiket nem lehet felfűzni. Ezt a lehetőséget végső lehetőségként tartsuk fent, és csak akkor használjuk, ha nem tudjuk más módon létrehozni a megfelelő G-kódot."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Felszínen nem"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr ""

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Csúcsszög"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Fúvóka átmérő"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Fúvóka tiltott területek"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "Fúvóka ID"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Fúvókaváltási extra visszatolt anyag"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Fúvókaváltás visszatolási sebesség"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Fúvókaváltás visszahúzási sebesség"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Fúvókaváltás visszahúzási távolság"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Fúvókaváltás visszahúzási sebesség"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Extruderek száma"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Engedélyezett extruderek száma"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Lassabb rétegek száma"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Az engedélyezett extruder szerelvények száma. Ez egy automatikus beállítás a szoftverből"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Az extruder szerelvények száma. Az extruder szerelvény áll a továbbító egységből, a nyomtatófejből, és bowdenes gépeken a PTFE csőből."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "A törlési mozgás ismétlésének száma, háynszor keresztezze a fej a kefét."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Ez egy olyan lépésszám, ami azt határozza meg, hogy hányszor csökkenti a kitöltési sűrűséget a rétegek emelkedése során addig, amíg eléri a kitöltési sűrűség felét. Azokon a területeken, ahol a fedő rétegek közelébe kerül a kitöltés, a sűrűség újra növekedni fog."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Hány esetben csökkenti felére a támasz kitörlésének a sűrűségét a felére, ahogy építi a támaszt. Azokon a területeken, ahol a felső felületekhez közelebb kerül, ott a sűrűség nőni fog, egészen a támasz kitöltési sűrűségig."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Oktett"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Ki"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Az objektumra vonatkozó eltolás mértéke X irányban."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Az objektumra vonatkozó eltolás mértéke Y irányban."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Az objektumra alkalmazott eltolás z irányban. Ezzel végrehajthatja azt, amit régen 'Object Sink' -nek hívtak."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Extruder eltolás"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr ""

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr ""

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Egyesével"

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr ""

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Csak akkor végez Z emelést, ha olyan nyomtatott részek felett mozog, amiket vízszintes mozgással nem lehet elkerülni."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "A vasalást csak a legfelső rétegen végzi el. Ha az alacsonyabb szinteken lévőfelső felületeken nem szükséges a sima felület, akkor ezzel időt takaríthatunk meg."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Szivárgáspajzs szöge"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Szivárgáspajzs távolság"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr ""

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Optimalizálás a falnyomtatási sorrendre"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Optimalizálja a falak nyomtatásának sorrendjét, hogy csökkentse a visszahúzások számát és a megtett távolságot. A legtöbb alkatrész számára előnyös lehet ennek engedélyezése, de bizonyos esetekben valójában hosszabb is lehet.Ezért kérjük, hasonlítsa össze a nyomtatási idő becsléseit az optimalizálással és anélkül.Az első réteg nincs optimalizálva, ha a széleket építõlap-tapadási típusnak választják."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Külső fúvóka átmérő"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Külső fal gyorsulás"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr ""

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr ""

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Külső fali extruder"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Külső fal áramlás"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Külső fal eltolás"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Külsö fal löket"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Külső falvonal szélessége"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Külső fal sebesség"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr ""

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr ""

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr ""

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Külső fal tisztítási távolság"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Az azonos rétegben lévő különböző szigetek külső falait sorban nyomtatják. Amikor engedélyezve van, korlátozódik az áramlás változásainak mértéke, mert a falak típusonként nyomtathatók ki. Amikor letiltva van, az utazások számát a szigetek között csökkenti, mert ugyanazon szigeteken lévő falak csoportosítva vannak."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr ""

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr ""

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Falkinyúlások szöge"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr ""

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr ""

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Szünet a visszahúzás után."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "A hűtőventillátor sebességének százalékos értéke hídfalak, és a felszíni rétegek nyomtatásakor."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "A második hídréteg nyomtatásakor használt ventillátor sebesség százalékos értékben megadva."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "A ventillátorok % -os sebesség aránya, amit a támaszok feletti külső, kéregfelületeken kell használni. Ha a ventillátor sebesség itt nagyobb, akkor a támasz könnyebben eltávolítható."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "A harmadik hídréteg nyomtatásakor használt ventillátor sebesség százalékos értékben megadva."

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr ""

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "A szeletelt rétegekben lévő sokszögeket, amelyek kerülete kisebb, mint ez az összeg, kiszűrjük. Az alacsonyabb értékek magasabb felbontású hálóhoz vezetnek a szeletelési idő költségén. Elsősorban nagy felbontású SLA nyomtatókhoz és nagyon apró, sok részlettel rendelkező 3D modellekhez készült."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr ""

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr ""

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr ""

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Előtorony gyorsulás"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr ""

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr ""

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr ""

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr ""

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Elő torony áramlás"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Előtorony löket"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Előtorony vonalszélesség"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr ""

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr ""

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Előtorony minimális térfogat"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr ""

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Előtorony mérete"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Előtorony sebesség"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr ""

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Előtorony X helyzet"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Előtorony Y helyzet"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Nyomtatási gyorsulás"

msgctxt "variant_name"
msgid "Print Core"
msgstr ""

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Nyomtatás löket"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr ""

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Nyomtatási sorrend"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Nyomtatási sebesség"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Vékony falak nyomtatása"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr ""

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Nyomtasson egy tornyot a nyomtatandó tárgy mellett, amely abban segít, hogy a fejben lévő anyagváltást végre tudja hajtani. Kinyomtatja az előtoronyba a fejben marad előző alapanyag maradványokat, így már kitisztult fúvókával tudja a nyomtatást folytatni a nyomtatandó testen."

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Csak ott nyomtasson kitöltő szerkezeteket, ahol a felső modellrésznek szüksége van alátámasztásra. Ennek az engedélyezése csökkenti a nyomtatási időt, illetve az anyagszükségletet, azonban a tárgyak belső szilárdsága egyenetlen lehet."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Nyomtassa a modelt úgy, mint ha egy öntőforma lenne. Ezzel elérhetjük, hogy olyan nyomtatványt kapunk, amit  ha kiöntünk, akkor a tárgyasztalon lévő modelt kapjuk vissza."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Nyomtassa a modell egyes részeit vékonyabbra a vízszintes síkon, mint a fúvóka mérete."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr ""

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Ha használunk második hídréteget, akkor az ezzel a sebességgel fog nyomtatódni."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Ha használunk harmadik hídréteget, akkor az ezzel a sebességgel fog nyomtatódni."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr ""

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr ""

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Az adott rétegnél a kitöltést nyomtatjuk a falak nyomtatása előtt.A falak előzetes nyomtatása pontosabb falakat eredményezhet, azonban az átfedések nyomtatása gyengébb lehet. A kitöltés elsőnek nyomtatása szilárdabb falakhoz vezethet, de a feltöltési minta néha megjelenhet a felületen."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr ""

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Nyomtatási hőmérséklet"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Kezdő réteg nyomtatási hőmérséklete"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr ""

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Minden rétegben egy további extra falat nyomtat. Ez segít a kitöltésnek hozzáépülni a falhoz, ezáltal erősebb lesz a tárgy szerkezete."

msgctxt "resolution label"
msgid "Quality"
msgstr "Minőség"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Negyed kocka"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Tutaj"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Tutaj légrés"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr ""

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr ""

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Tutajalap hűtés"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr ""

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr ""

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr ""

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Tutajalap-vonalak közötti távolság"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Tutajalap vonal szélessége"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Tutajalap gyorsulás"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Tutajalap löket"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Tutajalap nyomtatási sebessége"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr ""

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Tutajalap vastagsága"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr ""

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Tutaj extra margó"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Tutaj hűtés"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr ""

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr ""

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr ""

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr ""

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr ""

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr ""

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr ""

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Tutajközép hűtés"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr ""

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Tutaj középső vonal szélessége"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Tutajközép gyorsulás"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Tutajközép löket"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Tutajközép nyomtatási sebesség"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr ""

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Tutaj középső távolsága"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Tutaj közép vastagsága"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr ""

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Tutaj gyorsulás"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Tutaj löket"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Tutaj nyomtatási sebesség"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Tutaj simítás"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr ""

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr ""

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr ""

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr ""

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr ""

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr ""

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Tutajfedél hűtés"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Felső tutaj rétegvastagság"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Felső tutaj rétegek"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Tutaj felső vonalszélesség"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Tutajfedél gyorsulás"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Tutajfedél löket"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Tutajfedél nyomtatási sebesség"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr ""

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Tutaj felső távolsága"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr ""

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr ""

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Véletlenszerű"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Véletlenszerű kitöltés kezdés"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Az adott rétegben, a kezdő kitöltési sorokat véletlenszerűen választja ki.Ezzel elkerülhető az, hogy a kitöltés az egyik helyen erősebb legyen, mint máshol, vagy éppen valahol gyengébb legyen a kelleténél a kitöltés."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "A külső fal nyomtatása során, véletlenszerűen beremeg a fej. Ennek hatására a külső fal mintázata elmosott, homályos lesz, elnyomja a mintázatot."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Szögletes (négyszög)"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Normál hűtési sebesség"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Normál hűtési magasság"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Normál hűtési réteg"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Normál/Maximum ventillátor sebesség küszöb"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Relatív extrudálás"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Üregek eltávolítása"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Üres első rétegek eltávolítása"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Keresztezések eltávolítása"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr ""

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr ""

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr ""

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr ""

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Azon területek eltávolítása, ahol a hálók keresztezik egymást. Ezt általában ott kell használni, ahol kettős objektumok átfedésben vannak egymással."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Távolítsa el az üres rétegeket amik az első nyomtatott réteg alatt, ha vannak. Ennek a beállításnak a letiltása esetén az első rétegek üresek lehetnek, ha a Szelet-tolerancia beállítást Kizárólagos vagy Közepes értékre állítják."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr ""

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr ""

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr ""

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr ""

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Eltávolítja a tárgyból az összes furatot, és üreget, s csak a külső geometriát tartja meg. Egyúttal figyelmen kívül hagyja a belső geometriát is.Nem fogja figyelmbe venni az alulról vagy felülről látható rétegeket sem."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Lecseréli az alsó/felső felületi minta legkülsőbb falait koncentrikus vonalra.Egy vagy két vonal használata javítja a felső záró felületeket, ott, ahol még a kitöltés látható."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr ""

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr ""

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr ""

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Visszahúzás külső fal előtt"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Visszahúzás a rétegváltásnál"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr ""

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "A nyomtatószálat visszahúzza, mikor a fúvóka átmozog egy nem-nyomtatott területen."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Visszahúzza a nyomtatószálat, amikor a fúvóka a következő rétegre vált."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Visszahúzási távolság"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Előtolási plussz anyagmennyiség"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Minimum út visszahúzáshoz"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Visszahúzás előtolási sebesség"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Visszahúzás visszahúzási sebesség"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Visszahúzási sebesség"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Jobbra"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr ""

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr ""

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr ""

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr ""

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr ""

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr ""

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Sarok varrat preferálás"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr ""

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Nyomtatási sorrend kézi beállítása"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Beállítja a huzatpajzs magasságát. Kiválasztható, hogy a modell teljes magasságában, vagy egy részleges magasságig épüljön a pajzs."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "A több extrúderekkel rendelkező gépek nyomtatási beállításai."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Beállítások, amelyeket csak akkor használunk, ha a CuraEngine nem hívható meg a Cura grafikus felületéről."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr ""

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Éles sarok"

msgctxt "shell description"
msgid "Shell"
msgstr "Héj"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Legrövidebb"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Mutasd a gép változatait"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr ""

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr ""

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Kéreg bővítési távolság"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Kéreg átfedés"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Felület átlapolás százaléka"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Kéreg eltávolítás szélessége"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Az ennél keskenyebb kéregfelületek nem bővülnek ki. Ezzel elkerülhető, hogy keskeny kéregfelületek kibővüljenek, amik akkor jönnek létre, mikor a modell külső felületének lejtése közel van a függőlegeshez."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Ennyi kapcsolódó vonal után hagy ki egyet a törés könnyítése érdekében."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "A támasz nyomtatásakor néhány támaszbonal összeköttetés kihagy, így a szerkezet a végső eltávolításkor, ott konnyebben el fog törni. Ezt a beállítást a cikcakk támaszmintára tudjuk alkalmazni."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Szoknya"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Szoknya távolság"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr ""

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Szoknya vonalszám"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Szoknya/perem gyorsulás"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr ""

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Szoknya/perem áramlás"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Szoknya/perem löket"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Szoknya/perem vonalszélesség"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Szoknya/Perem minimális hossz"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Szoknya/perem sebesség"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Szeletelési tűrés"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr ""

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Kis funkció maximális hossza"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Kis funkció sebesség"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Kis lyuk maximális mérete"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Befejező nyomtatási hőmérséklet"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr ""

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr ""

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr ""

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr ""

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr ""

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr ""

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Okos rejtés"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Sima, spirális kontúrok"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "A Z-varrat láthatóságának csökkentése érdekében simítsa meg a spirális kontúrokat (a Z-varratnak alig láthatónak kell lennie a nyomaton, de a rétegnézetben továbbra is látható lesz). Vegye figyelembe, hogy a simítás általában elmossa a finom felület részleteit."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Valamennyi anyagveszteség léphet fel a fej üresben mozgatása során, ami az anyag csöppenéséből adódhat. Ezt a hiányt tudjuk itt tudunk kompenzálni."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Valamennyi anyag elszivároghat a törlési művelet során, ami itt kompenzálható."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Különleges módok"

msgctxt "speed description"
msgid "Speed"
msgstr "Sebesség"

msgctxt "speed label"
msgid "Speed"
msgstr "Sebesség"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Az emelési mozgáskor a Z tengely sebessége."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Külső kontúr spiralizálása"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "A spirálizálás kiegyenlíti a külső él Z mozgását. Ez folyamatos Z növekedést eredményez a teljes nyomtatás során. Ez a szolgáltatás a szilárd modellt egyetlen falú, szilárd aljú nyomtatássá teszi. Ezt a funkciót csak akkor kell engedélyezni, ha minden réteg csak egyetlen részt tartalmaz."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Készenléti hőmérséklet"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "Induló g-kód"

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr ""

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Az egyes rétegekben az egyes útvonalak kiindulási pontja. Ha az egymást követő rétegek útvonalai ugyanabban a pontban kezdődnek, egy ún. függőleges varrat jelenik meg a nyomtatvány felületén. Ha a felhasználó ezt egy megadott helyhez igazítja, a varratot egyszerűbben el tudja távolítani.Van lehetőség arra is, hogy a kezdőpontok véletlenszerűen helyezkedjenek el, így azok kevésbé lesznek észrevehetők. Ha a legrövidebb utat választja, a nyomtatási folyamat gyorsabb lesz."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Lépés per milliméter (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Lépés per milliméter (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Lépés per milliméter (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Lépés per milliméter (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Támaszték"

msgctxt "support label"
msgid "Support"
msgstr "Támaszték"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Támaszték gyorsulás"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Támasz alsó távolság"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Támasz falak száma"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Támasz perem vonalak száma"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Támasz perem szélesség"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Törésvonalak száma"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Törés méret"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Támasz sűrűség"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Támasz távolság elsődlegesség"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Támasz extrúder"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Támasz alapzat gyorsulás"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Alsó interfész sűrűség"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Támasz fedél extruder"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Támasz alsó áramlás"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Interfészaljzat vízszintes bővítés"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Támasz alapzat löket"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Alsó interfész irány"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Alsó interfész vonal távolság"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Támasz padlóvonal szélesség"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Alsó interfész minta"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Támasz alapzat sebesség"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Alsó interfész vastagság"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Támasz áramlás"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Vízszintes támasz bővítés"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Támasz kitöltés gyorsulás"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr ""

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Támasz kitöltés extruder"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Támasz kitöltés löket"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Támasz kitöltési rétegvastagság"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Támasz kitöltés iránya"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Támasz kitöltési sebesség"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Támasz interfész gyorsulás"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Interfész sűrűség"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Támasz interfész extruder"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Támasz interfész áramlás"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Támasz interfész vízszintes bővítés"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Támasz interfész löket"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Támaszinterfész vonal irány"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Támasz interfész vonalszélesség"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Interfész minta"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr ""

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Támasz interfész sebesség"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Interfész vastagság"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Támasz falak száma"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Támasz löket"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Támasz kapcsolódási távolság"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Támasz vonal távolság"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Támasz vonalszélesség"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Támasz háló"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Támasz túlnyúlási szög"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Támasz minta"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Támasz elhelyezés"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Támasz fedél gyorsulás"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Felső interfész sűrűség"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Támasz alapzat extruder"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Támasz felső áramlás"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Interfészfedél vízszintes bővítés"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Támasz fedél löket"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Felső interfész irány"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Felső interfész vonal távolság"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Támasz tetővonal szélesség"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Felső interfész minta"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Támasz fedél sebesség"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Felső interfész vastagság"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Támasz falak száma"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Támasz sebesség"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Támasz lépcső magasság"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Támasz lépcső maximális szélesség"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr ""

msgctxt "support_structure label"
msgid "Support Structure"
msgstr ""

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Támasz felső távolság"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Támasz falak száma"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Támasz X/Y távolság"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Támasz Z távolság"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr ""

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr ""

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr ""

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Kéreghűtés sebesség támogatás"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Felület"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Felületi energia"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Felszín mód"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "A felület tapadási jellemzője."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Felületi energia."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr ""

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Bekapcsolja, hogy minden egyes rétegnél, ahol kereszteződő hálók találhatóak, azok fonódjanak össze. Ha kikapcsoljuk ezt az opciót, akkor a kereszteződő hálók közül az egyik megkapja az átfedésben lévő háló teljes térfogatát, míg a többi hálót eltávolítja."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr ""

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "Az az X koordináta, melynek a közelében található a rétegek X nyomtatási kezdőpontja."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "Az az X koordináta, ahol a rétegek nyomtatását kezdeni fogja."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "A fejnek az az X koordinátája, ahol a fúvóka előkészül ahhoz, hogy elkezdődjön a nyomtatás."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Az az Y koordináta, melynek a közelében található a rétegek Y nyomtatási kezdőpontja."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Az az Y koordináta, ahol a rétegek nyomtatását kezdeni fogja."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "A fejnek az az Y koordinátája, ahol a fúvóka előkészül ahhoz, hogy elkezdődjön a nyomtatás."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Az a Z koordináta pont, ahol a fej, illetve a fúvóka áll, a nyomtatási folyamat megkezdésekor."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Az a gyorsulási érték, amit az első réteg nyomtatása alatt használ."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "A legelső rétegnél használt gyorsulási érték."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "A kezdő réteg nyomtatása alatt, a fej utaztatásához használt gyorsulási érték."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Az utazási mozgás gyorsítása a kezdő rétegnél."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "A belső falak nyomtatása alatt használt gyorsulás."

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr ""

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "A kitöltés nyomtatása alatt használt gyorsulási érték."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "A vasalás közben használt gyorsulási érték."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "A nyomtatás közbeni gyorsulás."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "A tutajalap nyomtatásához kapcsolódó gyorsulási érték."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr ""

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr ""

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "A támaszok legalsó rétegeinek nyomtatása alatt használt gyorsulás.Ha alacsonyabb gyorsulást választ, akkor segíti a támasz tapadását a modellek tetején."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "A támaszok kitöltésének nyomtatása alatt használt gyorsulás."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "A tutaj középső rétegeinek nyomtatásához kapcsolódó gyorsulási érték."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "A külső falak nyomtatása alatt használt gyorsulás."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Az előtorony nyomtatása során használt gyorsulás."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "A tutaj nyomtatásához kapcsolódó gyorsulási érték."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "A támaszok legalsó és legfelső rétegeinek nyomtatása alatt használt gyorsulás.Ha alacsonyabb gyorsulási értéket használunk, az segít javítani a kinyúlások nyomtatási minőségén."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "A támaszok legfelső rétegeinek nyomtatása alatt használt gyorsulás.Ha alacsonyabb gyorsulási értéket használunk, az segít javítani a kinyúlások nyomtatási minőségén."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Az a gyorsulás, amellyel a szoknya és a perem nyomtatásra kerül. Általában ezt a kezdeti réteg gyorsulásával hajtják végre, de néha érdemes lehet kinyomtatni a szoknyát vagy a karimát más gyorsulással."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "A támaszok nyomtatása alatt használt gyorsulás."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "A tutajfedél nyomtatásához kapcsolódó gyorsulási érték."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "Az a gyorsulás, amellyel a felső felület belső falai kinyomtatnak."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "Az a gyorsulás, amellyel a felső felület legkülső falai kinyomtatnak."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "A falak nyomtatása alatt használt gyorsulás."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "A legfelső, záró felületi rétegek nyomtatása alatt használt gyorsulás."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Az alsó és felső rétegek nyomtatása alatt használt gyorsulás."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "A fej utaztatása során használt gyorsulás."

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr ""

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "A vasalás során használt adagolási mennyiség. Ez segít megtartani a nyomást az olvadókamrában, ami elősegíti a felületi hézagok kitöltését.A beállításkor figyelembe kell venni, hogy a nyomás ne legyen túl nagy, mivel ez túlzott anyagáramláshoz vezethet, és elmosódásokat hozhat létre a felületen."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Az átfedés százalékos mértéke a kitöltés és a falak között.Meghatározása a kitöltés vonalszélességének százalékában történik.Az enyhe átfedés lehetővé teszi, hogy a falak szorosan kapcsolódjanak a kitöltéshez."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr ""

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Az átfedés mértéke a kitöltés és a falak között. Az enyhe átfedés lehetővé teszi, hogy a falak szorosan kapcsolódjanak a kitöltéshez."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "A visszahúzás távolsága az extruderek váltásakor. 0-ra állítva egyáltalán nincs visszahúzás.Ennek a távolságnak általában meg kell egyeznie a hőzóna hosszával."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "A vízszintes sík és a kúpos rész közötti szög a fúvóka vége fölött."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "A torony fedél szöge. A magasabb érték hegyes tornytetőket eredményez.Az alacsonyabb szöggel laposabb fedelet készíthetünk a toronynak."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "A forma számára kialakított külső falak túlnyúlási szöge. 0 ° -kal a forma külső héja függőleges lesz, míg 90 ° -kal a modell külső része a modell kontúrját követi."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "Az ágak átmérőjének változási szöge. Az ágak felülről lefelé vastagodnak. Ha a szög 0, akkor az ágak átmérője egyenletes, teljes hosszukban.Egy kis szög érték növelheti a fa tartásának stabilitását."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "A kúpos támasz dőlésszöge. A 0 fok függőleges és 90 fok vízszintes. A kisebb szögek miatt a tartószerkezet erősebb, de több anyagból áll. A negatív szögek miatt a támasz talpa szélesebb, mint a teteje."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Az egyes poligonokon egy rétegben megjelenő pontok átlagos sűrűsége.Vegyük figyelembe, hogy az eredeti sokszög pontok itt eldobásra kerülnek, így az alacsony sűrűség csökkenti a felbontást."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Az egyes vonalszakaszokon létrehozott rücskök közötti átlagos távolság. Mivel az eredeti pontok eldobásra kerülnek, a sok rücsök a felbontás csökkenését fogja eredményezni. Ennek az értéknek meg kell haladnia a rücskös falvastagság felét."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr ""

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "A nyomtatófej mozgásának alapértelmezett gyorsulása."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "A nyomtatáshoz használt alapértelmezett hőmérséklet. Ez az alap hőmérséklete az adott alapanyagnak. Minden egyéb nyomtatási hőmérséklet eltérés ettől az alaptól kerül számításra"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "A fűthető tárgyasztal alapértelmezett hőmérséklete. Ez a hőmérséklet az alap, és ehhez viszonyítjuk a többi hőmérséklet értékét"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A híd felszínének a sűrűsége. A 100 -nál kisebb értéknöveli a hézagokat a felszíni vonalak között."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Beállítja a támasz interfész sűrűségét a támasz alsó felületein.A magasabb érték nagyobb tapadást tesz lehetővé a támasznak, a modell felületén, azonban a támaszt nehezebb lesz eltávolítani."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Beállítja a támasz interfész sűrűségét a támasz felső felületein.A magasabb érték jobb minőségű túlnyúlás nyomtatást tesz lehetővém viszont a támaszt nehezebb lesz eltávolítani."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A második hídréteg sűrűsége. A 100-nál kisebb értékek növelik a hézagokat a felszíni vonalak között."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A harmadik hídréteg sűrűsége. A 100-nál kisebb értékek növelik a hézagokat a felszíni vonalak között."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "A nyomtatási terület mélysége (Y-irány)."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "A speciális támasz torony átméröje."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "A támasz legvékonyabb ágainak átmérője. A vastagabb ágak erősebbek. Az alap felé eső ágak vastagabbak lesznek, mint ez a méret."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr ""

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "A nyomtatószál adagoló kerék átmérője az extruderben."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr ""

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "A következő réteg magasságának különbsége az előzőhöz képest."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr ""

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "A vasalási vonalak közötti távolság."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr ""

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr ""

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "A fúvóka és a már kinyomtatott részek közötti távolság, ha kerülő útvonalakat használunk."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Az tutajalap-réteg vonalai közötti távolság. A széles távolság megkönnyíti a tutaj eltávolítását a tárgyasztalról."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "A tutajvonalak közötti távolság a középső tutajrétegben. A középső távolságnak meglehetősen szélesnek kell lennie, ugyanakkor elég sűrűnek is ahhoz, hogy megfelelően támassza a felső tutajrétegeket."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "A tutajvonalak közötti távolság a felső tutajrétegeknél. A távolságnak meg kell egyeznie a vonalszélességgel, hogy a felület tömör legyen."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr ""

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr ""

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Az a szélesség, amilyen széles lesz a Perem, a nyomtatott tárgy szélétől számítva. A nagyobb perem nagyobb tapadást fog eredményeznim viszont csökkenti az effektív használható nyomtatási területet."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "Ez a távolság a fúvóka végétől mért távolság, ameddig a nyomtatószálat vissza szükséges húzni, ha nem használjuk az adott extrudert."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Az a távolság, ami a fúvóka csúcstól a még szilárd nyomtatószálig tart.Ez gyakorlatilag az esetek nagy részében a fúvóka teljes hossza, a csúcstól a torokig tart."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "Az alsó kéreg felületek a kitöltésig jönnek létre. Ha bővítjük az alsó kérget, és növeljük ezt az értéket, akkor jobb tapadást érhetünk el a kitöltéssel kapcsolatban, illetve a szomszédos falak jobban tudnak tapadni a kéreghez. Az alacsonyabb érték anyagmegtakarítást eredményez."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "A kéreg felületek a kitöltésig jönnek létre. Ha bővítjük a kérget, és növeljük ezt az értéket, akkor jobb tapadást érhetünk el a kitöltéssel kapcsolatban, illetve a szomszédos falak jobban tudnak tapadni a kéreghez. Az alacsonyabb érték anyagmegtakarítást eredményez."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "A felső kéreg felületek a kitöltésig jönnek létre. Ha bővítjük a felső kérget, és növeljük ezt az értéket, akkor jobb tapadást érhetünk el a kitöltéssel kapcsolatban, illetve a szomszédos falak jobban tudnak tapadni a kéreghez. Az alacsonyabb érték anyagmegtakarítást eredményez."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "A fej oda-vissza mozgatásának távolsága a kefén."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr ""

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Az a sebesség, amivel a fúvóka lehűl az extrudálás közben.Ugyanezt az értéket kell használni az extrudálás közbeni felmelegedésre is."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Az az extruder szerelvény, amivel az első réteg támasz kitöltését nyomtatjuk.Ezt multi-extruderes gépeken használhatjuk."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Az az extruder, ami a támaszok fedelét nyomtatja.Ezt multi-extruderes gépeken használhatjuk."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Az az extruder szerelvény, amivel a támasztékok kitöltését nyomtatjuk. Ezt multi-extruderes gépeken használhatjuk."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Az az extruder, ami a támaszok alját és tetejét nyomtatja.Ezt multi-extruderes gépeken használhatjuk."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Az az extruder, ami a támaszok alját nyomtatja.Ezt multi-extruderes gépeken használhatjuk."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr ""

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Az az extruder, amit a tapadásnövelő felületek, szoknya, perem, tutaj nyomtatására használunk. Csak multi extruder esetén használható."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Az az extruder szerelvény, amivel a támasztékokat nyomtatjuk. Ezt multi-extruderes gépeken használhatjuk."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr ""

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Az az extruder szerelvény, ami a kitöltést nyomtatja. Ez csak multi-extruderes nyomtatóknál használható."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr ""

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "Az az extruder, amit a belső falak nyomtatásához használunk.Ezt csak multi extruder esetén használhatjuk."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Az az extruder, amit a külső falak nyomtatásához használunk.Ezt csak multi extruder esetén használhatjuk."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Az az extruder, amit az alsó/felső felületi rétegek nyomtatásához használunk. Ezt csak multi extruder esetén használhatjuk."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "Az az extruder, amelyik a felső réteg külső lezárását végzi.Ez a funkció csak multiextruderes gépen érhető el."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Az az extruder, amit a falak nyomtatásához használunk.Ezt csak multi extruder esetén használhatjuk."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "A hűtőventillátor sebessége a tutajalap rétegeinek nyomtatásakor."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "A hűtőventillátor sebessége a tutaj középső rétegeinek nyomtatásakor."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "A hűtőventillátor sebessége a tutaj nyomtatásakor."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "A hűtőventillátor sebessége a tutaj felső rétegeinek nyomtatásakor."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "Annak a képfájlnak, aminek a fényerősség értékei meghatározzák a minimális sűrűséget a nyomtatás kereszt kitöltésének megfelelő helyén."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "Annak a képfájlnak, aminek a fényerősség értékei meghatározzák a minimális sűrűséget a nyomtatás kereszt támasz kitöltésének megfelelő helyén."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Az első pár réteget lassabban tudjuk nyomtatni, ha ezt a modell formája szükségessé teszi. Növelheti a tapadást a tárgyasztalhoz, így sikeresebb lehet a nyomtatás. A sebesség folyamatosan növekedni fog, ahogy emelkedik a rétegeken."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "A légrés megadja, hogy a tutaj teteje és a modell alja között milyen legyena távolság. Csak az első réteget fogja megemelni ez az érték, ami így csökkenteni fogja a tutaj és a test egymáshoz tapadását. Ezáltal könnyebb lesz a nyomtatás végén eltávolítani a tutajt."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "A nyomtatási terület magassága (Z-irány)."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "A modell vízszintes részeinek feletti magasság, amelyet formaként nyomtatunk."

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr ""

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Az a magasság, ahol a ventillátorok a normál hűtési sebességgel forognak.Az alacsonyabb rétegekben a hűtés még kissebb, és fokozatosan növekedik a sebessége a normál szintig, ahogy eléri ezt a magasságot."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "A fúvóka csúcsa és az állványzat közötti magasságkülönbség (A keresztező X és/vagy az Y tengely állványzata)"

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "Az a magasságkülönbség, amit a Z emeléskor emelkedik a tengely extruder váltás után."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Az a magasságkülönbség, amit a Z emeléskor emelkedik a tengely."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "A magasság, amivel a Z tenhelyt megemeljük."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Az egyes nyomtatási rétegek magassága mm -ben. A magasabb érték gyorsabb nyomtatást eredményez, viszont a minőség rosszabb lesz, mint az alacsonyabb réteg magasságnál. Azonban a kissebb rétegmagasság növeli a nyomtatási időt."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Az a kitöltési magasság, amit elérve a kitöltési sűrűség lefeleződik."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "A támaszkitöltés azon magassága, ahol a sűrűség feleződni fog."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr ""

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "A kezdő réteg magassága mm-ben. A vastagabb kezdőréteg megkönnyíti a tapadást a tárgyasztalhoz."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr ""

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "A támasz lépcsőinek magassága azona a részen, ahol a modellen támaszkodik.Ha az érték alacsony, a támasz eltávolítása nehéz lehet, viszont a túl magas érték instabillá teheti a támaszt. Ha az érték 0, akkor kikapcsolja a lépcsőt."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr ""

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"A szoknya vízszintes távolsága a modell első rétegének külső szélétől. \n"
"Ez a minimális távolság. Ha a szoknya vonalak száma többszörözve van, akkor a szoknya kifelé fog nyövekedni."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr ""

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "A kitöltési minta eltolása az X tengely mentén."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "A kitöltési minta eltolása az Y tengely mentén."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "A fúvóka belső átmérője. Akkor változtasd meg ezt az értéket, ha nem szabványos fúvóka méretet használsz."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "A tutajalap nyomtatásakor használt löket."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "A tutaj középső rétegeinek nyomtatásakor használt löket."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "A tutaj nyomtatásakor használt löket."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "A tutajfedél nyomtatásakor használt löket."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Az eltávolítandó alsókéreg felület legnagyobb szélessége. Az ettől kissebb felületek el fognak tűnni. Ez segíthet korlátozni a modell ferde felületeinek alsó részének nyomtatásához felhasznált időt és anyagot."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Az eltávolítandó kéreg felület legnagyobb szélessége. Az ettől kissebb felületek el fognak tűnni. Ez segíthet korlátozni a modell ferde felületeinek alsó és felső részének nyomtatásához felhasznált időt és anyagot."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Az eltávolítandó felső kéreg felület legnagyobb szélessége. Az ettől kissebb felületek el fognak tűnni. Ez segíthet korlátozni a modell ferde felületeinek felső részének nyomtatásához felhasznált időt és anyagot."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr ""

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Az a réteg, ahol a ventillátor eléri a normál hűtési sebességet.Ha a normál hűtési magasság be van állítva, akkor ezt a rétegszámot kiszámítja a szoftver."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Az a rétegidő, amely beállítja a küszöbértéket a szokásos ventilátor sebesség és a ventilátor maximális sebessége között. Az ezúttal lassabb nyomtatású rétegek szokásos ventilátorsebességet használnak. A gyorsabb rétegek esetén a ventilátor sebessége fokozatosan növekszik a maximális ventilátor sebesség felé."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "A visszahúzott anyag hossza visszahúzáskor."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr ""

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "A gépre szerelt tárgyasztal anyaga."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "Az alapréteg magasságától eltérő legnagyobb megengedett réteg magasság."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Az a maximális szög, ami a szivárgáspajzsban megjelenhet. A 0 fok a függőleges, a 90 fok pedig a vízszintesnek felel meg. Ha a szög kisebb, akkor jobb lehet a pajzs hatásfoka, és jobban mentesíti a fúvókát a szivárgó anyagtól, azonban több felhasználandó anyagot igényel az építése."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "A túlnyúlások maximális szöge a nyomtathatóvá tétel után. 0 ° értéknél az összes túlnyúlást egy, az építőlemezhez kapcsolt modelldarab váltja fel, a 90 ° -ot a modell semmilyen módon nem változtatja meg."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr ""

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr ""

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "A maximális eltérés, ha csökken a felbontás a maximális felbontás beállításnál. Ha ezt növeli, a nyomtatás kevésbé lesz pontos, de a g-kód kisebb lesz. A maximális eltérés a maximális felbontás korlátja, tehát ha a kettő ütközik, akkor a maximális eltérés lesz magasabb prioritású."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "A maximális X/Y távolság két támasz szerkezet között. Mikor két elszeparált támasz túl közel kerül egymáshoz, azaz ettől az értéktől közelebb, akkor a támaszok egyesülni fognak."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr ""

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr ""

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a kezdő réteg  nyomtatása alatt."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a nyomtatófej mozgására vonatkoztatva."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "A maximális löket, amivel megrántja a fejet vasalás közben."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a belső falak nyomtatása alatt."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr ""

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a kitöltés nyomtatása alatt."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr ""

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr ""

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a támaszok  alsó felületének nyomtatása alatt."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a támaszok kitöltésének nyomtatása alatt."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a külső falak nyomtatása alatt."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása az előtorony  nyomtatása alatt."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a támaszok  alsó és felső felületének nyomtatása alatt."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a támaszok  felső felületének nyomtatása alatt."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a szoknya és a  perem nyomtatása alatt."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a támaszok nyomtatása alatt."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "A legnagyobb pillanatnyi sebességváltozás, amellyel a felső felület legkülső falai kinyomtatnak."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "A legnagyobb pillanatnyi sebességváltozás, amellyel a felső felület belső falai kinyomtatnak."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a falak nyomtatása alatt."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a felső záró felület nyomtatása alatt."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "A maximális pillanatnyi sebességváltozás változtatása az alsó/felső felületek nyomtatása alatt."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a fej utazási  mozgása alatt."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr ""

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "Az X motor maximális sebessége."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Az Y motor maximális sebessége."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "A Z motor maximális sebessége."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "A nyomtatószál maximális adagolási sebessége."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "A modellen támaszkodó támasz lépcső maximális szélessége. Az alacsony érték nehezíti az eltávolítást, de a túl magas érték instabillá teszi a támaszt."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr ""

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "A nyomtatófej minimális mozgási sebessége."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "Az a minimális hőmérséklet, ameddig fel kell melegedni a fejnek a nyomtatás megkezdéséhez."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Az a minimális időtartam, ameddig a fúvóka inaktív lehet, mielőtt elkezdene visszahűlni. Így csak akkor fog a fúvóka visszahűlni a készenléti hőmérsékletre, ha hosszabb ideig nincs használva."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "Az a belső túlnyúlási szög, amihez szükséges kitöltést hozzáadni. Ha ez 0°, a test teljes mértékben kitöltésre kerül. Ha az érték 90°, akkor nem lesz semmiféle kitöltés."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "A kinyúlások minimális szöge, amihez támaszt kell nyomtatni.0° -nál minden kinyúlás alá lesz támasztva, 90° -nál egyik sem."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Az a minimális útvonal, amit a fejnek mozognia kell X-Y irányban ahhoz, hogy a visszahúzás megtörténjen. Ez segíthet abban, hogy ne legyen túl gyakori visszahűzás kisméretű területek felett."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "A szoknya, vagy a perem minimális hossza. Ha ezt a hosszt nem érné el az összes szoknya vagy perem, akkor további szegélyvonalak adódnak hozzá, és kiegészítik addig, amíg el nem érik ezt a hosszt.Ha a vonalszám 0 -ra van állítva, akkor ez az opció figyelmen kívül van hagyva."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr ""

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr ""

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Minimális sebesség. Ez az a minimum, amivel a fej mozoghat. Ez azért szükséges, mert a minimális rétegidő miatt előfordulhatna, hogy a nyomtatófej tűlzottan lelassul. Ez esetben a fúvóka olvadókamra nyomása leeshetne, ami ronthatná a nyomtatási minőséget."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "Egy vonalszakasz minimális mérete a szeletelés után. Ha ezt megnöveli, a háló kisebb felbontású lesz. Ez lehetővé teheti a nyomtató számára, hogy lépést tartson a g-kód feldolgozásának sebességével, és növeli a szeletek sebességét azáltal, hogy eltávolítja a háló azon részleteit, amelyeket egyébként nem tud feldolgozni."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "Az utazási útvonalak minimális mérete szeletelés után. Ha ezt növeljük, akkor az utazási mozgások kevésbé rendelkeznek majd sima sarkokkal.Ez lehetővé teszi a nyomtatók számára, hogy lépést tartsanak a g-kód feldolgozásához szükséges sebességgel, azonban a modell elkerülése kevésbé lehet pontos."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr ""

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr ""

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr ""

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Egy adott réteg nyomtatásában eltöltött idő. Ha a réteg nagyon kicsi, akkor ez arra készteti a nyomtatót, hogy lelassuljon annyira, hogy a réteg nyomtatási ideje ezt az időtartamot elérje. Ez azért szükséges, hogy az adott réteg le tudjon hűlni annyira, hogy a következő réteg megfelelően tudjon ráépülni. A réteg nyomtatási ideje lehet ettől az értéktől rövidebb, ha a fejemelés le van tiltva, vagy ha a minimális sebesség ettől eltérő értéket ad meg."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Az előtorony minimális térfogata, minden egyes rétegben ahhoz, hogy az anyagcserét teljes egészében végre tudja hajtani."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr ""

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "A 3D nyomtatód neve."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "Az extruder szerelvény fúvóka azonosítója, például \"AA 0.4\" és \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "A fej mozgatásakor a már kinyomtatott részeket elkerülő útvonalon fog haladni.Ez az opció csak akkor érhető el, ha a fésű mód engedélyezve van."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "A fej mozgatásakor a kinyomtatott támaszokat elkerülő útvonalon fog haladni.Ez az opció csak akkor érhető el, ha a fésű mód engedélyezve van."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Az alsó rétegek száma. Az alsó vastagság alapján számítva ezt az értéket egész számra kerekíti."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr ""

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr ""

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr ""

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr ""

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr ""

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr ""

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr ""

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr ""

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "A Perem körvonalainak száma. Több vonal nagyobb tapadást eredményez, de csökkenti a használható nyomtatási területet."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "A támasz alá nyomtatandó peremvonalak száma. Több perem vonal javítja a tálcához való tapadást, viszon extra anyagfelhasználást is jelent."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr ""

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Ez a szám a tutaj felső rétegeinek száma. Ezek teljesen kitöltött rétegek amiken a modellek nyugszanak. 2 réteg használata sokkal simább első réteget fog eredményezni a modellen, mint ha 1 lenne."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "A felső rétegek száma. A felső vastagság alapján számítva ezt az értéket egész számra kerekíti."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "A felső felszíni zárórétegek száma. Általában egy felső réteg is elegendő a jó minőségű felső felületek előállításához."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Azoknak a falaknak a száma, amellyel a támogatást körül lehet venni. A fal hozzáadása megbízhatóbbá teszi a nyomtatást és jobban támaszthatja a túlnyúlásokat, de növeli a nyomtatási időt és a felhasznált anyagot."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Azoknak a falaknak a száma, amellyel a támogatást körül lehet venni. A fal hozzáadása megbízhatóbbá teszi a nyomtatást és jobban támaszthatja a túlnyúlásokat, de növeli a nyomtatási időt és a felhasznált anyagot."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Azoknak a falaknak a száma, amellyel a támogatást körül lehet venni. A fal hozzáadása megbízhatóbbá teszi a nyomtatást és jobban támaszthatja a túlnyúlásokat, de növeli a nyomtatási időt és a felhasznált anyagot."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Azoknak a falaknak a száma, amellyel a támogatást körül lehet venni. A fal hozzáadása megbízhatóbbá teszi a nyomtatást és jobban támaszthatja a túlnyúlásokat, de növeli a nyomtatási időt és a felhasznált anyagot."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr ""

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "A nyomtatandó falak száma. A falvastagság alapján számított és kerekített érték."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "A fúvóka hegyének külső átmérője."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr ""

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr ""

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "A támaszok szerkezeteinek mintázata. A különböző mintákkal elérhető eredmény lehet az erős vagy a könnyen eltávolítható támasz."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "A legfelső rétegeken lévő mintázat."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Az alsó/felső rétegek mintázata."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "A legalsóbb, kezdő réteg mintázata."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "A felső felületek vasalásához használt minta."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "A támasz interfész alsó felületének kialakítási mintája."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "A támasz interfész kialakítási mintája."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "A támasz interfész felső felületének kialakítási mintája."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "Az a pont, ahol az egyes rétegek nyomtatását kezdeni fogja."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr ""

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr ""

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "A maximális pillanatnyi sebességváltozás változtatása a kezdő rétegen."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr ""

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "A tárgyasztal alakja anélkül, hogy a ténylegesen nem használható területeket figyelembe vennénk."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Azoknak a kialakuló üregeknek a mérete, amik akkor jönnek létre, mikor a kereszt 3D mintában egy adott magasságnál a minta önmagát érinti."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Az minimális extrudálási mennyiség, mielőtt engedélyezi a kifutási műveletet.Ha nincs egy bizonyos mennyiségű extrudálás a kifuttatás előtt, nem épül fel elegendő nyomás az olvadótérben, és a kifutás során nem lesz elegendő anyag a nyomtatáshoz. Emiatt a kifutási mérték lineárisan van skálázva.Ez az érték mindig nagyobb, mint a kifutási mérték."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Az az átlagolt sebesség, (°C/mp) amivel a nyomtatási és a készenléti hőmérséklet között a fúvóka visszahűl."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Az az átlagolt sebesség, (°C/mp) amivel a készenléti és a nyomtatási hőmérséklet között a fúvóka melegszik."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Az a sebesség, amivel a belső falak nyomatásra kerülnek.A belső falak nyomtatási sebességének növelése csökkenti a nyomtatási időt.A javasolt sebességnek a külső falak, és a kitöltés nyomtatási sebessége közötti értékeket adjunk meg."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr ""

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "A hidak felszíni rétegeinek nyomtatási sebessége."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Az a sebesség, amivel a kitöltés nyomtatódik."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Az a sebesség, amivel a nyomtatás történik."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "A tutajalap nyomtatási sebessége. Ezt meglehetősen lassan kell nyomtatni, mivel a fúvókából kifolyó anyag mennyisége meglehetősen nagy."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr ""

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr ""

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Hidak falainak nyomtatási sebessége."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Az a sebesség, amellyel a ventilátorok forognak a nyomtatás kezdetén. Az ezt követő rétegekben a ventilátor sebességét fokozatosan növeli olyan szintre, amely megegyezik a normál ventilátor sebességgel."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Az a sebesség, amivel a hűtés történik normál nyomtatási esetben.Ha egy réteg nyomtatási sebessége egy küszöbérték felé emelkedik, akkor a hűtést növelni fogja egészen addig, amíg a maximumra nem emelkedik."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Az a hűtési sebesség, amivel a ventillátorok forognak a maximális hűtéskor.A maximális hűtés azoknál a rétegeknél történik, ahol elértük a minimális rétegídőt. A hűtés a normál és a maximum érték között tud változni."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "A nyomtatószál visszahúzás sebessége szál előtolási mozgáskor."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "Az a sebesség, amivel a nyomtatószál visszatöltődik a fejbe."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Az a sebesség, amivel a nyomtatószálat visszatoljuk a fejbe fúvókaváltás után."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "A nyomtatószál sebessége visszahúzáskor, és előtoláskor."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "Az a sebesség, amivel a nyomtatószál visszahúzódik, majd vissza töltődik a fejbe, a törlési művelet során."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Az a sebesség, amivel a szál visszahúzásra kerül a fúvókaváltás során."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Az a sebesség, amellyel a nyomtatószál visszahúzódik egy visszahúzási mozgás során."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "Az a sebesség, amivel a nyomtatószál visszahúzódik."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Nyomtatószál visszahúzási sebesség. A nagyobb sebesség jobb eredményhez vezethet, azonban a túl nagy sebesség a nyomtatószál eldarálásához vezethet a nyomtatószál adagolóban."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "A támasztékok alsó, kezdő rétegének nyomtatási sebessége. Ha ez a sebesség lassabb, akkor jobb lehet a támasz tapadása a tárgyasztalra."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "A támasztékok kitöltésének nyomatatási sebessége. Ha a kitöltések nyomtatási sebességét csökkentjük, a támaszték stabilabb lesz."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "A középső tutajrétegek nyomtatási sebessége. Ezt meglehetősen lassan kell nyomtatni, mivel a fúvókából kifolyó anyag mennyisége meglehetősen nagy."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "Az a sebesség, amivel a legkülsőbb falak nyomtatásra kerülnek.Az alacsonyabb sebesség javítja a külső felület végső minőségét, azonban, ha túl nagy a különbség a külső és a belső falak nyomtatási sebessége között, az negatív hatással lehet a minőségre."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "Az a asebesség, amivel a előtorony nyomtatódik. Nyomtassuk a előtornyot alacsony sebességen, mert ez segíthet a különböző anyagrétegeknek az egymáshoz tapadásában."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Az a sebesség, amivel a ventillátorok pörögnek."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "A tutaj nyomtatási sebessége."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Az a sebesség, amivel a támasztékok alsó és felső felületét nyomtatjuk.Ha ez a sebesség lassabb, akkor jobb lehet a kinyúlás minősége."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "A támasztékok fedő, felső rétegének nyomtatási sebessége. Ha ez a sebesség lassabb, akkor jobb lehet a kinyúlás minősége."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "A szoknya és a perem nyomtatási sebessége. Alapesetben ez ugyanannyi, mint a kezdő réteg sebessége, de néha szükséges lehet a különböző sebességgel való nyomtatásra."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "A támasztékok nyomattási sebessége. A támaszok nyomtatási sebességét bátran növelhetjük, mivel nem számít, milyen lesz a felületi minőségük.A nyomtatás után el lesznek távolítva, nem részei a nyomtatott tárgynak, és a nagyobb sebesség csökkentheti a nyomtatási időt."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "A tutaj felső rétegeinek nyomtatási sebessége. Ezeket kissé lassabban kell nyomtatni, hogy a fúvóka lassan kiegyenlítse a szomszédos felszíni vonalakat."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "Az a sebesség, amellyel a felső felület belső falai kinyomtatnak."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "Az a sebesség, amellyel a felső felület legkülső falai kinyomtatnak."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "A Z tengely emelési sebessége. Ez általában alaxcsonyabb, mint a nyomtatási sebesség, mivel a tárgyasztal, vagy az X keresztszánt nehezebb mozgatni."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "A falak nyomtatási sebessége."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "A felső felületen való áthaladási sebesség."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "Milyen gyorsan kell visszahúzni a nyomtatószálat, hogy az tisztán megszakadjon."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "A felső záró kéreg felületi rétegnek a nyomtatási sebessége."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Az alsó/felső réteg nyomtatási sebessége."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "A fej üresben történő pozícióváltásának sebessége."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "A kifutási művelet sebessége, a normál extrudálási út sebességéhez képest.Javasolt, hogy kisség csökkentsük, így 100% -nál alacsonyabb legyen az érték, mivel a kifutási mozgás közben csökken a nyomás a csőben."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr ""

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "A kezdő réteg nyomtatási sebessége. Az alacsonyabb érték segít növelni a tapadást a tárgyasztalhoz."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "A kezdő réteg utazási sebessége. Az Alacsonyabb érték javasolt, mivel a korábban már kinyomtatott részeleteket feltépheti a nyomtatófej a tárgyasztalról.A beállításnak az értéke kiszámítható, az utazási és a nyomatási sebesség arányából."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "Az a hőmérséklet, ahol a nyomtatószál tisztán meg tud szakadni."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "A nyomtató építési tér hőmérséklete. Ha ez az érték 0, akkor a gép nem képes az építési tér hőmérséklet szabályzására."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Az adott fúvóka hőmérséklete, amikor éppen egy másik fúvókát használnak nyomtatáshoz."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "Az a hőmérséklet, ahová a fejnek vissza kell hűlnie a nyomtatás befejezése előtt."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr ""

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "A nyomtatáshoz használt hőmérséklet."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr ""

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr ""

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr ""

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Az alsó rétegek vastagsága a nyomtatáskor. Ez az érték osztva a rétegmagassággal adja meg az alsó rétegek számát."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr ""

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "A támasz átmeneti, a modellt érintő csatlakozó felületének a vastagsága, amit a támasz aljára, vagy a tetejére nyomtat."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "A támasz átmeneti, a modellt érintő csatlakozó felületének a vastagsága, amit a támasz aljára nyomtat."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "A támasz átmeneti, a modellt érintő csatlakozó felületének a vastagsága, amit a támasz tetejére nyomtat."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "A felső rétegek vastagsága a nyomtatáskor. Ez az érték osztva a rétegmagassággal adja meg az felső rétegek számát."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Az alsó/felső rétegek vastagsága a nyomtatáskor. Ez az érték osztva a rétegmagassággal adja meg az alsó/felső rétegek számát."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "A falak vastagsága vízszintes irányban. Ez az érték osztva a falvonal szélességével határozza meg a falak számát."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "A kitöltési réteg vastagsága. Ez az érték minden esetben a normál rétegvastagság,vagy annak a többszöröse és kerekített értéke."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "A támasz kitöltés retegenkénti anyagvastagsága. Ennek az értéknek minden esetben a rétegmagasság egész többszörösének kell lennie, és oda lesz kerekítve."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "A létrehozandó g-kód típusa."

msgctxt "material_type description"
msgid "The type of material used."
msgstr ""

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "A csöppenés mértéke. Ennek az értéknek általában közel kell lennie a fúvóka átmérőjéhez."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "A nyomtatási terület szélessége (X-irány)."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "A támasz alá nyomtatandó perem szélessége. A nagyobb peremek javítják a tálcához való tapadást, viszon extra anyagfelhasználást is jelent."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "Az előtorony szélessége."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr ""

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "Az előtorony szélessége."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Az a szélesség, amelyen belül a rezgés történhet. Javasolt, hogy ez a külső fal szélessége alatt legyen, mivel a belső falalkat nem érinti ez a mozgás."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Az az ablak, amelyben érvényesülni tud a maximális visszahűzási szám.Ennek az értéknek megközelítőleg azonosnak kell lennie a visszahúzási távolsággal, s így lehet korlátozni a visszahúzások számát ugyanabban az anyaghelyzetben."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Az előtorony nyomtatási X koordinátája."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Az előtorony nyomtatási Y koordinátája."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr ""

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Ez határozza meg, hogy a fejnek a szélekhez képest mennyi a távolsága a hídfal megkezdése előtt. A híd nyomtatásának megkezdése előtt az olvadókamra nyomást csökkentheti, ami így vízszintesebb hídhoz vezethez."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr ""

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr ""

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr ""

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr ""

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr ""

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Ez a beállítás azt szabályozza, hogy tutajnak hány belső sarka legyen lekerekítve. A belső sarkokat félkörre kerekíti le, aminek sugara az itt megadott érték. Ez a beállítás eltávolítja a tutaj körvonalában lévő olyan lyukakat is, amik kisebbek, mint egy ilyen kör."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr ""

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr ""

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Ez a beállítás korlátozza a minimális extrudálási távolság ablakon belüli visszahúzódások számát. Az ablakon belüli további visszahúzódásokat figyelmen kívül hagyjuk. Ezzel elkerülhető, hogy ugyanazon a szálpozicióban többször visszahúzódjon, mivel ez a nyomtatószálat ellapíthatja, eldarálhatja az extruder adagolókeréknél és ez elakadási problémákat okozhat."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "A beállítással létrehozhatunk egy falat a modell körül, ami segít abban, hogy a külső levegő, vagy légáramlat érje a nyomtatott testet.Ez különösen azoknál az alapanyagoknál lehet segítség, amelyek hajlamosak a felválásra, repedésre, mint pl. az ABS, ASA."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr ""

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr ""

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr ""

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr ""

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Felső rétegek"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Felső kéreg bővítési távolság"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Felső kéreg eltávolítási szélesség"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "A felső felület belső falának gyorsulása"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "A felső felület legkülső falának hirtelen gyorsulása"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "A felső felület belső falának sebessége"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "A felső felület belső falainak áramlása"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "A felső felület külső falának gyorsulása"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "A felső felület legkülső falának áramlása"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "A felső felület belső falának hirtelen gyorsulása"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "A felső felület legkülső falának sebessége"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Felső felületi gyorsulás"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Felső és külső felületi extruder"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Felső kéregfelület áramlás"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Felső felület löket"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Felső kéreg rétegek"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Felső kéregvonal irányok"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Felső felszíni kéregvonal szélesség"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Felső felszíni kéregminta"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Felső záró felületi sebesség"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Felső vastagság"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr ""

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr ""

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr ""

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Alsó/felső gyorsulás"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Alsó/felső extruder"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Alsó/felső áramlás"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Alsó/felső löket"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Alsó/felső vonal irányok"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Alsó/felső vonalszélessége"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Alsó/felső mintázat"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Alsó/felső sebesség"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Alsó/felső vastagság"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Asztalt érintse"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Torony átmérő"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Torony fedél szög"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "A modellre alkalmazandó átalakítási mátrix, amikor azt fájlból tölti be."

msgctxt "travel label"
msgid "Travel"
msgstr "Utazás"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Utaztatási gyorsulás"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Elkerülő utazási távolság"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Utazás löket"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Utazási sebesség"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "A modellt csak felületként, térfogatként, vagy lazua felülettel kezelje.A normál nyomtatási mód csak a zárt szinteket nyomtatja ki. A \"Felület\" egyetlen falra nyomtat, amely a háló felületét követi nyomtatás nélkül, és nincs alsó és felső felület.A \"Mindkettő\" zárt szinteket nyomtat, ugyanúgy, mint a normál, és minden fennmaradó poligont pedig felületként."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr ""

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Három-hatszög"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Háromszög"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Háromszögek"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Háromszögek"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Háromszög"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Háromszögek"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr ""

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr ""

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr ""

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr ""

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Szintátfedések egyesítése"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Az ennél rövidebb nem alátámasztott falak a normál falbeállításokkal kerülnek kinyomtatásra. Az ettől hosszabb nem támogatott falakat viszont a hídfalak beállításai alapján nyomtatjuk ki."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Adaptív rétegek használata"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Tornyok használata"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr ""

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr ""

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Használjon relatív extrudálást abszolút extrudálás helyett. A relatív E-lépések használata megkönnyíti a g-kód utófeldolgozását. Ezt azonban nem minden nyomtató támogatja, és az abszolút E-lépésekhez viszonyítva nagyon kismértékű eltéréseket eredményezhet a lerakott anyag mennyiségében. Ettől a beállítástól függetlenül az extrudálás módját mindig abszolút értékre állítják, mielőtt bármilyen g-kód szkriptet kiadnának."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Speciális támasz tornyok használata a kisméretű túlnyúló területek támogatásához.Ezeknek a tornyoknak az átmérője nagyobb, mint az alátámasztott terület, azonban az alátámasztandó terület közelébe érve, fokozatosan csökken az átmérőjük, és egy tetőt képeznek."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Ezzel a hálóval módosíthatja az egyéb átfedéseknek megfelelő kitöltéseket. Kicseréli a többi háló feltöltési régióit ezekre a régiókra. Javasoljuk, hogy ehhez a hálóhoz csak egy falat nyomtasson, és ne használjon alsó/felső felületet."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Ezzel a hálóval határozhatja meg a támaszt területeket. Ez felhasználható a támasz struktúra létrehozására."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Ezzel a hálóval megadhatjuk, hogy a modell melyik részét ne lehessen észlelni túlnyúlásként. Ezt felhasználhatjuk arra, hogy a nem kívánt támasz struktúrákat eltávolítsuk."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Felhasználói megadás"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr ""

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr ""

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Várakozás a tárgyasztal felfűtésére"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Várakozás a fej felfűtésére"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Fal gyorsulás"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr ""

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Fali extruder"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Fal áramlás"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Fal löket"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Falvonalak száma"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Fali vonal szélessége"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr ""

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Fal sebesség"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Falvastagság"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr ""

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr ""

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr ""

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr ""

msgctxt "shell label"
msgid "Walls"
msgstr ""

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr ""

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr ""

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr ""

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr ""

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Ha engedélyezve van, a kitöltési sorok nyomtatási sorrendje optimalizálódik a megtett távolság csökkentése érdekében. Az elért utazási idő csökkentése nagymértékben függ a szeletelt modelltől, a kitöltési mintától, a sűrűségtől stb. Vegye figyelembe, hogy egyes modellek esetében, amelyeknek sok kis kitöltési területe van, a modell szeletelésének ideje jelentősen megnőhet."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Ha engedélyezzük ezt az opciót, akkor a hűtőventillátor sebessége meg fog változni a külső kéreg felületeken, közvetlenül a támasz felett."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Ha engedélyezve van, a z varrás koordinátái az egyes alkatrészek középpontjához viszonyítva vannak. Letiltva a koordináták meghatározzák az abszolút helyzetét a tárgyasztalon."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr ""

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr ""

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr ""

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "A hídfelszínek nyomtatásakor az extrudált anyagmennyiséget meg kell szorozni ezzel az értékkel."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "A hídfalak nyomtatásakor az extrudált anyag mennyiségét meg kell szorozni ezzel az értékkel."

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr ""

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr ""

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "A második hídréteg nyomtatásakor az extrudált anyag mennyiségét megszorozzuk ezzel az értékkel."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "A harmadik hídréteg nyomtatásakor az extrudált anyag mennyiségét megszorozzuk ezzel az értékkel."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Ha a nyomtató elérte a megadott minimális rétegidővel a minimális sebességet, és nem tud tovább lassulni, akkor a nyomtató emelje fel a fejet addig, amig amíg el nem éri a minimális rétegidőt."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Ha a modellnek csak néhány rétegű függőleges rése van, akkor ebben a keskeny térben a rétegek körül általában felületnek kell lennie.Akkor engedélyezze ezt a beállítást, ha nem szeretné, hogy keletkezzen itt felület, és ha a függőleges rés nagyon kicsi itt.Ez javítja a nyomtatási és a szeletelési időt, de technikailag a töltőanyagot a levegőnek teszi ki, azaz a belső kitöltés itt nyitott fog maradni."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr ""

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr ""

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr ""

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr ""

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Szálvisszahúzáskor a Z tengely megemelkedik, így elemeli a fejet a tárgytól, ami megakadályozza, hogy a fúvóka hozzáérjen a már kinyomtatott részekhez utazáskor. Ez csökkenti annak az esélyét, hogy a fej lelökje, vagy felszakítsa a tárgyat a tárgyasztalról."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Függetlenítés attól, hogy az X/Y támaszték távolsága felülbírálja-e a Z támasz távolságát, vagy fordítva. Amikor az X/Y felülbírálja a Z-t, az X/Y távolság elmozdíthatja a támaszt a modelltől, befolyásolva a tényleges Z távolságot a nyomtatványig. Ezt letilthatjuk, ha nem alkalmazzuk az X/Y távolságot a túlnyúlások körül."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Ez a beállítás, az X és Y nullpontot a nyomtatási terület középpontjába helyezi."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Azt határozza meg, hogy a végállás kapcsoló pozitív irányban van-e, vagy negatívban. (pozitív a magasabb X, negatív az alacsonyabb X koordinátát jelenti)"

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Azt határozza meg, hogy a végállás kapcsoló pozitív irányban van-e, vagy negatívban. (pozitív a magasabb Y, negatív az alacsonyabb Y koordinátát jelenti)"

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Azt határozza meg, hogy a végállás kapcsoló pozitív irányban van-e, vagy negatívban. (pozitív a magasabb Z, negatív az alacsonyabb Z koordinátát jelenti)"

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr ""

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr ""

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Azt határozza meg, hogy van -e a gépen fűthető tárgyasztal. Ha ez az opció ki van kapcsolva, nem lehet belkapcsolni a tárgyasztal fűtését."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Azt határozza meg, hogy a gép képes-e szabályozni a nyomtatási tér hőmérsékletét."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "A tárgyat a tárgyasztal közepére kell központosítani (0,0), annak a koordináta rendszernek a használata helyett, amelyben az objektum mentérse került."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "A hőmérsékletet a Cura-ból lehet szabályozni.Kapcsolja ki ezt, ha a fúvóka hőmérsékletének szabályozását kívülről szeretné végezni."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Annak a meghatározása, hogy a tárgyasztal hőmérsékleti parancsokat tartalmaz-e a kódolás elején.Amikor a start_gcode már tartalmaz tárgyasztal hőmérsékleti parancsokat, a Cura frontend automatikusan letiltja ezt a beállítást."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Annak a meghatározása, hogy a fúvóka hőmérsékleti parancsokat tartalmaz-e a kódolás elején.Amikor a start_gcode már tartalmaz fúvóka hőmérsékleti parancsokat, a Cura frontend automatikusan letiltja ezt a beállítást."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr ""

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Parancs beszúrás arra, hogy a gép várakozzon, amíg a tárgyasztal eléri a beállított célhőmérsékletet."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "A nyomtatás előtt a nyomtatószálat előkészítio, és az olvadókamra nyomást felépíti úgy, hogy egy foltot nyomtat. A perem és a szoknya is viselkedhet így, hiszen annak a nyomtatása során is felépíthető a nyomás. Ebben az esetben ez a funkció nem működik, és ezzel időt takaríthatunk meg."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr ""

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Megmutatja-e ennek a gépnek a különféle json-fájlokban leírt változatait."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Használja -e a firmware szálvisszahúzási parancsokat (G10/G11), a G1 parancsokban használt E szálvisszahúzási parancsok helyett."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Várakozás addig, amíg a nyomtatófej el nem éri a beállított célhőmérsékletet."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Egyetlen kitöltési vonalszélesség."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "A padló vagy a tető egyetlen vonalszélessége."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr ""

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "A nyomtatás tetjén lévő területek egyetlen sorának szélessége."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Egy sor szélessége. Általában az egyes vonalak szélességének meg kell egyeznie a fúvóka szélességével. Ennek az értéknek minimális csökkentése azonban jobb nyomatokat eredményezhet."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Egyetlen előtorony vonalszélesség."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Egyetlen szoknya/perem vonalszélesség."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Egyetlen támasz padlóvonal szélesség."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Egyetlen támasz tetővonal szélesség."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Egyetlen támasz vonalszélesség."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Egyetlen alsó/felső sorszélessége."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Egyetlen falvonal szélessége az összes fali vonalhoz, a legkülső fal kivételével."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Egy fal vonalának szélessége."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "A vonalak szélessége a tutajalap rétegeiben. Ezeknek vastag vonalaknak kell lenniük, hogy elősegítsék a tárgyasztalhoz tapadást."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "A vonalak szélessége a középső tutajrétegben. Ha a második rétegnél többet extrudálunk, akkor a vonalak jobban tapadnak majd a tárgyasztalhoz."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "A vonalak szélessége a tutaj felső felületén. Ezek lehetnek vékony vonalak, így a tutaj teteje sima lesz."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "A külső falvonal szélessége. Ennek az értéknek a csökkentésével nagyobb szintű részletesség érhető el."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr ""

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Törlési pont (kefe) X helyzete"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Z emelés sebesség"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Inaktív fúvóka tisztítása az előtornyon"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Törlési mozgás távolsága"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Fúvóka tisztítás rétegek között"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Törlés szünet"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Törlés ismétlés száma"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Törlés visszahúzás távolság"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Törlés visszahúzás engedélyezése"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Törlés visszahúzás anyag kompenzáció"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr ""

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Visszahúzási sebesség"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Törlés visszahúzás sebesség"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr ""

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Z emelés magasság"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Kitöltésen belül"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr ""

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "X végállás pozitív irányban"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Az az X helyzet, ahol a törlési szkript elindul."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y felülbírálás Z-re"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Y végállás pozitív irányban"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Z végállás pozitív irányban"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Z emelés extruder váltás után"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Z emelés magassága extruder váltás után"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z emelés magasság"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Z emelés nyomtatott részeknél"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Z emelés sebesség"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Z emelés visszahúzáskor"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z varrat igazítás"

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr ""

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z varrat helyzet"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Relatív Z varrat"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z varrat X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z varrat Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z felülbírálás X/Y-ra"

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr ""

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Cikcakk"

msgctxt "travel description"
msgid "travel"
msgstr "fej átpozícionálás"

#~ msgctxt "user_defined_print_order_enabled description"
#~ msgid "Allows to order the object list to set the print sequence manually. First object from the list will be printed first."
#~ msgstr "Lehetővé teszi az objektumlista rendezését a nyomtatási sorrend kézi beállításához. A lista első objektuma lesz először nyomtatva."

#~ msgctxt "brim_outside_only label"
#~ msgid "Brim Only on Outside"
#~ msgstr "Perem csak kívül"

#~ msgctxt "layer_0_z_overlap description"
#~ msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
#~ msgstr "A modell első és második rétege között átfedést hoz létre Z irányban.Ez képes kompenzálni azt a hibát, ami az első rétegben keletkezett. Ezt a hibát az okozza, hogy a tutaj légrésben az első réteg benyúlik, így nem alakul ki a tökéletes első réteg.Az első réteg feletti összes rész magasságát érinti ez a beállítás."

#~ msgctxt "machine_nozzle_head_distance label"
#~ msgid "Nozzle Length"
#~ msgstr "Fúvóka hossza"

#~ msgctxt "variant_name"
#~ msgid "Nozzle Size"
#~ msgstr "Fúvóka méret"

#~ msgctxt "brim_outside_only description"
#~ msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
#~ msgstr "Csak a modell külső falaihoz nyomtat Peremet. Ez csökkenti a perem nyomtatási költségét, és nem szükséges a belső részeken eltávolítani majd azt, továbbá a test letapadását nem csökkenti jelentősen."

#~ msgctxt "wall_overhang_speed_factor label"
#~ msgid "Overhanging Wall Speed"
#~ msgstr "Falkinyúlás sebessége"

#~ msgctxt "wall_overhang_speed_factor description"
#~ msgid "Overhanging walls will be printed at this percentage of their normal print speed."
#~ msgstr "A kinyúló falak a normál nyomtatási sebesség százalékos arányában adjuk meg."

#~ msgctxt "support_interface_skip_height label"
#~ msgid "Support Interface Resolution"
#~ msgstr "Interfész felosztás"

#~ msgctxt "machine_nozzle_head_distance description"
#~ msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
#~ msgstr "A fúvóka csúcsa és a nyomtatófej legalacsonyabb része (fűtőblokk) közötti magasságkülönbség."

#~ msgctxt "support_interface_skip_height description"
#~ msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
#~ msgstr "Amikor a szeletelő ellenőrzi, hogy hol tart a támasz a modell alatt vagy fölött, szükség esetén a megadott magasságú lépéseket teszi meg. Az alacsonyabb értékek lassabb szeleteést okoznak, míg a magasabb érték a normál támasz kinyomtatását eredményezhetik olyan helyeken, ahol támasz interfészt kellene nyomtatni."
