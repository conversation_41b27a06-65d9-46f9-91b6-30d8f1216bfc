msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-13 09:02+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: it_IT\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>Come generare la prime tower:<ul><li><b>Normale:</b> creare un contenitore in cui vengono preparati i materiali secondari</li><li><b>Impilata:</b> creare una prime tower più intervallata possibile. Ciò farà risparmiare tempo e filamento, ma è possibile solo se i materiali utilizzati aderiscono tra loro</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "Un brim intorno a un modello potrebbe toccarne un altro in un punto non desiderato. Ciò rimuove tutto il brim entro questa distanza dai modelli che ne sono sprovvisti."

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Distanza da mantenere dai bordi del modello. La stiratura fino in fondo sino al bordo del reticolo può causare la formazione di un bordo frastagliato nella stampa."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Fattore indicante la quantità di filamento che viene compressa tra l'alimentatore e la camera dell'ugello, usato per stabilire a quale distanza spostare il materiale per un cambio di filamento."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Un elenco di direzioni linee intere da usare quando gli strati rivestimento superficie superiore utilizzano le linee o la configurazione zig zag. Gli elementi dall’elenco sono utilizzati in sequenza con il progredire degli strati e, al raggiungimento della fine dell’elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l’intero elenco è racchiuso tra parentesi quadre. L’elenco predefinito è vuoto, vale a dire che utilizza i valori angolari predefiniti (45 e 135 gradi)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Un elenco di direzioni linee intere da usare quando gli strati superiori/inferiori utilizzano le linee o la configurazione zig zag. Gli elementi dall’elenco sono utilizzati in sequenza con il progredire degli strati e, al raggiungimento della fine dell’elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l’intero elenco è racchiuso tra parentesi quadre. L’elenco predefinito è vuoto, vale a dire che utilizza i valori angolari predefiniti (45 e 135 gradi)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Elenco di direzioni linee intere da utilizzare. Gli elementi dall'elenco sono utilizzati in sequenza con il progredire dei layers e, al raggiungimento della fine dell'elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l'intero elenco è racchiuso tra parentesi quadre. L’elenco predefinito è vuoto, vale a dire che utilizza l'angolo predefinito di 0 gradi."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Elenco di direzioni linee intere da utilizzare. Gli elementi dall'elenco sono utilizzati in sequenza con il progredire dei layers e, al raggiungimento della fine dell'elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l'intero elenco è racchiuso tra parentesi quadre. L'elenco predefinito è vuoto, vale a dire che utilizza gli angoli predefiniti (alterna tra 45 e 135 gradi se le interfacce sono abbastanza spesse oppure 90 gradi)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Elenco di direzioni linee intere da utilizzare. Gli elementi dall'elenco sono utilizzati in sequenza con il progredire dei layers e, al raggiungimento della fine dell'elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l'intero elenco è racchiuso tra parentesi quadre. L'elenco predefinito è vuoto, vale a dire che utilizza gli angoli predefiniti (alterna tra 45 e 135 gradi se le interfacce sono abbastanza spesse oppure 90 gradi)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Elenco di direzioni linee intere da utilizzare. Gli elementi dall'elenco sono utilizzati in sequenza con il progredire dei layers e, al raggiungimento della fine dell'elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l'intero elenco è racchiuso tra parentesi quadre. L'elenco predefinito è vuoto, vale a dire che utilizza gli angoli predefiniti (alterna tra 45 e 135 gradi se le interfacce sono abbastanza spesse oppure 90 gradi)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Un elenco di direzioni linee intere. Gli elementi dall’elenco sono utilizzati in sequenza con il progredire degli strati e, al raggiungimento della fine dell’elenco, la sequenza ricomincia dall’inizio. Le voci elencate sono separate da virgole e l’intero elenco è racchiuso tra parentesi quadre. L’elenco predefinito è vuoto, vale a dire che utilizza i valori angolari predefiniti (45 e 135 gradi per le linee e la configurazione zig zag e 45 gradi per tutte le altre configurazioni)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Un elenco di poligoni con aree alle quali l’ugello non può accedere."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Un elenco di poligoni con aree alle quali la testina di stampa non può accedere."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Raccomandazione sull'entità della possibile distanza dei rami dai punti che supportano. I rami possono violare questo valore per raggiungere la loro destinazione (piano di stampa o parte piatta del modello). Ridurre questo valore può rendere il supporto più robusto, ma incrementa la quantità di rami (e, di conseguenza, la quantità di materiale/il tempo di stampa)"

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Posizione assoluta di innesco estrusore"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Variazione massima strati adattivi"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Dimensione della topografia dei layer adattivi"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Dimensione variazione strati adattivi"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Gli strati adattivi calcolano l’altezza degli strati in base alla forma del modello."

msgctxt "infill_wall_line_count description"
msgid "Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\nThis feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr "Aggiunge pareti supplementari intorno alla zona di riempimento. Queste pareti possono ridurre l’abbassamento delle linee del rivestimento esterno superiore/inferiore, pertanto saranno necessari meno strati di rivestimento esterno superiore/inferiore per ottenere la stessa qualità al costo del materiale supplementare."
"Questa funzione può essere abbinata a Collega poligoni riempimento per collegare tutto il riempimento in un unico percorso di estrusione senza necessità di avanzamenti o arretramenti, se configurata correttamente."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Adesione"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tendenza di adesione"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Regolare l’entità della sovrapposizione tra le pareti e (i punti finali delle) linee centrali del rivestimento esterno espressa in percentuale delle larghezze delle linee del rivestimento esterno. Una leggera sovrapposizione consente alle pareti di essere saldamente collegate al rivestimento. Si noti che, data una larghezza uguale del rivestimento esterno e della linea perimetrale, qualsiasi percentuale superiore al 50% può già causare il superamento della parete da parte del rivestimento esterno in quanto, in quel punto, la posizione dell’ugello dell’estrusore del rivestimento esterno può già avere superato la parte centrale della parete."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Regolare l’entità della sovrapposizione tra le pareti e (i punti finali delle) linee centrali del rivestimento esterno. Una leggera sovrapposizione consente alle pareti di essere saldamente collegate al rivestimento. Si noti che, data una larghezza uguale del rivestimento esterno e della linea perimetrale, qualsiasi percentuale superiore alla metà della parete può già causare il superamento della parete da parte del rivestimento esterno in quanto, in quel punto, la posizione dell’ugello dell’estrusore del rivestimento esterno può già aver superato la parte centrale della parete."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Regola la densità del riempimento della stampa."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Regola la densità delle parti superiori e inferiori della struttura di supporto. Un valore superiore genera sbalzi migliori, ma i supporti sono più difficili da rimuovere."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Consente di regolare la densità della struttura di supporto utilizzata per generare le punte dei rami. Un valore più alto si traduce in sbalzi migliori, ma i supporti saranno più difficili da rimuovere. Usa il tetto del supporto per valori molto alti o assicurati che la densità di supporto sia altrettanto alta nella parte superiore."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Regola la densità della struttura di supporto. Un valore superiore genera sbalzi migliori, ma i supporti sono più difficili da rimuovere."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Regolare il diametro del filamento utilizzato. Abbinare questo valore al diametro del filamento utilizzato."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Regola il posizionamento delle strutture di supporto. Il posizionamento può essere impostato su contatto con il piano di stampa o in tutti i possibili punti. Quando impostato su tutti i possibili punti, le strutture di supporto verranno anche stampate sul modello."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Dopo la stampa della torre di innesco con un ugello, pulisce il materiale fuoriuscito dall’altro ugello sulla torre di innesco."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Dopo il passaggio della macchina da un estrusore all’altro, il piano di stampa viene abbassato per creare uno spazio tra l’ugello e la stampa. In tal modo si previene il rilascio di materiale fuoriuscito dall’ugello sull’esterno di una stampa."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Tutto"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Tutti contemporaneamente"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Indica tutte le impostazioni che influiscono sulla risoluzione della stampa. Queste impostazioni hanno un elevato impatto sulla qualità (e il tempo di stampa)"

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "Consente di ordinare l'elenco degli oggetti per impostare manualmente la sequenza di stampa. Il primo oggetto dell'elenco verrà stampato per primo."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Parete supplementare alternativa"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Rimozione maglie alternate"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Alterna direzioni parete"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Consente di alternare direzioni parete ogni altro strato o inserto. Utile per materiali che possono accumulare stress, come per la stampa su metallo."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Alluminio"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Tenere sempre nota dello strumento attivo"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Arretra sempre quando si sposta per iniziare una parete esterna."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Determina l'entità di offset (o estensione dello strato) applicata a tutti i poligoni su ciascuno strato. I valori positivi possono compensare fori troppo estesi; i valori negativi possono compensare fori troppo piccoli."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "È l'entità di offset (estensione dello strato) applicata a tutti i poligoni di supporto in ciascuno strato. Un valore negativo può compensare lo schiacciamento del primo strato noto come \"zampa di elefante\"."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "È l'entità di offset (estensione dello strato) applicato a tutti i poligoni di supporto in ciascuno strato. I valori positivi possono appianare le aree di supporto, accrescendone la robustezza."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Entità di offset applicato alle parti inferiori del supporto."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Entità di offset applicato alle parti superiori del supporto."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Entità di offset applicato ai poligoni di interfaccia del supporto."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "L'entità di retrazione del filamento in modo che non fuoriesca durante la sequenza di pulitura."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Un aggiunta al raggio dal centro di ciascun cubo per controllare il contorno del modello, per decidere se questo cubo deve essere suddiviso. Valori maggiori comportano un guscio più spesso di cubi piccoli vicino al contorno del modello."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Maglia anti-sovrapposizione"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Posizione retratta anti fuoriuscita di materiale"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Velocità di retrazione anti fuoriuscita del materiale"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Applica l’offset estrusore al sistema coordinate. Influisce su tutti gli estrusori."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "Nei punti in cui i modelli si toccano, viene generata una struttura del fascio ad incastro. Questo migliora l'adesione tra i modelli, soprattutto quelli stampati in materiali diversi."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Aggiramento delle parti stampate durante gli spostamenti"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Aggiramento dei supporti durante gli spostamenti"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Indietro"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Indietro a sinistra"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Indietro a destra"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Entrambi"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Entrambi si sovrappongono"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Strati inferiori"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Strato iniziale configurazione inferiore"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Distanza prolunga rivestimento inferiore"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Larghezza rimozione rivestimento inferiore"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Spessore degli strati inferiori"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Densità del ramo"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Diametro del ramo"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Angolo del diametro del ramo"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Posizione di retrazione prima della rottura"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Velocità di retrazione prima della rottura"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Temperatura di preparazione alla rottura"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Posizione di retrazione per la rottura"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Velocità di retrazione per la rottura"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Temperatura di rottura"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Rottura del supporto in pezzi di grandi dimensioni"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Velocità della ventola ponte"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Ponte a strati multipli"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Densità del secondo rivestimento esterno ponte"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Velocità della ventola per il secondo rivestimento esterno ponte"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Flusso del secondo rivestimento esterno ponte"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Velocità di stampa del secondo rivestimento esterno ponte"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Densità del rivestimento esterno ponte"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Flusso del rivestimento esterno ponte"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Velocità di stampa del rivestimento esterno ponte"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Soglia di supporto rivestimento esterno ponte"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Densità massima del riempimento rado del Bridge"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Densità del terzo rivestimento esterno ponte"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Velocità della ventola del terzo rivestimento esterno ponte"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Flusso del terzo rivestimento esterno ponte"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Velocità di stampa del terzo rivestimento esterno ponte"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Coasting parete ponte"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Flusso della parete ponte"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Velocità di stampa della parete ponte"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Brim"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "Margine per evitare il brim"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Distanza del Brim"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Numero di linee del brim"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "Posizione del brim"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Brim in sostituzione del supporto"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Larghezza del brim"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Adesione piano di stampa"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Estrusore adesione piano di stampa"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Tipo di adesione piano di stampa"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Materiale piano di stampa"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Forma del piano di stampa"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Temperatura piano di stampa"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Temperatura piano di stampa Strato iniziale"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Temperatura volume di stampa"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "Limite per la temperatura del volume di costruzione"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "Avviso sulla temperatura del volume di costruzione"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "Attivando questa impostazione, la tua torre di primerizzazione avrà un bordo, anche se il modello non lo prevede. Se desideri una base più robusta per una torre alta, puoi aumentare l'altezza della base."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Centra oggetto"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Cambia la geometria del modello stampato in modo da richiedere un supporto minimo. Sbalzi molto inclinati diventeranno sbalzi poco profondi. Le aree di sbalzo scendono per diventare più verticali."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Scegliere tra le tecniche disponibili per generare il supporto. Il supporto \"normale\" crea una struttura di supporto direttamente sotto le parti di sbalzo e rilascia tali aree direttamente al di sotto. La struttura \"ad albero\" crea delle ramificazioni verso le aree di sbalzo che supportano il modello sulle punte di tali ramificazioni consentendo a queste ultime di avanzare intorno al modello per supportarlo il più possibile dal piano di stampa."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Velocità di Coasting"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Volume di Coasting"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Il Coasting sostituisce l'ultima parte di un percorso di estrusione con un percorso di spostamento. Il materiale fuoriuscito viene utilizzato per stampare l'ultimo tratto del percorso di estrusione al fine di ridurre i filamenti."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Modalità Combing"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "La funzione Combing tiene l’ugello all’interno delle aree già stampate durante lo spostamento. In tal modo le corse di spostamento sono leggermente più lunghe ma si riduce l’esigenza di effettuare retrazioni. Se questa funzione viene disabilitata, il materiale viene retratto e l’ugello si sposta in linea retta al punto successivo. È anche possibile evitare il combing sopra le aree del rivestimento esterno superiore/inferiore o effettuare il combing solo nel riempimento."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Impostazioni riga di comando"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrica"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Concentrica"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Concentriche"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Angolo del supporto conico"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Larghezza minima del supporto conico"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Collegamento delle linee di riempimento"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Collega poligoni di riempimento"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Collegamento linee supporto"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Collegamento Zig Zag supporto"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Collega poligoni superiori/inferiori"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Collega i percorsi di riempimento quando corrono uno accanto all’altro. Per le configurazioni di riempimento composte da più poligoni chiusi, l’abilitazione di questa impostazione riduce notevolmente il tempo di spostamento."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Collega i ZigZag. Questo aumenta la forza della struttura di supporto a zig zag."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Collega le estremità delle linee del supporto. L’abilitazione di questa impostazione consente di ottenere un supporto più robusto e ridurre la sottoestrusione, ma si utilizza più materiale."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Collegare le estremità nel punto in cui il riempimento incontra la parete interna utilizzando una linea che segue la forma della parete interna. L'abilitazione di questa impostazione può far meglio aderire il riempimento alle pareti riducendo nel contempo gli effetti del riempimento sulla qualità delle superfici verticali. La disabilitazione di questa impostazione consente di ridurre la quantità di materiale utilizzato."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Collega i percorsi del rivestimento esterno superiore/inferiore quando corrono uno accanto all’altro. Per le configurazioni concentriche, l’abilitazione di questa impostazione riduce notevolmente il tempo di spostamento, tuttavia poiché i collegamenti possono aver luogo a metà del riempimento, con questa funzione la qualità della superficie superiore potrebbe risultare inferiore."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Controlla se gli angoli sul profilo del modello influenzano la posizione della giunzione. Nessuno significa che gli angoli non hanno alcuna influenza sulla posizione della giunzione. Nascondi giunzione favorisce la presenza della giunzione su un angolo interno. Esponi giunzione favorisce la presenza della giunzione su un angolo esterno. Nascondi o esponi giunzione favorisce la presenza della giunzione su un angolo interno o esterno. Smart Hiding consente sia gli angoli interni che quelli esterni ma sceglie con maggiore frequenza gli angoli interni, se opportuno."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Converte ogni linea di riempimento in questo numero di linee. Le linee supplementari non si incrociano tra loro, ma si evitano. In tal modo il riempimento risulta più rigido, ma il tempo di stampa e la quantità di materiale aumentano."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Velocità di raffreddamento"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Raffreddamento"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Raffreddamento"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Incrociata"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Incrociata"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Incrociata 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Dimensioni cavità 3D incrociata"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Immagine di densità del riempimento incrociato per il supporto"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Immagine di densità del riempimento incrociato"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Materiale cristallino"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Cubo"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Suddivisione in cubi"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Guscio suddivisione in cubi"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Ritaglio maglia"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Collegamento dei dati di flusso del materiale (in mm3 al secondo) alla temperatura (in °C)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Accelerazione predefinita"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Temperatura piano di stampa preimpostata"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Jerk filamento predefinito"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Temperatura di stampa preimpostata"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Jerk X-Y predefinito"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Jerk Z predefinito"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Indica il jerk predefinito per lo spostamento sul piano orizzontale."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Indica il jerk predefinito del motore per la direzione Z."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Indica il jerk predefinito del motore del filamento."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Rileva i ponti e modifica la velocità di stampa, il flusso e le impostazioni ventola durante la stampa dei ponti."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Determina l'ordine di stampa delle pareti. La stampa anticipata delle pareti esterne migliora la precisione dimensionale poiché i guasti dalle pareti interne non possono propagarsi all'esterno. Se si esegue la stampa in un momento successivo, tuttavia, è possibile impilarle meglio quando vengono stampati gli sbalzi. Quando c'è una quantità irregolare di pareti interne totali, l'ultima riga centrale viene sempre stampata per ultima."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Determina la priorità di questa mesh quando si considera la sovrapposizione multipla delle mesh di riempimento. Per le aree con la sovrapposizione di più mesh di riempimento verranno utilizzate le impostazioni della mesh con la classificazione più alta. Una mesh di riempimento con una classificazione più alta modificherà il riempimento delle mesh di riempimento con una classificazione inferiore e delle mesh normali."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Determina quando uno strato di riempimento fulmine deve supportare il materiale sopra di esso. Misurato nell'angolo dato lo stesso di uno strato."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Determina quando uno strato di riempimento fulmine deve supportare il modello sopra di esso. Misurato nell'angolo dato lo spessore."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Diametro"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Aumento del diametro sul modello"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Diametro che ogni ramo cerca di ottenere quando raggiunge la piastra di costruzione. Migliora l'adesione al piano."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Sono previste diverse opzioni che consentono di migliorare l'applicazione dello strato iniziale dell’estrusione e migliorano l’adesione. Il brim aggiunge un'area piana a singolo strato attorno alla base del modello, per evitare deformazioni. Il raft aggiunge un fitto reticolato con un tetto al di sotto del modello. Lo skirt è una linea stampata attorno al modello, ma non collegata al modello."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Aree non consentite"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Indica la distanza tra le linee di riempimento stampate. Questa impostazione viene calcolata mediante la densità del riempimento e la larghezza della linea di riempimento."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Indica la distanza tra le linee della struttura di supporto dello strato iniziale stampato. Questa impostazione viene calcolata mediante la densità del supporto."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Distanza tra le linee della parte inferiore del supporto stampate. Questa impostazione viene calcolata dalla densità della parte inferiore del supporto, ma può essere regolata separatamente."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Distanza tra le linee della parte superiore del supporto stampate. Questa impostazione viene calcolata dalla densità della parte superiore del supporto, ma può essere regolata separatamente."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Indica la distanza tra le linee della struttura di supporto stampata. Questa impostazione viene calcolata mediante la densità del supporto."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Distanza dalla stampa alla base del supporto. Notare che viene arrotondata all'altezza del layer successivo."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "È la distanza tra la parte superiore del supporto e la stampa."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Distanza dalla parte superiore/inferiore della struttura di supporto alla stampa. Questo spazio permette di rimuovere i supporti dopo la stampa del modello. L'ultimo strato di supporto sotto il modello potrebbe essere una frazione degli strati regolari."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Indica la distanza di uno spostamento inserito dopo ogni linea di riempimento, per determinare una migliore adesione del riempimento alle pareti. Questa opzione è simile alla sovrapposizione del riempimento, ma senza estrusione e solo su una estremità della linea di riempimento."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Distanza di spostamento inserita dopo la parete esterna per nascondere meglio la giunzione Z."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Indica la distanza del riparo paravento dalla stampa, nelle direzioni X/Y."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Indica la distanza del riparo materiale fuoriuscito dalla stampa, nelle direzioni X/Y."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Indica la distanza della struttura di supporto dallo sbalzo, nelle direzioni X/Y."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Indica la distanza della struttura di supporto dalla stampa, nelle direzioni X/Y."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "I punti di distanza vengono spostati per risistemare il percorso"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "I punti di distanza vengono spostati per risistemare il percorso"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Non generare aree di riempimento inferiori a questa (piuttosto usare il rivestimento esterno)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Altezza del riparo paravento"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Limitazione del riparo paravento"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Distanza X/Y del riparo paravento"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Maglia supporto di discesa"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Doppia estrusione"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Ellittica"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Abilita controllo accelerazione"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Abilita impostazioni ponte"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Abilitazione della funzione di Coasting"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Abilitazione del supporto conico"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Abilitazione del riparo paravento"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Abilita movimento fluido"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Abilita stiratura"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Abilita controllo jerk"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Abilita controllo temperatura ugello"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Abilitazione del riparo materiale fuoriuscito"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Abilitazione blob di innesco"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Abilitazione torre di innesco"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Abilitazione raffreddamento stampa"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "Abilita il resoconto del processo di stampa"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Abilitazione della retrazione"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Abilitazione brim del supporto"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Abilitazione parte inferiore supporto"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Abilitazione interfaccia supporto"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Abilitazione irrobustimento parte superiore (tetto) del supporto"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Abilita l'accelerazione spostamenti"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Abilita jerk spostamenti"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Abilita il riparo esterno del materiale fuoriuscito. Questo crea un guscio intorno al modello per pulitura con un secondo ugello, se è alla stessa altezza del primo ugello."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "Abilita il resoconto del processo di stampa per impostare i valori di soglia per il rilevamento di eventuali errori."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "Abilita piccole (fino a \"piccola larghezza superiore/inferiore) aree sul livello più alto (esposto all'aria) per il riempimento con muri invece che con il modello predefinito."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Abilita la regolazione del jerk della testina di stampa quando la velocità nell’asse X o Y cambia. Aumentando il jerk il tempo di stampa si riduce a discapito della qualità di stampa."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Abilita la regolazione dell’accelerazione della testina di stampa. Aumentando le accelerazioni il tempo di stampa si riduce a discapito della qualità di stampa."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Abilita le ventole di raffreddamento durante la stampa. Le ventole migliorano la qualità di stampa sugli strati con tempi per strato più brevi e ponti/sbalzi."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "Codice G fine"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Lunghezza di svuotamento di fine filamento"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Velocità di svuotamento di fine filamento"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Abilita la stampa del brim intorno al modello anche se quello spazio dovrebbe essere occupato dal supporto. Sostituisce alcune zone del primo strato del supporto con zone del brim."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "Ovunque"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "In Tutti i Possibili Punti"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Esclusiva"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Sperimentale"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Esponi giunzione"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Ricucitura completa dei fori"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Questa funzione tenta di 'ricucire' i fori aperti nella maglia chiudendo il foro con poligoni a contatto. Questa opzione può richiedere lunghi tempi di elaborazione."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Conteggio pareti di riempimento supplementari"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Numero di pareti di rivestimento esterno supplementari"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Materiale extra per l'innesco dopo il cambio dell'ugello."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Posizione X innesco estrusore"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Posizione Y innesco estrusore"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Posizione Z innesco estrusore"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Condivisione del riscaldatore da parte degli estrusori"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Estrusori condividono ugello"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Modificatore della velocità di raffreddamento estrusione"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Fattore di correzione della velocità basato sulla larghezza di estrusione. A 0% la velocità di movimento viene mantenuta costante alla velocità di stampa. Al 100% la velocità di movimento viene regolata in modo da mantenere costante il flusso (in mm³/s), ovvero le linee la cui larghezza è metà di quella normale vengono stampate due volte più velocemente e le linee larghe il doppio vengono stampate a metà della velocità. Un valore maggiore di 100% può aiutare a compensare la pressione più alta richiesta per estrudere linee larghe."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Velocità della ventola"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Override velocità della ventola"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Profili di dettagli inferiori a questa lunghezza saranno stampati utilizzando Velocità Dettagli di piccole dimensioni."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Funzionalità che non sono state ancora precisate completamente."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Diametro ruota del tirafilo"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Temperatura di stampa finale"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Retrazione firmware"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Estrusore del supporto primo strato"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Flusso"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Rapporto di equalizzazione del flusso"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "Limite per il flusso"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Fattore di compensazione del flusso"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Offset massimo dell'estrusione di compensazione del flusso"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Grafico della temperatura del flusso"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "Avviso sul flusso"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Determina la compensazione del flusso per il primo strato: la quantità di materiale estruso sullo strato iniziale viene moltiplicata per questo valore."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Compensazione del flusso sulle linee inferiori del primo strato"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Compensazione del flusso sulle linee di riempimento."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Compensazione del flusso sulle linee di supporto superiore o inferiore."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Compensazione del flusso sulle linee delle aree nella parte superiore della stampa."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Compensazione del flusso sulle linee della torre di innesco."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Compensazione del flusso sulle linee dello skirt o del brim."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Compensazione del flusso sulle linee di supporto inferiore."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Compensazione del flusso sulle linee di supporto superiore."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Compensazione del flusso sulle linee di supporto."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Compensazione del flusso sulla linea a parete più esterna del primo strato."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Compensazione del flusso sulla linea perimetrale più esterna."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Compensazione del flusso sulla linea della parete esterna più esterna della superficie superiore."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Compensazione del flusso sulle linee delle pareti della superficie superiore per tutte le linee delle pareti tranne quella più esterna."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Compensazione del flusso sulle linee superiore/inferiore."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Compensazione del flusso sulle linee di parete per tutte le linee di parete tranne quella più esterna, ma solo per il primo strato"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Compensazione del flusso sulle linee perimetrali per tutte le linee perimetrali tranne quella più esterna."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Compensazione del flusso sulle linee perimetrali."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Determina la compensazione del flusso: la quantità di materiale estruso viene moltiplicata per questo valore."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Angolo di movimento del fluido"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Distanza di spostamento del movimento del fluido"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Breve distanza di movimento del fluido"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Lunghezza di svuotamento dello scarico"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Velocità di svuotamento dello scarico"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Per strutture sottili, circa una o due volte la dimensione dell'ugello, le larghezze delle linee devono essere modificate per rispettare lo spessore del modello. Questa impostazione controlla la larghezza minima della linea consentita per le pareti. Le larghezze minime delle linee determinano intrinsecamente anche le larghezze massime delle linee, poiché si esegue la transizione da N a N+1 pareti ad uno spessore geometrico in cui le pareti N sono larghe e le pareti N+1 sono strette. La linea perimetrale più larga possible è due volte la larghezza minima della linea perimetrale."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Avanti"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Avanti a sinistra"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Avanti a destra"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Piena altezza"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Rivestimento esterno incoerente (fuzzy)"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Densità del rivestimento esterno incoerente (fuzzy)"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Fuzzy Skin solo all'esterno"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Distanza dei punti del rivestimento incoerente (fuzzy)"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Spessore del rivestimento esterno incoerente (fuzzy)"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Versione codice G"

msgctxt "machine_end_gcode description"
msgid "G-code commands to be executed at the very end - separated by \n."
msgstr "I comandi codice G da eseguire alla fine, separati da "
"."

msgctxt "machine_start_gcode description"
msgid "G-code commands to be executed at the very start - separated by \n."
msgstr "I comandi codice G da eseguire all’avvio, separati da "
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "Il GUID del materiale. È impostato automaticamente."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Altezza gantry"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Generazione della struttura a incastro"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Generazione supporto"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Genera un brim entro le zone di riempimento del supporto del primo strato. Questo brim viene stampato al di sotto del supporto, non intorno ad esso. L’abilitazione di questa impostazione aumenta l’adesione del supporto al piano di stampa."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Genera un’interfaccia densa tra il modello e il supporto. Questo crea un rivestimento esterno sulla sommità del supporto su cui viene stampato il modello e al fondo del supporto, dove appoggia sul modello."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Genera una spessa lastra di materiale tra la parte inferiore del supporto e il modello. Questo crea un rivestimento tra modello e supporto."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Genera una spessa lastra di materiale tra la parte superiore del supporto e il modello. Questo crea un rivestimento tra modello e supporto."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Genera strutture per supportare le parti del modello a sbalzo. Senza queste strutture, queste parti collasserebbero durante la stampa."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Cristallo"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Andare ancora una volta sulla superficie superiore, questa volta estrudendo una piccolissima quantità di materiale. Lo scopo è quello di sciogliere ulteriormente la plastica sulla parte superiore, creando una superficie più liscia. La pressione nella camera dell'ugello viene mantenuta elevata, in modo che le grinze nella superficie siano riempite con il materiale."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Altezza fasi di riempimento graduale"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Fasi di riempimento graduale"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Altezza fasi di riempimento graduale del supporto"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Fasi di riempimento graduale del supporto"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Riduci gradualmente questa temperatura quando si stampa a velocità ridotte a causa del tempo di strato minimo."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Griglia"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Griglia"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Griglia"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Griglia"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Griglia"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Raggruppa le pareti esterne"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "È dotato della stabilizzazione della temperatura del volume di stampa"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Piano di stampa riscaldato"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Velocità di riscaldamento"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Lunghezza della zona di riscaldamento"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Indica la limitazione in altezza del riparo paravento. Al di sopra di tale altezza non sarà stampato alcun riparo."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Nascondi giunzione"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Nascondi o esponi giunzione"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Espansione orizzontale dei fori"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Diametro massimo di espansione orizzontale dei fori"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "I fori e i profili delle parti con un diametro inferiore a quello indicato verranno stampati utilizzando Velocità Dettagli di piccole dimensioni."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Espansione orizzontale"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Fattore di scala orizzontale per la compensazione della contrazione"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "La lunghezza massima di estensione del filamento prima che si rompa durante il riscaldamento."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "La distanza alla quale deve essere retratto il materiale prima che smetta di fuoriuscire."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "Distanza di spostamento del filamento al fine di compensare le modifiche nella velocità di flusso, come percentuale della distanza di spostamento del filamento in un secondo di estrusione."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "La distanza di retrazione del filamento al fine di consentirne la rottura netta."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "La velocità massima di retrazione del filamento prima che si rompa durante questa operazione."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "La velocità a cui deve essere retratto il materiale durante un cambio di filamento per evitare la fuoriuscita di materiale."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "Velocità di adescamento del materiale dopo la sostituzione di una bobina vuota con una nuova dello stesso materiale."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "Velocità di adescamento del materiale dopo il passaggio a un materiale diverso."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "Tempo per il quale è possibile mantenere il materiale all'esterno di un luogo di conservazione asciutto in sicurezza."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "I passi del motore passo-passo in un millimetro di spostamento nella direzione X."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "I passi del motore passo-passo in un millimetro di spostamento nella direzione Y."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "I passi del motore passo-passo in un millimetro di spostamento nella direzione Z."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Quanti passi dei motori passo-passo causano lo spostamento della ruota del tirafilo di un millimetro attorno alla sua circonferenza."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "Quantità di materiale da utilizzare per svuotare il materiale precedente dall'ugello (in lunghezza del filamento) durante la sostituzione di una bobina vuota con una nuova dello stesso materiale."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "Quantità di materiale da utilizzare per svuotare il materiale precedente dall'ugello (in lunghezza del filamento) quando si passa a un materiale diverso."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "La quantità di filamento di ogni estrusore che si presume sia stata retratta dalla punta dell'ugello condiviso al termine dello script gcode di avvio stampante; il valore deve essere uguale o maggiore della lunghezza della parte comune dei condotti dell'ugello."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Come interagiranno l'interfaccia di supporto e il supporto quando si sovrappongono. Attualmente implementato solo per il tetto di supporto."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Entità dell'altezza di un ramo se è posizionato sul modello. Previene piccoli blob di supporto. Questa impostazione viene ignorata quando un ramo supporta un tetto di supporto."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Se una zona di rivestimento esterno è supportata per meno di questa percentuale della sua area, effettuare la stampa utilizzando le impostazioni ponte. In caso contrario viene stampata utilizzando le normali impostazioni rivestimento esterno."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Se un segmento del percorso utensile devia più di questo angolo dal movimento generale, viene risistemato."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Se abilitata, il secondo e il terzo strato sopra l’aria vengono stampati utilizzando le seguenti impostazioni. In caso contrario, questi strati vengono stampati utilizzando le impostazioni normali."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Se si pensa di eseguire la transizione avanti e indietro tra numeri di pareti differenti in rapida successione, non eseguire alcuna transizione. Rimuovere le transizioni se sono più vicine di questa distanza."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se la base del raft è abilitata, questa è l'area extra attorno al modello a cui viene assegnato un raft. Se si aumenta questo margine si crea un raft più robusto, utilizzando più materiale e lasciando meno spazio per la stampa."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se è abilitata la funzione raft, questo valore indica di quanto il raft fuoriesce rispetto al perimetro esterno del modello. Aumentando questo margine si creerà un raft più robusto, utilizzando però più materiale e lasciando meno spazio per la stampa."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se la parte centrale del raft è abilitata, questa è l'area extra attorno al modello a cui viene assegnato un raft. Se si aumenta questo margine si crea un raft più robusto, utilizzando più materiale e lasciando meno area per la stampa."

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se la parte superiore del raft è abilitata, questa è l'area extra attorno al modello a cui viene assegnato un raft. Se si aumenta questo margine si crea un raft più robusto, utilizzando più materiale e lasciando meno area per la stampa."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Questa funzione ignora la geometria interna derivante da volumi in sovrapposizione all’interno di una maglia, stampandoli come un unico volume. Questo può comportare la scomparsa di cavità interne."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Includi temperatura piano di stampa"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Includi le temperature del materiale"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inclusiva"

msgctxt "infill description"
msgid "Infill"
msgstr "Riempimento"

msgctxt "infill label"
msgid "Infill"
msgstr "Riempimento"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Accelerazione riempimento"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Riempimento prima delle pareti"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Densità del riempimento"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Estrusore riempimento"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Flusso di riempimento"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Jerk riempimento"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Spessore dello strato di riempimento"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Direzioni delle linee di riempimento"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Distanza tra le linee di riempimento"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Moltiplicatore delle linee di riempimento"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Larghezza delle linee di riempimento"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Maglia di riempimento"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Angolo di sbalzo del riempimento"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Sovrapposizione del riempimento"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Percentuale di sovrapposizione del riempimento"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Configurazione di riempimento"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Velocità di riempimento"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Supporto riempimento"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Ottimizzazione spostamenti riempimento"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Distanza del riempimento"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Offset X riempimento"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Offset Y riempimento"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Layer inferiori iniziali"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Velocità iniziale della ventola"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Accelerazione dello strato iniziale"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Flusso inferiore dello strato iniziale"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Diametro dello strato iniziale"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Flusso dello strato iniziale"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Altezza dello strato iniziale"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Espansione orizzontale dello strato iniziale"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Flusso della parete interna dello strato iniziale"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Jerk dello strato iniziale"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Larghezza linea strato iniziale"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Flusso della parete esterna dello strato iniziale"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Accelerazione di stampa strato iniziale"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Jerk di stampa strato iniziale"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Velocità di stampa strato iniziale"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Velocità di stampa dello strato iniziale"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Distanza tra le linee del supporto dello strato iniziale"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Accelerazione spostamenti dello strato iniziale"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Jerk spostamenti dello strato iniziale"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Velocità di spostamento dello strato iniziale"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Z Sovrapposizione Primo Strato"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Temperatura di stampa iniziale"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Accelerazione parete interna"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Estrusore parete interna"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Jerk parete interna"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Velocità di stampa della parete interna"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Flusso pareti interne"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Larghezza delle linee della parete interna"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Inserto applicato al percorso della parete esterna. Se la parete esterna è di dimensioni inferiori all’ugello e stampata dopo le pareti interne, utilizzare questo offset per fare in modo che il foro dell’ugello si sovrapponga alle pareti interne anziché all’esterno del modello."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "Solo all'interno"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "Dall'interno all'esterno"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Linee di interfaccia preferite"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Interfaccia preferita"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "Impilata"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Conteggio degli strati delle travi ad incastro"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Larghezza della trave a incastro"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "Prevenzione incastro dei bordi"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "Profondità di incastro"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "Orientamento della struttura ad incastro"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Stiramento del solo strato più elevato"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Accelerazione di stiratura"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Flusso di stiratura"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Inserto di stiratura"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Jerk stiratura"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Spaziatura delle linee di stiratura"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Configurazione di stiratura"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Velocità di stiratura"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Origine del centro"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "È un materiale di supporto"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Questo tipo di materiale è quello che si stacca in modo netto quando viene riscaldato (cristallino) oppure è il tipo che produce lunghe catene di polimeri intrecciati (non cristallino)?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Questo materiale viene normalmente utilizzato come materiale di supporto durante la stampa."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Distorce solo i profili delle parti, non i fori di queste."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Mantenimento delle superfici scollegate"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Altezza dello strato"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Avvio strato X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Avvio strato Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Indica lo spessore dello strato di base del raft. Questo strato deve essere spesso per aderire saldamente al piano di stampa."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "È lo spessore dello strato intermedio del raft."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "È lo spessore degli strati superiori del raft."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Lasciare un collegamento tra le linee del supporto ogni N millimetri per facilitare la rottura del supporto stesso."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Sinistra"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Sollevamento della testina"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Fulmine"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Angolo di sbalzo riempimento fulmine"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Angolo eliminazione riempimento fulmine"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Angolo di raddrizzatura riempimento fulmine"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Angolo di supporto riempimento fulmine"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Limite di portata dei rami"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Limita la distanza di ogni ramo dal punto che supporta. Questo può rendere il supporto più robusto, ma aumenta la quantità di rami (e, di conseguenza, la quantità di materiale/il tempo di stampa)"

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "Limite per attivare l'avviso sulla temperatura del volume di costruzione."

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "Limite per il rilevamento dell'anomalia della temperatura del volume di costruzione."

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "Limite per il rilevamento dell'anomalia della temperatura di stampa."

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "Limite per attivare l'avviso sulla temperatura di stampa."

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "Limite per il rilevamento dell'anomalia del flusso."

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "Limite per attivare l'avviso sul flusso."

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Limita il volume di questa maglia all'interno di altre maglie. Questo può essere utilizzato per stampare talune aree di una maglia con impostazioni diverse e con un diverso estrusore."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Limitazione in altezza"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Larghezza della linea"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Macchina"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Profondità macchina"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Poligono testina macchina e ventola"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Altezza macchina"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Tipo di macchina"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Larghezza macchina"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Impostazioni macchina specifiche"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Rendi stampabile lo sbalzo"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Fa sovrapporre leggermente le maglie a contatto tra loro. In tal modo ne migliora l’adesione."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Realizza aree di supporto più piccole nella parte inferiore che in corrispondenza dello sbalzo."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Rappresenta il supporto ovunque sotto la maglia di supporto, in modo che in questa non vi siano punti a sbalzo."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Rende la posizione di innesco estrusore assoluta anziché relativa rispetto all’ultima posizione nota della testina."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\nIt may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr "Il primo e il secondo strato del modello vanno sovrapposti nella direzione Z per compensare il filamento perso nello spazio vuoto. Tutti i modelli al di sopra del primo strato saranno spostati verso il basso da questa quantità."
"Si può notare che a volte il secondo strato viene stampato al di sotto dello strato iniziale a causa di questa impostazione. Si tratta di un comportamento previsto."

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Rendere le maglie più indicate alla stampa 3D."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (volumetrica)"

msgctxt "material description"
msgid "Material"
msgstr "Materiale"

msgctxt "material label"
msgid "Material"
msgstr "Materiale"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "Marca del materiale"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID materiale"

msgctxt "material_type label"
msgid "Material Type"
msgstr "Tipo di materiale"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Volume di materiale tra le operazioni di pulitura"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Massima distanza di combing senza retrazione"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Accelerazione massima X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Accelerazione massima Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Accelerazione massima Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Angolo massimo dei rami"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Deviazione massima"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Deviazione massima dell'area di estrusione"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Velocità massima della ventola"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Accelerazione massima filamento"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Massimo angolo modello"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Area foro di sbalzo massima"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Durata di posizionamento massima"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Risoluzione massima"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Numero massimo di retrazioni"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Angolo massimo rivestimento esterno per prolunga"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Velocità massima E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Velocità massima X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Velocità massima Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Velocità massima Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Diametro supportato dalla torre"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Risoluzione massima di spostamento"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "Indica l’accelerazione massima del motore per la direzione X"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Indica l’accelerazione massima del motore per la direzione Y."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Indica l’accelerazione massima del motore per la direzione Z."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Indica l’accelerazione massima del motore del filamento."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Densità massima del riempimento considerato rado. Il rivestimento esterno sul riempimento rado è considerato non supportato; pertanto potrebbe essere trattato come rivestimento esterno ponte."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "È il diametro massimo nelle direzioni X/Y di una piccola area, che deve essere sostenuta da una torre speciale."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Il massimo volume di materiale che può essere estruso prima di iniziare un'altra operazione di pulitura ugello. Se questo valore è inferiore al volume del materiale richiesto in un layer, l'impostazione non ha effetto in questo layer, vale a dire che si limita a una pulitura per layer."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Sovrapposizione maglie"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Correzioni delle maglie"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Posizione maglia X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Posizione maglia Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Posizione maglia Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Classificazione dell'elaborazione delle maglie"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matrice rotazione maglia"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Intermedia"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Larghezza minimo dello stampo"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Tempo minimo temperatura di standby"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Lunghezza minima parete ponte"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Larghezza minima della linea perimetrale pari"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Finestra di minima distanza di estrusione"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Dimensioni minime della feature"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Velocità di alimentazione minima"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Altezza minima rispetto al modello"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Area minima riempimento"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Tempo minimo per strato"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Larghezza minima della linea perimetrale dispari"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Circonferenza minima dei poligoni"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Larghezza minima rivestimento esterno per prolunga"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Velocità minima"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Area minima supporto"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Area minima parti inferiori supporto"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Area minima interfaccia supporto"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Area minima parti superiori supporto"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Distanza X/Y supporto minima"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Larghezza minima della linea perimetrale sottile"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Volume minimo prima del Coasting"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Larghezza minima della linea perimetrale"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Dimensione minima dell'area per i poligoni dell'interfaccia di supporto. I poligoni con un'area più piccola rispetto a questo valore saranno stampati come supporto normale."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Dimensioni minime area per i poligoni del supporto. I poligoni con un’area inferiore a questo valore non verranno generati."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Dimensione minima dell'area per le parti inferiori del supporto. I poligoni con un'area più piccola rispetto a questo valore saranno stampati come supporto normale."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Dimensione minima dell'area per le parti superiori del supporto. I poligoni con un'area più piccola rispetto a questo valore saranno stampati come supporto normale."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Spessore minimo di feature sottili. Le feature modello che sono più sottili di questo valore non verranno stampate, mentre le feature più spesse delle dimensioni minime della feature verranno ampliate fino alla larghezza minima della linea perimetrale."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Indica la larghezza minima alla quale viene ridotta la base dell’area del supporto conico. Larghezze minori possono comportare strutture di supporto instabili."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Stampo"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Angolo stampo"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Altezza parte superiore dello stampo"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Ordine di stiratura monotonico"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "Ordine monotono della superficie della parte superiore del raft"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Ordine superficie superiore monotonico"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Ordine superiore/inferiore monotonico"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Più linee di skirt contribuiscono a migliorare l'avvio dell'estrusione per modelli di piccole dimensioni. L'impostazione di questo valore a 0 disattiverà la funzione skirt."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Moltiplicatore della larghezza della linea del primo strato Il suo aumento potrebbe migliorare l'adesione al piano."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Fattore di spostamento senza carico"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Nessun rivest. est. negli interstizi a Z"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Modi non tradizionali di stampare i modelli."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Nessuno"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Nessuno"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normale"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "Normale"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normale"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Di norma Cura cerca di \"ricucire\" piccoli fori nella maglia e di rimuovere le parti di uno strato che presentano grossi fori. Abilitando questa opzione, Cura mantiene quelle parti che non possono essere 'ricucite'. Questa opzione deve essere utilizzata come ultima risorsa quando non sia stato possibile produrre un corretto codice G in nessun altro modo."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Non nel rivestimento"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Non su superficie esterna"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Angolo ugello"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Diametro ugello"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Aree ugello non consentite"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "ID ugello"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "Lunghezza ugello"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Quantità di materiale extra della Prime Tower, al cambio ugello"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Velocità innesco cambio ugello"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Velocità di retrazione cambio ugello"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Distanza di retrazione cambio ugello"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Velocità di retrazione cambio ugello"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Numero di estrusori"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Numero di estrusori abilitati"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Numero di strati stampati a velocità inferiore"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Numero di treni di estrusori abilitati; impostato automaticamente nel software"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Il numero di treni di estrusori. Un treno di estrusori è la combinazione di un alimentatore, un tubo bowden e un ugello."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Numero di passaggi dell'ugello attraverso lo spazzolino."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Indica il numero di volte per dimezzare la densità del riempimento quando si va al di sotto degli strati superiori. Le aree più vicine agli strati superiori avranno una densità maggiore, fino alla densità del riempimento."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Indica il numero di volte per dimezzare la densità del riempimento quando si va al di sotto delle superfici superiori. Le aree più vicine alle superfici superiori avranno una densità maggiore, fino alla densità del riempimento."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Ottagonale"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Disinserita"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Offset applicato all’oggetto per la direzione X."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Offset applicato all’oggetto per la direzione Y."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Offset applicato all’oggetto in direzione z. Con questo potrai effettuare quello che veniva denominato 'Object Sink’."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Offset con estrusore"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Sul piano di stampa, quando possibile"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Sul modello, se necessario"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Uno alla volta"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Esegue solo uno Z Hop quando si sposta sopra le parti stampate che non possono essere evitate mediante uno spostamento orizzontale con Aggiramento delle parti stampate durante lo spostamento."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Effettua lo stiramento solo dell'ultimissimo strato della maglia. È possibile quindi risparmiare tempo se gli strati inferiori non richiedono una finitura con superficie liscia."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Angolo del riparo materiale fuoriuscito"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Distanza del riparo materiale fuoriuscito"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Intervallo ottimale dei rami"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Ottimizzazione sequenza di stampa pareti"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Ottimizzare la sequenza di stampa delle pareti in modo da ridurre il numero di retrazioni e la distanza percorsa. L'abilitazione di questa funzione porta vantaggi per la maggior parte dei pezzi; alcuni possono richiedere un maggior tempo di esecuzione; si consiglia di confrontare i tempi di stampa stimati con e senza ottimizzazione. Scegliendo la funzione brim come tipo di adesione del piano di stampa, il primo strato non viene ottimizzato."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Diametro esterno ugello"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Accelerazione parete esterna"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Estrusore parete esterna"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Flusso della parete esterna"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Inserto parete esterna"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Jerk parete esterna"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Larghezza delle linee della parete esterna"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Velocità di stampa della parete esterna"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Distanza del riempimento parete esterna"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Le pareti esterne di diverse isole nello stesso strato vengono stampate in sequenza. Quando abilitata, la quantità di variazione del flusso è limitata perché le pareti vengono stampate un tipo alla volta; quando disabilitata, si riduce il numero di spostamenti tra le isole perché le pareti nello stesso isola sono raggruppate."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "Solo all'esterno"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "Dall'esterno all'interno"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Angolo parete di sbalzo"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Velocità parete di sbalzo"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Le pareti di sbalzo verranno stampate a questa percentuale della loro normale velocità di stampa."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pausa dopo ripristino."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "La velocità della ventola in percentuale da usare durante la stampa delle pareti e del rivestimento esterno ponte."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "La velocità delle ventola in percentuale da usare per stampare il secondo strato del rivestimento esterno ponte."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Percentuale della velocità della ventola da usare quando si stampano le zone del rivestimento esterno subito sopra il supporto. L’uso di una velocità ventola elevata può facilitare la rimozione del supporto."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "La velocità della ventola in percentuale da usare per stampare il terzo strato del rivestimento esterno ponte."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "I poligoni in strati sezionati con una circonferenza inferiore a questo valore verranno scartati. I valori inferiori generano una maglia con risoluzione superiore al costo del tempo di sezionamento. È dedicata in particolare alle stampanti SLA ad alta risoluzione e a modelli 3D molto piccoli, ricchi di dettagli."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Angolo dei rami preferito"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Impedisce la transizione avanti e indietro tra una parete aggiuntiva e una di meno. Questo margine estende l'intervallo di larghezze linea che segue a [Larghezza minima della linea perimetrale - Margine, 2 * Larghezza minima della linea perimetrale + Margine]. Incrementando questo margine si riduce il numero di transizioni, che riduce il numero di avvii/interruzioni estrusione e durata dello spostamento. Tuttavia, variazioni ampie della larghezza della linea possono portare a problemi di sottoestrusione o sovraestrusione."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Accelerazione della torre di innesco"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Base della Torre di Primerizzazione"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Altezza della Base della Torre di Primerizzazione"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Dimensione della Base della Torre di Primerizzazione"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Pendenza della Base della Torre di Primerizzazione"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Flusso torre di innesco"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Jerk della torre di innesco"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Larghezza della linea della torre di innesco"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "Distanza massima tra i rami della prime tower"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Volume minimo torre di innesco"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Interlinea del Radeau della Torre di Primerizzazione"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Dimensioni torre di innesco"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Velocità della torre di innesco"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "Tipi di prime tower"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Posizione X torre di innesco"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Posizione Y torre di innesco"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Accelerazione di stampa"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Jerk stampa"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "Resoconto del processo di stampa"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Sequenza di stampa"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Velocità di stampa"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Stampa pareti sottili"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "Stampa un brim all'esterno, all'interno o in entrambi i punti del modello. A seconda di quest'ultimo, tale scelta consente di ridurre la quantità di brim da rimuovere in seguito, garantendo al contempo una corretta adesione al piano di stampa."

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Stampa una torre accanto alla stampa che serve per innescare il materiale dopo ogni cambio ugello."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Stampare le strutture di riempimento solo laddove è necessario supportare le sommità del modello. L'abilitazione di questa funzione riduce il tempo di stampa e l'utilizzo del materiale, ma comporta una disuniforme resistenza dell'oggetto."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Stampa linee di stiratura in un ordine che ne causa sempre la sovrapposizione con le linee adiacenti in un singola direzione. Questa operazione richiede un tempo di stampa leggermente superiore ma rende l'aspetto delle superfici piane più uniforme."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Stampa i modelli come uno stampo, che può essere fuso per ottenere un modello che assomigli ai modelli sul piano di stampa."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Stampa parti del modello orizzontalmente più sottili delle dimensioni dell'ugello."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "Stampare le linee della superficie della parte superiore del raft in un ordine che fa sì che si sovrappongano sempre alle linee adiacenti in un'unica direzione. Ciò richiede un tempo di stampa leggermente superiore, ma conferisce alla superficie un aspetto più consistente, visibile anche sulla superficie inferiore del modello."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "La velocità di stampa da usare per stampare il secondo strato del rivestimento esterno ponte."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "La velocità di stampa da usare per stampare il terzo strato del rivestimento esterno ponte."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "Limite per la temperatura di stampa"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "Avviso sulla temperatura di stampa"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Stampa il riempimento prima delle pareti. La stampa preliminare delle pareti può avere come risultato pareti più precise, ma sbalzi di stampa peggiori. La stampa preliminare del riempimento produce pareti più robuste, anche se a volte la configurazione (o pattern) di riempimento potrebbe risultare visibile attraverso la superficie."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Stampa linee superficie superiori in un ordine che ne causa sempre la sovrapposizione con le linee adiacenti in un singola direzione. Questa operazione richiede un tempo di stampa leggermente superiore ma rende l'aspetto delle superfici piane più uniforme."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Stampa linee superiori/inferiori in un ordine che ne causa sempre la sovrapposizione con le linee adiacenti in un singola direzione. Questa operazione richiede un tempo di stampa leggermente superiore ma rende l'aspetto delle superfici piane più uniforme."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Temperatura di stampa"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Temperatura di stampa Strato iniziale"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "Stampare la linea più interna dello skirt con più strati facilita la rimozione dello skirt stesso."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Stampa una parete supplementare ogni due strati. In questo modo il riempimento rimane catturato tra queste pareti supplementari, creando stampe più resistenti."

msgctxt "resolution label"
msgid "Quality"
msgstr "Qualità"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Quarto di cubo"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Raft"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Traferro del raft"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "Margine extra della base del raft"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Estrusore della base del raft"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Velocità della ventola per la base del raft"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Spaziatura delle linee dello strato di base del raft"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Larghezza delle linee dello strato di base del raft"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Accelerazione di stampa della base del raft"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Jerk di stampa della base del raft"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Velocità di stampa della base del raft"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "Levigatura della base del raft"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Spessore della base del raft"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Conteggio parete base del raft"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Margine extra del raft"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Velocità della ventola per il raft"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "Margine extra della parte centrale del raft"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Estrusore intermedio del raft"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Velocità della ventola per il raft intermedio"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Strati intermedi del raft"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Larghezza delle linee dello strato intermedio del raft"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Accelerazione di stampa raft intermedio"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Jerk di stampa raft intermedio"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Velocità di stampa raft intermedio"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "Levigatura della parte centrale del raft"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Spaziatura dello strato intermedio del raft"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Spessore dello strato intermedio del raft"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "Conteggio delle pareti della parte centrale del raft"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Accelerazione di stampa del raft"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Jerk stampa del raft"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Velocità di stampa del raft"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Smoothing raft"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "Margine extra della parte superiore del raft"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Estrusore superiore del raft"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Velocità della ventola per la parte superiore del raft"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Spessore dello strato superiore del raft"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Strati superiori del raft"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Larghezza delle linee superiori del raft"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Accelerazione di stampa parte superiore del raft"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Jerk di stampa parte superiore del raft"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Velocità di stampa parte superiore del raft"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "Levigatura della parte superiore del raft"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Spaziatura superiore del raft"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "Conteggio delle parete della parte superiore del raft"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "Conteggio delle pareti del raft"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Casuale"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Avvio con riempimento casuale"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Decidere in modo casuale quale sarà la linea di riempimento ad essere stampata per prima. In tal modo si evita che un segmento diventi il più resistente sebbene si esegua uno spostamento aggiuntivo."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Distorsione (jitter) casuale durante la stampa della parete esterna, così che la superficie assume un aspetto ruvido ed incoerente (fuzzy)."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Rettangolare"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Velocità regolare della ventola"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Velocità regolare della ventola in altezza"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Velocità regolare della ventola in corrispondenza dello strato"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Soglia velocità regolare/massima della ventola"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Estrusione relativa"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Rimozione di tutti i fori"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Rimuovere i primi strati vuoti"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Rimuovi intersezione maglie"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "Rimuovere gli angoli interni dalla base del raft"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Rimuovi angoli interni raft"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "Rimuovere gli angoli interni dalla parte centrale del raft"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "Rimuovere gli angoli interni dalla parte superiore del raft"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Rimuove le aree in cui maglie multiple si sovrappongono tra loro. Questo può essere usato se oggetti di due materiali uniti si sovrappongono tra loro."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Rimuovere gli strati vuoti sotto il primo strato stampato, se presenti. La disabilitazione di questa impostazione può provocare la presenza di primi strati vuoti, se l'impostazione di Tolleranza di sezionamento è impostata su Esclusiva o Intermedia."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "Rimuovere gli angoli interni dalla base del raft, facendolo diventare convesso."

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "Rimuovere gli angoli interni dalla parte centrale del raft, facendolo diventare convesso."

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "Rimuove gli angoli interni dalla parte superiore del raft, facendolo diventare convesso."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Consente di rimuovere angoli interni dal raft, facendolo diventare convesso."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Rimuove i fori presenti su ciascuno strato e mantiene soltanto la forma esterna. Questa funzione ignora qualsiasi invisibile geometria interna. Tuttavia, essa ignora allo stesso modo i fori degli strati visibili da sopra o da sotto."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Sostituisce la parte più esterna della configurazione degli strati superiori/inferiori con una serie di linee concentriche. L’utilizzo di una o due linee migliora le parti superiori (tetti) che iniziano sul materiale di riempimento."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "Resoconto degli eventi che superano le soglie impostate"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Preferenza di appoggio"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Retrazione prima della parete esterna"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Retrazione al cambio strato"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Ritrae il filamento quando l'ugello si sta muovendo su un'area non stampata."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Ritrae il filamento quando l'ugello si sta muovendo su un'area non stampata."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Ritrae il filamento quando l'ugello si sta muovendo allo strato successivo."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Distanza di retrazione"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Entità di innesco supplementare dopo la retrazione"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Distanza minima di retrazione"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Velocità di innesco dopo la retrazione"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Velocità di retrazione"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Velocità di retrazione"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Destra"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Scala la velocità della ventola a 0-1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Scalare la velocità della ventola in modo che sia compresa tra 0 e 1 anziché tra 0 e 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Fattore di scala per la compensazione della contrazione"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "La scena è dotata di maglie di supporto"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Preferenze angolo giunzione"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Imposta manualmente la sequenza di stampa"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Imposta l’altezza del riparo paravento. Scegliere di stampare il riparo paravento all’altezza totale del modello o a un’altezza limitata."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Indica le impostazioni utilizzate per la stampa con estrusori multipli."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Impostazioni utilizzate solo se CuraEngine non è chiamato dalla parte anteriore di Cura."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Retrazione iniziale ugello condivisa"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Angolo più acuto"

msgctxt "shell description"
msgid "Shell"
msgstr "Guscio"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Il più breve"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Mostra varianti macchina"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Layer di supporto del bordo del rivestimento"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Spessore del supporto del bordo del rivestimento"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Distanza prolunga rivestimento esterno"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Sovrapposizione del rivestimento esterno"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Percentuale di sovrapposizione del rivestimento esterno"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Larghezza rimozione rivestimento"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Le aree del rivestimento esterno inferiori a questa non vengono prolungate. In tal modo si evita di prolungare le aree del rivestimento esterno strette che vengono create quando la superficie del modello presenta un’inclinazione quasi verticale."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Salto di una ogni N linee di collegamento per rendere la struttura del supporto più facile da rompere."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Salto di alcuni collegamenti per rendere la struttura del supporto più facile da rompere. Questa impostazione è applicabile alla configurazione a zig-zag del riempimento del supporto."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Skirt"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Distanza dello skirt"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Altezza dello skirt"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Numero di linee dello skirt"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Accelerazione skirt/brim"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Estrusore skirt/brim"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Flusso dello skirt/brim"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Jerk dello skirt/brim"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Larghezza delle linee dello skirt/brim"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Lunghezza minima dello skirt/brim"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Velocità dello skirt/brim"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Tolleranza di sezionamento"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Velocità layer iniziale per dettagli di piccole dimensioni"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Lunghezza massima dettagli di piccole dimensioni"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Velocità dettagli piccole dimensioni"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Dimensione massima foro piccolo"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Temperatura di stampa per piccoli strati"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Piccola area inferiore/superiore sulla superficie"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Larghezza superiore e inferiore delle regioni più piccole"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "I dettagli di piccole dimensioni sul primo layer saranno stampati a questa percentuale della velocità di stampa normale. Una stampa più lenta può aiutare in termini di adesione e precisione."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "I dettagli di piccole dimensioni verranno stampati a questa percentuale della velocità di stampa normale. Una stampa più lenta può aiutare in termini di adesione e precisione."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Le piccole aree superiori/inferiori vengono riempite con muri invece che con il modello superiore/inferiore predefinito. Questo aiuta a evitare movimenti a singhiozzo. Per impostazione predefinita, questa opzione è disattivata per il livello più alto (esposto all'aria) (vedere \"Piccola area inferiore/superiore sulla superficie\")."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Brim smart"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Occultamento intelligente"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Levigazione dei profili con movimento spiraliforme"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Leviga i profili con movimento spiraliforme per ridurre la visibilità della giunzione Z (la giunzione Z dovrebbe essere appena visibile sulla stampa, ma rimane visibile nella visualizzazione a strati). Notare che la levigatura tende a rimuovere le bavature fini della superficie."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Qui è possibile compensare l’eventuale trafilamento di materiale che può verificarsi durante uno spostamento."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Qui è possibile compensare l’eventuale trafilamento di materiale che può verificarsi nel corso della pulitura durante il movimento."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Modalità speciali"

msgctxt "speed description"
msgid "Speed"
msgstr "Velocità"

msgctxt "speed label"
msgid "Speed"
msgstr "Velocità"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Velocità di spostamento dell'asse z durante il sollevamento (Hop)."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Stampa del contorno esterno con movimento spiraliforme"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Appiattisce il contorno esterno attorno all'asse Z con movimento spiraliforme. Questo crea un aumento costante lungo l'asse Z durante tutto il processo di stampa. Questa caratteristica consente di ottenere un modello pieno in una singola stampata con fondo solido. Questa caratteristica deve essere abilitata solo quando ciascuno strato contiene solo una singola parte."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Temperatura di Standby"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "Codice G avvio"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Punto di partenza di ogni percorso nell'ambito di uno strato. Quando i percorsi in strati consecutivi iniziano nello stesso punto, sulla stampa può apparire una linea di giunzione verticale. Se si allineano in prossimità di una posizione specificata dall’utente, la linea di giunzione può essere rimossa più facilmente. Se disposti in modo casuale, le imprecisioni in corrispondenza dell'inizio del percorso saranno meno evidenti. Prendendo il percorso più breve la stampa sarà più veloce."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Passi per millimetro (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Passi per millimetro (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Passi per millimetro (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Passi per millimetro (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Supporto"

msgctxt "support label"
msgid "Support"
msgstr "Supporto"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Accelerazione supporto"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Distanza inferiore supporto"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Numero delle linee perimetrali inferiori di supporto"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Numero di linee del brim del supporto"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Larghezza del brim del supporto"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Conteggio linee di rottura supporto"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Dimensioni frammento supporto"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Densità del supporto"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Priorità distanza supporto"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Estrusore del supporto"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Accelerazione parte inferiore del supporto"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Densità parte inferiore del supporto"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Estrusore parte inferiore del supporto"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Flusso supporto inferiore"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Espansione orizzontale parti inferiori supporto"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Jerk parte inferiore del supporto"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Direzioni della larghezza della linea di supporto inferiore"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Distanza della linea di supporto inferiore"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Larghezza della linea di supporto inferiore"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Configurazione della parte inferiore del supporto"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Velocità di stampa della parte inferiore del supporto"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Spessore parte inferiore del supporto"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Flusso del supporto"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Espansione orizzontale supporto"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Accelerazione riempimento supporto"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Estrusore riempimento del supporto"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Jerk riempimento supporto"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Spessore dello strato di riempimento di supporto"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Direzione delle linee di riempimento supporto"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Velocità di riempimento del supporto"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Accelerazione interfaccia supporto"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Densità interfaccia supporto"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Estrusore interfaccia del supporto"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Flusso interfaccia di supporto"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Espansione orizzontale interfaccia supporto"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Jerk interfaccia supporto"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Direzioni della linea dell'interfaccia di supporto"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Larghezza della linea dell’interfaccia di supporto"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Configurazione interfaccia supporto"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Priorità interfaccia di supporto"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Velocità interfaccia supporto"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Spessore interfaccia supporto"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Numero delle linee perimetrali dell'interfaccia di supporto"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Jerk supporto"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Distanza giunzione supporto"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Distanza tra le linee del supporto"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Larghezza delle linee di supporto"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Supporto maglia"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Angolo di sbalzo del supporto"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Configurazione del supporto"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Posizionamento supporto"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Accelerazione parte superiore del supporto"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Densità parte superiore (tetto) del supporto"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Estrusore parte superiore del supporto"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Flusso supporto superiore"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Espansione orizzontale parti superiori supporto"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Jerk parte superiore del supporto"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Direzioni delle linee di supporto superiori"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Distanza tra le linee della parte superiore (tetto) del supporto"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Larghezza delle linee di supporto superiori"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Configurazione della parte superiore (tetto) del supporto"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Velocità di stampa della parte superiore (tetto) del supporto"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Spessore parte superiore (tetto) del supporto"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Numero delle linee perimetrali del tetto di supporto"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Velocità di stampa del supporto"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Altezza gradini supporto"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Larghezza massima gradino supporto"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Angolo di pendenza minimo gradini supporto"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Struttura di supporto"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Distanza superiore supporto"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Numero delle linee perimetrali supporto"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Distanza X/Y supporto"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Distanza Z supporto"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Linee di supporto preferite"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Supporto preferito"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Velocità della ventola del rivestimento esterno supportato"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Superficie"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Energia superficiale"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Modalità superficie"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Tendenza di adesione superficiale."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Energia superficiale."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Scambia l'ordine di stampa della prima e seconda linea più interna del brim per agevolarne la rimozione."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Selezionare quali volumi di intersezione maglie appartengono a ciascuno strato, in modo che le maglie sovrapposte diventino interconnesse. Disattivando questa funzione una delle maglie ottiene tutto il volume della sovrapposizione, che viene rimosso dalle altre maglie."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Distanza orizzontale target tra due layer adiacenti. Riducendo questa impostazione, i layer più sottili verranno utilizzati per avvicinare i margini dei layer."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "La coordinata X della posizione in prossimità della quale si trova la parte per avviare la stampa di ciascuno strato."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "La coordinata X della posizione in prossimità della quale si innesca all’avvio della stampa di ciascuna parte in uno strato."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "La coordinata X della posizione in cui l’ugello si innesca all’avvio della stampa."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "La coordinata Y della posizione in prossimità della quale si trova la parte per avviare la stampa di ciascuno strato."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "La coordinata Y della posizione in prossimità della quale si innesca all’avvio della stampa di ciascuna parte in uno strato."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "La coordinata Y della posizione in cui l’ugello si innesca all’avvio della stampa."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Indica la coordinata Z della posizione in cui l’ugello si innesca all’avvio della stampa."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Indica l’accelerazione durante la stampa dello strato iniziale."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "Indica l’accelerazione dello strato iniziale."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Indica l’accelerazione degli spostamenti dello strato iniziale."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Indica l’accelerazione degli spostamenti dello strato iniziale."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "Indica l’accelerazione alla quale vengono stampate tutte le pareti interne."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "L’accelerazione con cui viene stampato il riempimento."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "L’accelerazione con cui viene effettuata la stiratura."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "L’accelerazione con cui avviene la stampa."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "Indica l’accelerazione con cui viene stampato lo strato di base del raft."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "Accelerazione alla quale vengono stampate le parti inferiori del supporto. La stampa ad una accelerazione inferiore può migliorare l'adesione del supporto nella parte superiore del modello."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Indica l’accelerazione con cui viene stampato il riempimento del supporto."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "Indica l’accelerazione con cui viene stampato lo strato intermedio del raft."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "Indica l’accelerazione alla quale vengono stampate le pareti più esterne."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Indica l’accelerazione con cui viene stampata la torre di innesco."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "Indica l’accelerazione con cui viene stampato il raft."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Accelerazione alla quale vengono stampate le parti superiori e inferiori del supporto. La loro stampa ad un'accelerazione inferiore può migliorare la qualità dello sbalzo."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Accelerazione alla quale vengono stampate le parti superiori del supporto. La loro stampa ad un'accelerazione inferiore può migliorare la qualità dello sbalzo."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Indica l’accelerazione alla quale sono stampati lo skirt ed il brim. Normalmente questa operazione viene svolta all’accelerazione dello strato iniziale, ma a volte è possibile che si desideri stampare lo skirt o il brim ad un’accelerazione diversa."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Indica l’accelerazione con cui viene stampata la struttura di supporto."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "Indica l’accelerazione alla quale vengono stampati gli strati superiori del raft."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "L'accelerazione con cui vengono stampate le pareti interne della superficie superiore."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "L'accelerazione con cui vengono stampate le pareti più esterne della superficie superiore."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Indica l’accelerazione alla quale vengono stampate le pareti."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "Indica l'accelerazione alla quale vengono stampati gli strati rivestimento superficie superiore."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Indica l’accelerazione alla quale vengono stampati gli strati superiore/inferiore."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Indica l’accelerazione alla quale vengono effettuati gli spostamenti."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "Quantità di materiale, relativo ad una normale linea del rivestimento, da estrudere durante la stiratura. Mantenere l'ugello pieno aiuta a riempire alcune delle fessure presenti sulla superficie superiore, ma una quantità eccessiva comporta un'estrusione eccessiva con conseguente puntinatura sui lati della superficie."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Indica la quantità di sovrapposizione tra il riempimento e le pareti come percentuale della larghezza della linea di riempimento. Una leggera sovrapposizione consente il saldo collegamento delle pareti al riempimento."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Indica la quantità di sovrapposizione tra il riempimento e le pareti. Una leggera sovrapposizione consente il saldo collegamento delle pareti al riempimento."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "Indica il valore di retrazione alla commutazione degli estrusori. Impostato a 0 per nessuna retrazione. Questo valore generalmente dovrebbe essere lo stesso della lunghezza della zona di riscaldamento."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "L’angolo tra il piano orizzontale e la parte conica esattamente sopra la punta dell’ugello."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "L’angolo della parte superiore di una torre. Un valore superiore genera parti superiori appuntite, un valore inferiore, parti superiori piatte."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "Angolo dello sbalzo delle pareti esterne creato per il modello. 0° rende il guscio esterno dello stampo verticale, mentre 90° fa in modo che il guscio esterno dello stampo segua il profilo del modello."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "L’angolo del diametro dei rami con il graduale ispessimento verso il fondo. Un angolo pari a 0 genera rami con spessore uniforme sull’intera lunghezza. Un angolo minimo può aumentare la stabilità del supporto ad albero."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "È l'angolo di inclinazione del supporto conico. Con 0 gradi verticale e 90 gradi orizzontale. Angoli inferiori rendono il supporto più robusto, ma richiedono una maggiore quantità di materiale. Angoli negativi rendono la base del supporto più larga rispetto alla parte superiore."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Indica la densità media dei punti introdotti su ciascun poligono in uno strato. Si noti che i punti originali del poligono vengono scartati, perciò una bassa densità si traduce in una riduzione della risoluzione."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Indica la distanza media tra i punti casuali introdotti su ciascun segmento di linea. Si noti che i punti originali del poligono vengono scartati, perciò un elevato livello di regolarità si traduce in una riduzione della risoluzione. Questo valore deve essere superiore alla metà dello spessore del rivestimento incoerente (fuzzy)."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "La marca del materiale utilizzato."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "Indica l’accelerazione predefinita del movimento della testina di stampa."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "La temperatura preimpostata utilizzata per la stampa. Deve essere la temperatura “base” di un materiale. Tutte le altre temperature di stampa devono usare scostamenti basati su questo valore"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "La temperatura preimpostata utilizzata per il piano di stampa. Deve essere la temperatura “base” di un piano di stampa. Tutte le altre temperature di stampa devono usare scostamenti basati su questo valore"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "La densità dello strato del rivestimento esterno ponte. I valori inferiori a 100 aumentano la distanza tra le linee del rivestimento esterno."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Densità delle parti inferiori della struttura di supporto. Un valore più alto comporta una migliore adesione del supporto alla parte superiore del modello."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Densità delle parti superiori della struttura di supporto. Un valore superiore genera sbalzi migliori, ma i supporti sono più difficili da rimuovere."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "La densità del secondo strato del rivestimento esterno ponte. I valori inferiori a 100 aumentano la distanza tra le linee del rivestimento esterno."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "La densità del terzo strato del rivestimento esterno ponte. I valori inferiori a 100 aumentano la distanza tra le linee del rivestimento esterno."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "La profondità (direzione Y) dell’area stampabile."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Corrisponde al diametro di una torre speciale."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "Il diametro dei rami più sottili del supporto. I rami più spessi sono più resistenti. I rami verso la base avranno spessore maggiore."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "Il diametro della parte superiore della punta dei rami dell'albero di supporto."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "Il diametro della ruota che guida il materiale nel tirafilo."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "Il diametro dei rami più larghi del supporto dell'albero. Un tronco più spesso è più robusto; un tronco più sottile occupa meno spazio sul piano di stampa."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "La differenza in altezza dello strato successivo rispetto al precedente."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "Distanza tra le linee di stiratura."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "La distanza tra l’ugello e le parti già stampate quando si effettua lo spostamento con aggiramento."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Indica la distanza tra le linee che costituiscono lo strato di base del raft. Un'ampia spaziatura favorisce la rimozione del raft dal piano di stampa."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "Indica la distanza fra le linee dello strato intermedio del raft. La spaziatura dello strato intermedio deve essere abbastanza ampia, ma al tempo stesso sufficientemente fitta da sostenere gli strati superiori del raft."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Indica la distanza tra le linee che costituiscono la maglia superiore del raft. La distanza deve essere uguale alla larghezza delle linee, in modo tale da ottenere una superficie solida."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "La distanza tra le linee del radeau per l'unico strato di radeau della torre di primerizzazione. Un ampio interlinea facilita la rimozione del radeau dalla piastra di costruzione."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "La distanza dal confine tra i modelli per generare una struttura a incastro, misurata in celle. Un numero troppo basso di celle determina una scarsa adesione."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Indica la distanza tra il modello e la linea di estremità del brim. Un brim di maggiore dimensione aderirà meglio al piano di stampa, ma con riduzione dell'area di stampa."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "La distanza dall'esterno di un modello in cui non verranno generate strutture a incastro, misurate in celle."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "La distanza dalla punta dell’ugello in cui il calore dall’ugello viene trasferito al filamento."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "Distanza per cui i rivestimenti inferiori si estendono nel riempimento. Valori maggiori migliorano l'aderenza del rivestimento al riempimento e consentono una migliore aderenza al rivestimento delle pareti dello strato inferiore. Valori minori consentono di risparmiare sul materiale utilizzato."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "Distanza per cui i rivestimenti si estendono nel riempimento. Valori maggiori migliorano l'aderenza del rivestimento al riempimento e consentono una migliore aderenza al rivestimento delle pareti degli strati adiacenti. Valori minori consentono di risparmiare sul materiale utilizzato."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "Distanza per cui i rivestimenti superiori si estendono nel riempimento. Valori maggiori migliorano l'aderenza del rivestimento al riempimento e consentono una migliore aderenza al rivestimento delle pareti dello strato superiore. Valori minori consentono di risparmiare sul materiale utilizzato."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "La distanza dello spostamento longitudinale eseguito dalla testina attraverso lo spazzolino."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "I punti finali delle linee di riempimento vengono accorciati per risparmiare sul materiale. Questa impostazione è l'angolo di sbalzo dei punti finali di queste linee."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Indica l'incremento di velocità di raffreddamento dell'ugello in fase di estrusione. Lo stesso valore viene usato per indicare la perdita di velocità di riscaldamento durante il riscaldamento in fase di estrusione."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa del primo strato del riempimento del supporto. Utilizzato nell’estrusione multipla."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa del primo strato del raft. Utilizzato nell’estrusione multipla."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per la stampa delle parti inferiori del supporto. Utilizzato nell’estrusione multipla."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa del riempimento del supporto. Utilizzato nell’estrusione multipla."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa dello strato intermedio del raft. Utilizzato nell’estrusione multipla."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per la stampa delle parti superiori e inferiori del supporto. Utilizzato nell’estrusione multipla."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per la stampa delle parti superiori del supporto. Utilizzato nell’estrusione multipla."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa dello skirt o del brim. Utilizzato nell’estrusione multipla."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa dello skirt/brim/raft. Utilizzato nell’estrusione multipla."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa del supporto. Utilizzato nell’estrusione multipla."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "Il treno estrusore utilizzato per la stampa degli strati superiori del raft. Utilizzato nell’estrusione multipla."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare il riempimento. Si utilizza nell'estrusione multipla."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare le pareti interne. Si utilizza nell'estrusione multipla."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare la parete esterna. Si utilizza nell'estrusione multipla."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare il rivestimento superiore e quello inferiore. Si utilizza nell'estrusione multipla."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare il rivestimento più in alto. Si utilizza nell'estrusione multipla."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Treno estrusore utilizzato per stampare le pareti. Si utilizza nell'estrusione multipla."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "Indica la velocità di rotazione della ventola per lo strato di base del raft."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "Indica la velocità di rotazione della ventola per gli strati intermedi del raft."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Indica la velocità di rotazione della ventola per il raft."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "Indica la velocità di rotazione della ventola per gli strati superiori del raft."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "La posizione del file di un'immagine i cui i valori di luminosità determinano la densità minima nella posizione corrispondente nel riempimento della stampa."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "La posizione del file di un'immagine i cui i valori di luminosità determinano la densità minima nella posizione corrispondente nel supporto."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "I primi strati vengono stampati più lentamente rispetto al resto del modello, per ottenere una migliore adesione al piano di stampa ed ottimizzare nel complesso la percentuale di successo delle stampe. La velocità aumenta gradualmente nel corso di esecuzione degli strati successivi."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "È l'interstizio tra lo strato di raft finale ed il primo strato del modello. Solo il primo strato viene sollevato di questo valore per ridurre l'adesione fra lo strato di raft e il modello. Ciò rende più facile rimuovere il raft."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "L’altezza (direzione Z) dell’area stampabile."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "Altezza sopra le parti orizzontali del modello che stampano lo stampo."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Indica l’altezza alla quale la ventola ruota alla velocità regolare. Agli strati stampati a velocità inferiore la velocità della ventola aumenta gradualmente dalla velocità iniziale a quella regolare."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "La differenza di altezza tra la punta dell’ugello e il sistema gantry (assy X e Y)."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "La differenza di altezza tra la punta dell’ugello e la parte inferiore della testina di stampa."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "La differenza di altezza durante l'esecuzione di uno Z Hop dopo il cambio dell'estrusore."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "La differenza di altezza durante l’esecuzione di uno Z Hop."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "La differenza di altezza durante l’esecuzione di uno Z Hop."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Indica l’altezza di ciascuno strato in mm. Valori più elevati generano stampe più rapide con risoluzione inferiore, valori più bassi generano stampe più lente con risoluzione superiore."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Indica l’altezza di riempimento di una data densità prima di passare a metà densità."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "Indica l’altezza di riempimento del supporto di una data densità prima di passare a metà densità."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "L'altezza delle travi della struttura a incastro, misurata in numero di strati. Un numero minore di strati è più forte, ma più soggetto a difetti."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "L'altezza delle travi della struttura a incastro, misurata in numero di strati. Un numero minore di strati è più forte, ma più soggetto a difetti."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "Indica l’altezza dello strato iniziale in mm. Uno strato iniziale più spesso facilita l’adesione al piano di stampa."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "L'altezza della base della torre di primerizzazione. Aumentando questo valore si otterrà una torre di primerizzazione più robusta perché la base sarà più ampia. Se questa impostazione è troppo bassa, la torre di primerizzazione non avrà una base solida."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "Altezza dei gradini della parte inferiore del supporto a scala che appoggia sul modello. Un valore inferiore rende il supporto più difficile da rimuovere, ma valori troppo elevati possono rendere instabili le strutture di supporto. Impostare a zero per disabilitare il profilo a scala."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "Distanza orizzontale tra la linea del primo brim e il profilo del primo layer della stampa. Un piccolo interstizio può semplificare la rimozione del brim e allo stesso tempo fornire dei vantaggi termici."

msgctxt "skirt_gap description"
msgid "The horizontal distance between the skirt and the first layer of the print.\nThis is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr "Indica la distanza orizzontale tra lo skirt ed il primo strato della stampa."
"Questa è la distanza minima. Più linee di skirt aumenteranno tale distanza."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Le linee di riempimento vengono raddrizzate per risparmiare sul tempo di stampa. Questo è l'angolo di sbalzo massimo consentito sulla lunghezza della linea di riempimento."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "Il riempimento si sposta di questa distanza lungo l'asse X."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "Il riempimento si sposta di questa distanza lungo l'asse Y."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Il diametro interno dell’ugello. Modificare questa impostazione quando si utilizza una dimensione ugello non standard."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "Indica il jerk con cui viene stampato lo strato di base del raft."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "Indica il jerk con cui viene stampato lo strato intermedio del raft."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "Indica il jerk con cui viene stampato il raft."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "Indica il jerk al quale vengono stampati gli strati superiori del raft."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Larghezza massima delle aree di rivestimento inferiore che è possibile rimuovere. Ogni area di rivestimento più piccola di questo valore verrà eliminata. Questo può aiutare a limitare il tempo e il materiale necessari per la stampa del rivestimento inferiore sulle superfici inclinate del modello."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Larghezza massima delle aree di rivestimento che è possibile rimuovere. Ogni area di rivestimento più piccola di questo valore verrà eliminata. Questo può aiutare a limitare il tempo e il materiale necessari per la stampa del rivestimento superiore/inferiore sulle superfici inclinate del modello."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Larghezza massima delle aree di rivestimento superiore che è possibile rimuovere. Ogni area di rivestimento più piccola di questo valore verrà eliminata. Questo può aiutare a limitare il tempo e il materiale necessari per la stampa del rivestimento superiore sulle superfici inclinate del modello."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Indica lo strato in corrispondenza del quale la ventola ruota alla velocità regolare. Se è impostata la velocità regolare in altezza, questo valore viene calcolato e arrotondato a un numero intero."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Indica il tempo per strato che definisce la soglia tra la velocità regolare e quella massima della ventola. Gli strati che vengono stampati a una velocità inferiore a questo valore utilizzano una velocità regolare della ventola. Per gli strati stampati più velocemente la velocità della ventola aumenta gradualmente verso la velocità massima della ventola."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "La lunghezza del materiale retratto durante il movimento di retrazione."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "Il fattore di magnitudo usato per la pendenza della base della torre di primerizzazione. Aumentando questo valore, la base diventerà più sottile. Diminuendolo, la base diventerà più spessa."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Il materiale del piano di stampa installato sulla stampante."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "La differenza di altezza massima rispetto all’altezza dello strato di base."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "È l'angolazione massima ammessa delle parti nel riparo. Con 0 gradi verticale e 90 gradi orizzontale. Un angolo più piccolo comporta minori ripari non riusciti, ma maggiore materiale."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "L’angolo massimo degli sbalzi dopo essere stati resi stampabili. A un valore di 0° tutti gli sbalzi sono sostituiti da un pezzo del modello collegato al piano di stampa, 90° non cambia il modello in alcun modo."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "Angolo massimo dei rami mentre crescono intorno al modello. Usa un angolo inferiore per renderli più verticali e più stabili. Usa un angolo più alto per ottenere una portata maggiore."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "L'area massima di un foro nella base del modello prima che venga rimossa da Rendi stampabile lo sbalzo.  I fori più piccoli di questo verranno mantenuti.  Un valore di 0 mm² riempirà i fori nella base del modello."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "La deviazione massima consentita quando si riduce la risoluzione per l'impostazione Risoluzione massima. Se si aumenta questo parametro, la stampa sarà meno precisa, ma il g-code sarà più piccolo. Deviazione massima rappresenta il limite per Risoluzione massima; pertanto se le due impostazioni sono in conflitto, verrà considerata vera l'impostazione Deviazione massima."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "La distanza massima tra le strutture di supporto nelle direzioni X/Y. Quando la distanza tra le strutture è inferiore al valore indicato, le strutture convergono in una unica."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "Distanza massima in mm di spostamento del filamento per compensare le modifiche nella velocità di flusso."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "La deviazione massima dell'area di estrusione consentita durante la rimozione di punti intermedi da una linea retta. Un punto intermedio può fungere da punto di modifica larghezza in una lunga linea retta. Pertanto, se viene rimosso, la linea avrà una larghezza uniforme e, come risultato, perderà (o guadagnerà) area di estrusione. In caso di incremento si può notare una leggera sotto (o sovra) estrusione tra pareti parallele rette, poiché sarà possibile rimuovere più punti di variazione della larghezza intermedi. La stampa sarà meno precisa, ma il G-Code sarà più piccolo."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "Indica il cambio della velocità istantanea massima durante la stampa dello strato iniziale."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Indica il cambio della velocità istantanea massima della testina di stampa."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Indica la variazione della velocità istantanea massima durante la stiratura."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono stampate tutte le pareti interne."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Indica il cambio della velocità istantanea massima con cui viene stampato il riempimento."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Indica la variazione della velocità istantanea massima con cui vengono stampate le parti inferiori."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Indica il cambio della velocità istantanea massima con cui viene stampato il riempimento del supporto."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono stampate le pareti più esterne."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "Indica il cambio della velocità istantanea massima con cui viene stampata la torre di innesco del supporto."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Indica la variazione della velocità istantanea massima con cui vengono stampate le parti superiori e inferiori."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Indica la variazione della velocità istantanea massima con cui vengono stampate le parti superiori."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono stampati lo skirt e il brim."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Indica il cambio della velocità istantanea massima con cui viene stampata la struttura del supporto."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "La massima variazione istantanea di velocità con cui vengono stampate le pareti più esterne della superficie superiore."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "La massima variazione istantanea di velocità con cui vengono stampate le pareti interne della superficie superiore."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono stampate le pareti."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Indica la variazione di velocità istantanea massima con cui vengono stampati gli strati rivestimento superficie superiore."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono stampati gli strati superiore/inferiore."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Indica il cambio della velocità istantanea massima con cui vengono effettuati gli spostamenti."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "La lunghezza massima dei rami stampabili a mezz'aria."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "Indica la velocità massima del motore per la direzione X."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Indica la velocità massima del motore per la direzione Y."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Indica la velocità massima del motore per la direzione Z."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "Indica la velocità massima del filamento."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "Larghezza massima dei gradini della parte inferiore del supporto a scala che appoggia sul modello. Un valore inferiore rende il supporto più difficile da rimuovere, ma valori troppo elevati possono rendere instabili le strutture di supporto."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "Distanza minima tra l'esterno dello stampo e l'esterno del modello."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Indica la velocità di spostamento minima della testina di stampa."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "La temperatura minima durante il riscaldamento fino alla temperatura alla quale può già iniziare la stampa."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Il tempo minimo in cui un estrusore deve essere inattivo prima che l’ugello si raffreddi. Solo quando un estrusore non è utilizzato per un periodo superiore a questo tempo potrà raffreddarsi alla temperatura di standby."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "L'angolo minimo degli sbalzi interni per il quale viene aggiunto il riempimento. Per un valore corrispondente a 0°, gli oggetti sono completamente riempiti di materiale, per un valore corrispondente a 90° non è previsto riempimento."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "Indica l’angolo minimo degli sbalzi per i quali viene aggiunto il supporto. A un valore di 0 ° tutti gli sbalzi vengono supportati, con un valore di 90 ° non sarà fornito alcun supporto."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Determina la distanza minima necessaria affinché avvenga una retrazione. Questo consente di avere un minor numero di retrazioni in piccole aree."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "Indica la lunghezza minima dello skirt o del brim. Se tale lunghezza minima non viene raggiunta da tutte le linee skirt o brim insieme, saranno aggiunte più linee di skirt o brim fino a raggiungere la lunghezza minima. Nota: se il valore è impostato a 0, questa funzione viene ignorata."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "La larghezza minima della linea per pareti polilinea di riempimento interstizi linea intermedia. Questa impostazione determina lo spessore modello in corrispondenza del quale si passa dalla stampa di due linee perimetrali alla stampa di due pareti esterne e di una singola parete centrale al centro. Una larghezza minima della linea perimetrale pari più elevata porta a una larghezza massima della linea perimetrale dispari più elevata. La larghezza massima della linea di parete dispari viene calcolata come 2 * Larghezza minima della linea di parete pari."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "La larghezza minima della linea per normali pareti poligonali. Questa impostazione determina lo spessore modello in corrispondenza del quale si passa dalla stampa di una singola linea perimetrale sottile alla stampa di due linee perimetrali. Una larghezza minima della linea perimetrale pari più elevata porta a una larghezza massima della linea perimetrale dispari più elevata. La larghezza massima della linea perimetrale pari viene calcolata come Larghezza della linea perimetrale esterna + 0,5 * Larghezza minima della linea perimetrale dispari."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Indica la velocità minima di stampa, a prescindere dal rallentamento per il tempo minimo per strato. Quando la stampante rallenta eccessivamente, la pressione nell’ugello risulta insufficiente con conseguente scarsa qualità di stampa."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "La dimensione minima di un segmento di linea dopo il sezionamento. Se tale dimensione aumenta, la maglia avrà una risoluzione inferiore. Questo può consentire alla stampante di mantenere la velocità per processare il g-code ed aumenterà la velocità di sezionamento eliminando i dettagli della maglia che non è comunque in grado di processare."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "La dimensione minima di un segmento lineare di spostamento dopo il sezionamento. Aumentando tale dimensione, le corse di spostamento avranno meno angoli arrotondati. La stampante può così mantenere la velocità per processare il g-code, ma si può verificare una riduzione della precisione di aggiramento del modello."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "La pendenza minima dell'area alla quale ha effetto la creazione dei gradini. Valori bassi dovrebbero semplificare la rimozione del supporto sulle pendenze meno profonde, ma in realtà dei valori bassi possono generare risultati molto irrazionali sulle altre parti del modello."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Indica il tempo minimo dedicato a uno strato. Questo forza la stampante a rallentare, per impiegare almeno il tempo impostato qui per uno strato. Questo consente il corretto raffreddamento del materiale stampato prima di procedere alla stampa dello strato successivo. La stampa degli strati potrebbe richiedere un tempo inferiore al minimo se la funzione Sollevamento della testina è disabilitata e se la velocità minima non viene rispettata."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Il volume minimo per ciascuno strato della torre di innesco per scaricare materiale a sufficienza."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "Il diametro massimo di un ramo che deve collegarsi al modello può aumentare unendosi ai rami che potrebbero raggiungere il piano di stampa. L'aumento riduce il tempo di stampa, ma aumenta l'area di supporto che poggia sul modello"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "Il nome del modello della stampante 3D in uso."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "ID ugello per un treno estrusore, come \"AA 0.4\" e \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "Durante lo spostamento l’ugello evita le parti già stampate. Questa opzione è disponibile solo quando è abilitata la funzione Combing."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "Durante lo spostamento l'ugello evita i supporti già stampati. Questa opzione è disponibile solo quando è abilitata la funzione combing."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Indica il numero degli strati inferiori. Quando calcolato mediante lo spessore dello strato inferiore, il valore viene arrotondato a numero intero."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "Il numero di contorni da stampare intorno alla configurazione lineare nello strato di base del raft."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "Il numero di contorni da stampare intorno al modello lineare negli strati centrali del raft."

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "Il numero di contorni da stampare intorno al modello lineare negli strati superiori del raft."

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "Il numero di contorni da stampare intorno al modello lineare del raft."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "Numero di layer di riempimento che supportano i bordi del rivestimento."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Il numero di layer inferiori iniziali, dal piano di stampa verso l'alto. Quando viene calcolato mediante lo spessore inferiore, questo valore viene arrotondato a un numero intero."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Il numero di strati tra la base e la superficie del raft. Questi costituiscono lo spessore principale del raft. L'incremento di questo numero crea un raft più spesso e robusto."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Corrisponde al numero di linee utilizzate per un brim. Più linee brim migliorano l’adesione al piano di stampa, ma con riduzione dell'area di stampa."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Corrisponde al numero di linee utilizzate per il brim del supporto. Più linee brim migliorano l’adesione al piano di stampa, ma utilizzano una maggiore quantità di materiale."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "Numero di strati sulla parte superiore del secondo strato del raft. Si tratta di strati completamente riempiti su cui poggia il modello. 2 strati danno come risultato una superficie superiore più levigata rispetto ad 1 solo strato."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Indica il numero degli strati superiori. Quando calcolato mediante lo spessore dello strato superiore, il valore viene arrotondato a numero intero."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "Numero degli strati di rivestimento superiori. Solitamente è sufficiente un unico strato di sommità per ottenere superfici superiori di qualità elevata."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Il numero di pareti circostanti il riempimento di supporto. L'aggiunta di una parete può rendere la stampa del supporto più affidabile ed in grado di supportare meglio gli sbalzi, ma aumenta il tempo di stampa ed il materiale utilizzato."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Il numero di pareti con cui circondare il pavimento dell'interfaccia di supporto. L'aggiunta di una parete può rendere la stampa di supporto più affidabile e può supportare meglio gli sbalzi, ma aumenta il tempo di stampa e il materiale utilizzato."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Il numero di pareti con cui circondare il tetto dell'interfaccia di supporto. L'aggiunta di una parete può rendere la stampa di supporto più affidabile e può supportare meglio gli sbalzi, ma aumenta il tempo di stampa e il materiale utilizzato."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Il numero di pareti con cui circondare l'interfaccia di supporto. L'aggiunta di una parete può rendere la stampa di supporto più affidabile e può supportare meglio gli sbalzi, ma aumenta il tempo di stampa e il materiale utilizzato."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "Il numero di pareti, conteggiate dal centro, su cui occorre distribuire la variazione. Valori più bassi indicano che la larghezza delle pareti esterne non cambia."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Indica il numero delle pareti. Quando calcolato mediante lo spessore della parete, il valore viene arrotondato a numero intero."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Il diametro esterno della punta dell'ugello."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Configurazione del materiale di riempimento della stampa. Il riempimento a linea e a zig zag cambia direzione su strati alternati, riducendo il costo del materiale. Le configurazioni a griglia, a triangolo, tri-esagonali, cubiche, ottagonali, a quarto di cubo, incrociate e concentriche sono stampate completamente su ogni strato. Le configurazioni gyroid, cubiche, a quarto di cubo e ottagonali variano per ciascuno strato per garantire una più uniforme distribuzione della forza in ogni direzione. Il riempimento fulmine cerca di minimizzare il riempimento, supportando solo la parte superiore dell'oggetto."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Indica la configurazione delle strutture di supporto della stampa. Le diverse opzioni disponibili generano un supporto robusto o facile da rimuovere."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "Configurazione degli strati superiori."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Indica la configurazione degli strati superiori/inferiori."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "La configurazione al fondo della stampa sul primo strato."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Configurazione utilizzata per la stiratura della superficie superiore."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "È la configurazione (o pattern) con cui vengono stampate le parti inferiori del supporto."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "È la configurazione (o pattern) con cui viene stampata l’interfaccia del supporto con il modello."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "È la configurazione (o pattern) con cui vengono stampate le parti superiori del supporto."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "La posizione accanto al punto in cui avviare la stampa di ciascuna parte in uno layer."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "L'angolo dei rami preferito, quando questi non devono evitare il modello. Usa un angolo inferiore per renderli più verticali e più stabili. Usa un angolo più alto per unire più velocemente i rami."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "Il posizionamento preferito delle strutture di supporto. Se le strutture non si possono collocare nella posizione preferita, saranno collocate altrove, anche se ciò significherà posizionarle sul modello."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "Indica il cambio della velocità istantanea massima dello strato iniziale."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "La forma del piano di stampa senza tenere conto delle aree non stampabili."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "La forma della testina di stampa. Queste sono le coordinate relative alla posizione della testina di stampa. Questa coincide in genere con la posizione del primo estrusore. Le posizioni a sinistra e davanti alla testina di stampa devono essere coordinate negative."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Dimensioni delle cavità negli incroci a quattro vie nella configurazione 3D incrociata alle altezze a cui la configurazione tocca se stessa."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "È il volume minimo di un percorso di estrusione prima di consentire il coasting. Per percorsi di estrusione inferiori, nel tubo Bowden si è accumulata una pressione inferiore, quindi il volume rilasciato si riduce in modo lineare. Questo valore dovrebbe essere sempre maggiore del volume di Coasting."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "La velocità (°C/s) alla quale l’ugello si raffredda calcolando la media sulla gamma di temperature di stampa normale e la temperatura di attesa."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "La velocità (°C/s) alla quale l’ugello si riscalda calcolando la media sulla gamma di temperature di stampa normale e la temperatura di attesa."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Indica la velocità alla quale vengono stampate tutte le pareti interne. La stampa della parete interna eseguita più velocemente di quella della parete esterna consentirà di ridurre il tempo di stampa. Si consiglia di impostare questo parametro ad un valore intermedio tra la velocità della parete esterna e quella di riempimento."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "Indica la velocità alla quale vengono stampate le zone di rivestimento esterno del ponte."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Indica la velocità alla quale viene stampato il riempimento."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Indica la velocità alla quale viene effettuata la stampa."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Indica la velocità alla quale viene stampata la base del raft. La sua stampa deve avvenire molto lentamente, considerato che il volume di materiale che fuoriesce dall'ugello è piuttosto elevato."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Indica la velocità alla quale vengono stampate le pareti ponte."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "La velocità di rotazione della ventola all’inizio della stampa. Negli strati successivi la velocità della ventola aumenta gradualmente da zero fino allo strato corrispondente alla velocità regolare in altezza."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Indica la velocità alla quale ruotano le ventole prima di raggiungere la soglia. Quando uno strato viene stampato a una velocità superiore alla soglia, la velocità della ventola tende gradualmente verso la velocità massima della ventola."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Indica la velocità di rotazione della ventola al tempo minimo per strato. La velocità della ventola aumenta gradualmente tra la velocità regolare della ventola e la velocità massima della ventola quando viene raggiunta la soglia."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "Indica la velocità alla quale il filamento viene preparato durante un movimento di retrazione."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "Indica la velocità alla quale il filamento viene preparato durante un movimento di retrazione per pulitura."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Indica la velocità alla quale il filamento viene sospinto indietro dopo la retrazione per cambio ugello."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "Indica la velocità alla quale il filamento viene retratto e preparato durante un movimento di retrazione."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "Indica la velocità alla quale il filamento viene retratto e preparato durante un movimento di retrazione per pulitura."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Indica la velocità alla quale il filamento viene retratto durante una retrazione per cambio ugello."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Indica la velocità alla quale il filamento viene retratto durante un movimento di retrazione."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "Indica la velocità alla quale il filamento viene retratto durante un movimento di retrazione per pulitura."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Indica la velocità di retrazione del filamento. Una maggiore velocità di retrazione funziona bene, ma una velocità di retrazione eccessiva può portare alla deformazione del filamento."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "Velocità alla quale viene stampata la parte inferiore del supporto. La stampa ad una velocità inferiore può migliorare l'adesione del supporto nella parte superiore del modello."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "Indica la velocità alla quale viene stampato il riempimento del supporto. La stampa del riempimento a velocità inferiori migliora la stabilità."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Indica la velocità alla quale viene stampato lo strato intermedio del raft. La sua stampa deve avvenire molto lentamente, considerato che il volume di materiale che fuoriesce dall'ugello è piuttosto elevato."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "Indica la velocità alla quale vengono stampate le pareti più esterne. La stampa della parete esterna ad una velocità inferiore migliora la qualità finale del rivestimento. Tuttavia, una grande differenza tra la velocità di stampa della parete interna e quella della parete esterna avrà effetti negativi sulla qualità."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "Indica la velocità alla quale è stampata la torre di innesco. La stampa della torre di innesco a una velocità inferiore può renderla maggiormente stabile quando l’adesione tra i diversi filamenti non è ottimale."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Indica la velocità di rotazione delle ventole di raffreddamento stampa."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "Indica la velocità alla quale il raft è stampato."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Velocità alla quale vengono stampate le parti superiori e inferiori del supporto. La loro stampa a velocità inferiori può migliorare la qualità dello sbalzo."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Velocità alla quale vengono stampate le parti superiori del supporto. La loro stampa a velocità inferiori può migliorare la qualità dello sbalzo."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Indica la velocità a cui sono stampati lo skirt ed il brim. Normalmente questa operazione viene svolta alla velocità di stampa dello strato iniziale, ma a volte è possibile che si desideri stampare lo skirt o il brim ad una velocità diversa."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "Indica la velocità alla quale viene stampata la struttura di supporto. La stampa della struttura di supporto a velocità elevate può ridurre considerevolmente i tempi di stampa. La qualità superficiale della struttura di supporto di norma non riveste grande importanza in quanto viene rimossa dopo la stampa."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Indica la velocità alla quale sono stampati gli strati superiori del raft. La stampa di questi strati deve avvenire un po' più lentamente, in modo da consentire all'ugello di levigare lentamente le linee superficiali adiacenti."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "La velocità con cui vengono stampate le pareti interne della superficie superiore."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "La velocità con cui vengono stampate le pareti più esterne della superficie superiore."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Velocità alla quale viene eseguito il movimento Z verticale per i sollevamenti in Z. In genere è inferiore alla velocità di stampa, dal momento che il piano o il corpo di stampa della macchina sono più difficili da spostare."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Indica la velocità alla quale vengono stampate le pareti."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "Velocità alla quale passare sopra la superficie superiore."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "La velocità alla quale retrarre il filamento al fine di romperlo in modo netto."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "Indica la velocità di stampa degli strati superiori."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Indica la velocità alla quale vengono stampati gli strati superiore/inferiore."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Indica la velocità alla quale vengono effettuati gli spostamenti."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "È la velocità a cui eseguire lo spostamento durante il Coasting, rispetto alla velocità del percorso di estrusione. Si consiglia di impostare un valore leggermente al di sotto del 100%, poiché durante il Coasting la pressione nel tubo Bowden scende."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "La velocità dello strato iniziale. È consigliabile un valore inferiore per migliorare l'adesione al piano di stampa. Non influisce sulle strutture di adesione del piano di stampa stesse, come brim e raft."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "Indica la velocità di stampa per lo strato iniziale. Un valore inferiore è consigliabile per migliorare l’adesione al piano di stampa."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "Indica la velocità di spostamento per lo strato iniziale. Un valore inferiore è consigliabile per evitare di rimuovere le parti precedentemente stampate dal piano di stampa. Il valore di questa impostazione può essere calcolato automaticamente dal rapporto tra la velocità di spostamento e la velocità di stampa."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "La temperatura a cui il filamento viene rotto, con una rottura netta."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "La temperatura dell'ambiente in cui stampare. Se il valore è 0, la temperatura del volume di stampa non verrà regolata."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Indica la temperatura dell'ugello quando un altro ugello è attualmente in uso per la stampa."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "La temperatura alla quale può già iniziare il raffreddamento prima della fine della stampa."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "La temperatura utilizzata per la stampa del primo strato."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Indica la temperatura usata per la stampa."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "Indica la temperatura usata per il piano di stampa riscaldato per il primo strato. Se questo valore è 0, il piano di stampa viene lasciato non riscaldato per il primo strato."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "Indica la temperatura utilizzata per il piano di stampa riscaldato. Se questo valore è 0, il piano di stampa viene lasciato non riscaldato."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "La temperatura utilizzata per scaricare il materiale. deve essere più o meno uguale alla massima temperatura di stampa possibile."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Indica lo spessore degli strati inferiori nella stampa. Questo valore diviso per la l’altezza dello strato definisce il numero degli strati inferiori."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "Spessore del riempimento supplementare che supporta i bordi del rivestimento."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "Indica lo spessore dell’interfaccia del supporto dove tocca il modello nella parte inferiore o in quella superiore."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "Indica lo spessore delle parti inferiori del supporto. Questo controlla il numero di strati fitti stampati sulla sommità dei punti di un modello su cui appoggia un supporto."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "Lo spessore delle parti superiori del supporto. Questo controlla la quantità di strati fitti alla sommità del supporto su cui appoggia il modello."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "Indica lo spessore degli strati superiori nella stampa. Questo valore diviso per la l’altezza dello strato definisce il numero degli strati superiori."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Indica lo spessore degli strati superiore/inferiore nella stampa. Questo valore diviso per la l’altezza dello strato definisce il numero degli strati superiori/inferiori."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Spessore delle pareti in direzione orizzontale. Questo valore diviso per la larghezza della linea della parete definisce il numero di pareti."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Indica lo spessore per strato di materiale di riempimento. Questo valore deve sempre essere un multiplo dell’altezza dello strato e in caso contrario viene arrotondato."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Indica lo spessore per strato del materiale di riempimento del supporto. Questo valore deve sempre essere un multiplo dell’altezza dello strato e in caso contrario viene arrotondato."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "Il tipo di codice G da generare."

msgctxt "material_type description"
msgid "The type of material used."
msgstr "Il tipo di materiale utilizzato."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "È il volume di materiale fuoriuscito. Questo valore deve di norma essere prossimo al diametro dell'ugello al cubo."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "La larghezza (direzione X) dell’area stampabile."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "Corrisponde alla larghezza del brim da stampare al di sotto del supporto. Un brim più largo migliora l’adesione al piano di stampa, ma utilizza una maggiore quantità di materiale."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "La larghezza delle travi della struttura ad incastro."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "La larghezza del bordo/base della torre di primerizzazione. Una base più ampia migliora l'aderenza alla piastra di costruzione, ma riduce anche l'area di stampa effettiva."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "Indica la larghezza della torre di innesco."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Indica la larghezza entro cui è ammessa la distorsione (jitter). Si consiglia di impostare questo valore ad un livello inferiore rispetto alla larghezza della parete esterna, poiché le pareti interne rimangono inalterate."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "La finestra in cui è impostato il massimo numero di retrazioni. Questo valore deve corrispondere all'incirca alla distanza di retrazione, in modo da limitare effettivamente il numero di volte che una retrazione interessa lo stesso spezzone di materiale."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "Indica la coordinata X della posizione della torre di innesco."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "Indica la coordinata Y della posizione della torre di innesco."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Nella scena sono presenti maglie di supporto. Questa impostazione è controllata da Cura."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Questo comanda la distanza che l’estrusore deve percorrere in coasting immediatamente dopo l’inizio di una parete ponte. Il coasting prima dell’inizio del ponte può ridurre la pressione nell’ugello e generare un ponte più piatto."

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Questa impostazione controlla la quantità di arrotondamento degli angoli interni nel contorno della base del raft. Gli angoli interni sono arrotondati a semicerchio con un raggio pari al valore indicato. Questa impostazione rimuove anche i buchi nel contorno del raft che sono più piccoli di tale cerchio."

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Questa impostazione controlla la quantità di arrotondamento degli angoli interni del contorno della parte centrale del raft. Gli angoli interni sono arrotondati a semicerchio con un raggio pari al valore indicato. Questa impostazione rimuove anche i buchi nel contorno del raft che sono più piccoli di tale cerchio."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Questa impostazione controlla l'entità dell'arrotondamento degli angoli interni sul profilo raft. Gli angoli interni vengono arrotondati a semicerchio con un raggio pari al valore indicato. Questa impostazione elimina inoltre i fori sul profilo raft più piccoli di tale cerchio."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Questa impostazione controlla la quantità di arrotondamento degli angoli interni del contorno della parte superiore del raft. Gli angoli interni sono arrotondati a semicerchio con un raggio pari al valore indicato. Questa impostazione rimuove anche i buchi nel contorno del raft che sono più piccoli di tale cerchio."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Questa impostazione limita il numero di retrazioni previste all'interno della finestra di minima distanza di estrusione. Ulteriori retrazioni nell'ambito di questa finestra saranno ignorate. Questo evita di eseguire ripetute retrazioni sullo stesso pezzo di filamento, onde evitarne l'appiattimento e conseguenti problemi di deformazione."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "In tal modo si creerà una protezione attorno al modello che intrappola l'aria (calda) e lo protegge da flussi d’aria esterna. Particolarmente utile per i materiali soggetti a deformazione."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Diametro della punta"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Per compensare la contrazione del materiale durante il raffreddamento, il modello sarà scalato con questo fattore nella direzione XY (orizzontalmente)."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Per compensare la contrazione del materiale durante il raffreddamento, il modello sarà scalato con questo fattore nella direzione Z (verticalmente)."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Per compensare la contrazione del materiale durante il raffreddamento, il modello sarà scalato con questo fattore."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Strati superiori"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Distanza prolunga rivestimento superiore"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Larghezza rimozione rivestimento superiore"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Accelerazione della parete interna della superficie superiore"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Jerk parete esterna della superficie superiore"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Velocità di stampa della parete interna della superficie superiore"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Flusso della parete interna della superficie superiore"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Accelerazione della parete esterna della superficie superiore"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Flusso della parete esterna della superficie superiore"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Jerk parete interna della superficie superiore"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Velocità di stampa della parete esterna della superficie superiore"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Accelerazione del rivestimento superficie superiore"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Estrusore rivestimento superficie superiore"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Flusso rivestimento esterno superficie superiore"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Jerk del rivestimento superficie superiore"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Strati di rivestimento superficie superiore"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Direzioni linea rivestimento superficie superiore"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Larghezza linea rivestimento superficie superiore"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Configurazione del rivestimento superficie superiore"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Velocità del rivestimento superficie"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Spessore dello strato superiore"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Nelle superfici superiore e/o inferiore dell'oggetto con un angolo più grande di questa impostazione, il rivestimento esterno non sarà prolungato. Questo evita il prolungamento delle aree del rivestimento esterno strette che vengono create quando la pendenza della superficie del modello è quasi verticale. Un angolo di 0° è orizzontale e non causa il prolungamento di alcun rivestimento esterno, mentre un angolo di 90° è verticale e causa il prolungamento di tutto il rivestimento esterno."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Superiore / Inferiore"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Superiore / Inferiore"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Accelerazione strato superiore/inferiore"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Estrusore superiore/inferiore"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Flusso superiore/inferiore"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Jerk strato superiore/inferiore"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Direzioni delle linee superiori/inferiori"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Larghezza delle linee superiore/inferiore"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Configurazione dello strato superiore/inferiore"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Velocità di stampa delle parti superiore/inferiore"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Spessore dello strato superiore/inferiore"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Contatto con il Piano di Stampa"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Diametro della torre"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Angolazione della parte superiore (tetto) della torre"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Matrice di rotazione da applicare al modello quando caricato dal file."

msgctxt "travel label"
msgid "Travel"
msgstr "Spostamenti"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Accelerazione spostamenti"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Distanza di aggiramento durante gli spostamenti"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Jerk spostamenti"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Velocità degli spostamenti"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Trattare il modello solo come una superficie, un volume o volumi con superfici libere. Il modo di stampa normale stampa solo volumi delimitati. “Superficie” stampa una parete singola tracciando la superficie della maglia senza riempimento e senza rivestimento esterno superiore/inferiore. “Entrambi” stampa i volumi delimitati come normali ed eventuali poligoni rimanenti come superfici."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Albero"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Tri-esagonale"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Triangoli"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Triangoli"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Triangoli"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Triangoli"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Triangoli"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Diametro del tronco"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Unione dei volumi in sovrapposizione"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Le pareti non supportate di lunghezza inferiore a questo valore verranno stampate utilizzando le normali impostazioni parete. Le pareti non supportate di lunghezza superiore verranno stampate utilizzando le impostazioni parete ponte."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Uso di strati adattivi"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Utilizzo delle torri"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Usa una velocità di accelerazione separata per i movimenti di spostamento. Se disabilitato, i movimenti di spostamento utilizzeranno il valore di accelerazione della linea stampata alla destinazione."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Usa un tasso di jerk separato per i movimenti di spostamento. Se disabilitata, i movimenti di spostamento utilizzeranno il valore di jerk della linea stampata alla destinazione."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Utilizza l'estrusione relativa invece di quella assoluta. L'utilizzo di fasi E relative facilita la post-elaborazione del codice G. Tuttavia, questa impostazione non è supportata da tutte le stampanti e può causare deviazioni molto piccole nella quantità di materiale depositato rispetto alle fasi E assolute. Indipendentemente da questa impostazione, la modalità estrusione sarà sempre impostata su assoluta prima che venga generato uno script in codice G."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Utilizza speciali torri per il supporto di piccolissime aree di sbalzo. Queste torri hanno un diametro maggiore rispetto a quello dell'area che supportano. In prossimità dello sbalzo il diametro delle torri diminuisce, formando un 'tetto'."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Utilizzare questa maglia per modificare il riempimento di altre maglie a cui è sovrapposta. Sostituisce le regioni di riempimento di altre maglie con le regioni di questa maglia. Si consiglia di stampare solo una parete e non il rivestimento esterno superiore/inferiore per questa maglia."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Utilizzare questa maglia per specificare le aree di supporto. Può essere usata per generare una struttura di supporto."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Utilizzare questa maglia per specificare dove nessuna parte del modello deve essere rilevata come in sovrapposizione. Può essere usato per rimuovere struttura di supporto indesiderata."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Specificato dall’utente"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Fattore di scala verticale per la compensazione della contrazione"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Tolleranza verticale negli strati sezionati. Di norma i contorni di uno strato vengono generati prendendo le sezioni incrociate fino al centro dello spessore di ciascun livello (intermedie). In alternativa, ogni strato può avere le aree che cadono all'interno del volume per tutto lo spessore dello strato (esclusive) oppure uno strato presenta le aree che rientrano all'interno di qualsiasi punto dello strato (inclusive). Le aree inclusive conservano la maggior parte dei dettagli; le esclusive generano la soluzione migliore, mentre le intermedie restano più vicine alla superficie originale."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Attendi il riscaldamento del piano di stampa"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Attendi il riscaldamento dell’ugello"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Accelerazione parete"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Conteggio distribuzione parete"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Estrusore pareti"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Flusso della parete"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Jerk parete"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Numero delle linee perimetrali"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Larghezza delle linee perimetrali"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Ordinamento parete"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Velocità di stampa della parete"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Spessore delle pareti"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Lunghezza transizione parete"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Distanza di filtro transizione parete"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Margine filtro di transizione parete"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Angolo di soglia di transizione parete"

msgctxt "shell label"
msgid "Walls"
msgstr "Pareti"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "Le pareti con uno sbalzo superiore a quest'angolo saranno stampate con le impostazioni per le pareti a sbalzo. Se il valore è 90, nessuna parete sarà trattata come parete a sbalzo. Nemmeno lo sbalzo supportato dal supporto sarà trattato come tale."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Se questa opzione è abilitata, i percorsi utensile vengono corretti per le stampanti con pianificatori del movimento regolare. I piccoli movimenti che deviano dalla direzione del percorso utensile generale vengono risistemati per migliorare i movimenti del fluido."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Quando abilitato, l’ordine di stampa delle linee di riempimento viene ottimizzato per ridurre la distanza percorsa. La riduzione del tempo di spostamento ottenuta dipende in particolare dal modello sezionato, dalla configurazione di riempimento, dalla densità, ecc. Si noti che, per alcuni modelli che hanno piccole aree di riempimento, il tempo di sezionamento del modello può aumentare notevolmente."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Quando abilitata, la velocità della ventola di raffreddamento stampa viene modificata per le zone del rivestimento esterno subito sopra il supporto."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Se abilitato, le coordinate della giunzione Z sono riferite al centro di ogni parte. Se disabilitato, le coordinate definiscono una posizione assoluta sul piano di stampa."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Per un valore superiore a zero, le corse di spostamento in modalità combing superiori a tale distanza utilizzeranno la retrazione. Se il valore impostato è zero, non è presente un valore massimo e le corse in modalità combing non utilizzeranno la retrazione."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Quando è maggiore di zero, l'Espansione orizzontale dei fori viene applicata gradualmente sui fori piccoli (i fori piccoli vengono espansi maggiormente). Quando l'opzione è impostata su zero, l'espansione orizzontale sarà applicata a tutti i fori. I fori più grandi del diametro massimo di espansione orizzontale non saranno espansi."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Se maggiore di zero, l'espansione orizzontale del foro è la quantità di offset applicata a tutti i fori in ciascun livello. I valori positivi aumentano la dimensione dei fori, i valori negativi riducono la dimensione dei fori. Se questa impostazione è abilitata, può essere ulteriormente regolata con l'opzione del diametro max dell'espansione orizzontale del foro."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Quando si stampano le zone di rivestimento esterno ponte, la quantità di materiale estruso viene moltiplicata per questo valore."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Quando si stampano le pareti ponte, la quantità di materiale estruso viene moltiplicata per questo valore."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Quando si stampa il secondo strato del rivestimento esterno ponte, la quantità di materiale estruso viene moltiplicata per questo valore."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Quando si stampa il terzo strato del rivestimento esterno ponte, la quantità di materiale estruso viene moltiplicata per questo valore."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Quando viene raggiunta la velocità minima per il tempo minimo per strato, sollevare la testina dalla stampa e attendere il tempo supplementare fino al raggiungimento del valore per tempo minimo per strato."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Quando il modello presenta piccoli spazi vuoti verticali composti da un numero ridotto di strati, intorno a questi strati di norma dovrebbe essere presente un rivestimento esterno nell'interstizio. Abilitare questa impostazione per non generare il rivestimento esterno se l'interstizio verticale è molto piccolo. Ciò consente di migliorare il tempo di stampa e il tempo di sezionamento, ma dal punto di vista tecnico lascia il riempimento esposto all'aria."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Quando creare transizioni tra numeri di parete pari e dispari. Una forma a cuneo con un angolo maggiore di questa impostazione non presenta transazioni e nessuna parete verrà stampata al centro per riempire lo spazio rimanente. Riducendo questa impostazione si riduce il numero e la lunghezza di queste pareti centrali, ma potrebbe lasciare spazi vuoti o sovraestrusione."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Quando si esegue la transizione tra numeri di parete diversi poiché la parte diventa più sottile, viene allocata una determinata quantità di spazio per dividere o unire le linee perimetrali."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Durante la pulizia, il piano di stampa viene abbassato per creare uno spazio tra l'ugello e la stampa. Questo impedisce l'urto dell'ugello sulla stampa durante gli spostamenti, riducendo la possibilità di far cadere la stampa dal piano."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Ogniqualvolta avviene una retrazione, il piano di stampa viene abbassato per creare uno spazio tra l’ugello e la stampa. Previene l’urto dell’ugello sulla stampa durante gli spostamenti riducendo la possibilità di far cadere la stampa dal piano."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Indica se la distanza X/Y del supporto esclude la distanza Z del supporto o viceversa. Quando X/Y esclude Z, la distanza X/Y può allontanare il supporto dal modello, influenzando l’effettiva distanza Z allo sbalzo. È possibile disabilitare questa funzione non applicando la distanza X/Y intorno agli sbalzi."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Indica se le coordinate X/Y della posizione zero della stampante sono al centro dell’area stampabile."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Se l’endstop dell’asse X è in direzione positiva (coordinata X alta) o negativa (coordinata X bassa)."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Se l’endstop dell’asse Y è in direzione positiva (coordinata Y alta) o negativa (coordinata Y bassa)."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Se l’endstop dell’asse Z è in direzione positiva (coordinata Z alta) o negativa (coordinata Z bassa)."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Indica se gli estrusori condividono un singolo riscaldatore piuttosto che avere ognuno il proprio."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Indica se gli estrusori condividono un singolo ugello piuttosto che avere ognuno il proprio. Se impostato su true, si prevede che lo script gcode di avvio della stampante imposti tutti gli estrusori su uno stato di retrazione iniziale noto e mutuamente compatibile (nessuno o un solo filamento non retratto); in questo caso lo stato di retrazione iniziale è descritto, per estrusore, dal parametro 'machine_extruders_shared_nozzle_initial_retraction'."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Indica se la macchina ha un piano di stampa riscaldato."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Se la macchina è in grado di stabilizzare la temperatura del volume di stampa."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Per centrare l’oggetto al centro del piano di stampa (0,0) anziché utilizzare il sistema di coordinate in cui l’oggetto è stato salvato."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Per controllare la temperatura da Cura. Disattivare per controllare la temperatura ugello dall’esterno di Cura."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Sceglie se includere comandi temperatura piano di stampa all’avvio del codice G. Quando start_gcode contiene già comandi temperatura piano di stampa la parte anteriore di Cura disabilita automaticamente questa impostazione."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Sceglie se includere comandi temperatura ugello all’avvio del codice G. Quando start_gcode contiene già comandi temperatura ugello la parte anteriore di Cura disabilita automaticamente questa impostazione."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Indica se includere nel G-Code la pulitura ugello tra i layer (massimo 1 per layer). L'attivazione di questa impostazione potrebbe influenzare il comportamento della retrazione al cambio layer. Utilizzare le impostazioni di retrazione per pulitura per controllare la retrazione in corrispondenza dei layer in cui sarà in funzione lo script di pulitura."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Sceglie se inserire un comando per attendere finché la temperatura del piano di stampa non viene raggiunta all’avvio."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Eventuale innesco del filamento con un blob prima della stampa. L'attivazione di questa impostazione garantisce che l'estrusore avrà il materiale pronto all'ugello prima della stampa. Anche la stampa Brim o Skirt può funzionare da innesco, nel qual caso la disabilitazione di questa impostazione consente di risparmiare tempo."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Indica se stampare tutti i modelli un layer alla volta o se attendere di terminare un modello prima di passare al successivo. La modalità \"uno per volta\" è possibile solo se a) è abilitato solo un estrusore b) tutti i modelli sono separati in modo tale che l'intera testina di stampa possa muoversi tra di essi e che tutti i modelli siano più bassi della distanza tra l'ugello e gli assi X/Y."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Sceglie se mostrare le diverse varianti di questa macchina, descritte in file json a parte."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Specifica se usare comandi di retrazione firmware (G10/G11) anziché utilizzare la proprietà E nei comandi G1 per retrarre il materiale."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Sceglie se attendere finché la temperatura dell’ugello non viene raggiunta all’avvio."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Indica la larghezza di una singola linea di riempimento."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Indica la larghezza di una singola linea di supporto superiore o inferiore."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Larghezza di un singola linea delle aree nella parte superiore della stampa."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Indica la larghezza di una linea singola. In generale, la larghezza di ciascuna linea deve corrispondere alla larghezza dell’ugello. Tuttavia, una lieve riduzione di questo valore potrebbe generare stampe migliori."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Indica la larghezza di una singola linea della torre di innesco."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Indica la larghezza di una singola linea dello skirt o del brim."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Indica la larghezza di una singola linea di supporto inferiore."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Indica la larghezza di una singola linea di supporto superiore."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Indica la larghezza di una singola linea di supporto."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Indica la larghezza di una singola linea superiore/inferiore."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Indica la larghezza di una singola linea della parete per tutte le linee della parete tranne quella più esterna."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Indica la larghezza di una singola linea perimetrale."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Indica la larghezza delle linee dello strato di base del raft. Le linee di questo strato devono essere spesse per favorire l'adesione al piano di stampa."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Indica la larghezza delle linee dello strato intermedio del raft. Una maggiore estrusione del secondo strato provoca l'incollamento delle linee al piano di stampa."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Indica la larghezza delle linee della superficie superiore del raft. Queste possono essere linee sottili atte a levigare la parte superiore del raft."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Indica la larghezza della linea della parete esterna. Riducendo questo valore, è possibile stampare livelli di dettaglio più elevati."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Larghezza della parete che sostituirà feature sottili (in base alle dimensioni minime della feature) del modello. Se la larghezza minima della linea perimetrale è più sottile dello spessore della feature, la parete diventerà spessa come la feature stessa."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Posizione X spazzolino di pulitura"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Velocità di sollevamento (Hop) per pulitura"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Ugello pulitura inattiva sulla torre di innesco"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Distanza spostamento longitudinale di pulitura"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Pulitura ugello tra gli strati"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Pausa pulitura"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Conteggio ripetizioni operazioni di pulitura"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Distanza di retrazione per pulitura"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Retrazione per pulitura abilitata"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Entità di innesco supplementare dopo retrazione per pulitura"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Velocità di pulitura retrazione"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Velocità di retrazione per pulitura"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Velocità di retrazione per pulitura"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Pulitura Z Hop"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Altezza Z Hop pulitura"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Nel riempimento"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Tenere nota dello strumento attivo dopo l'invio di comandi temporanei allo strumento non attivo. Richiesto per la stampa con doppio estrusore con Smoothie o altro firmware con comandi modali dello strumento."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "Endstop X in direzione positiva"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Posizione X in cui verrà avviato lo script di pulitura."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y esclude Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Endstop Y in direzione positiva"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Endstop Z in direzione positiva"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Z Hop dopo cambio estrusore"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Z Hop dopo cambio altezza estrusore"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Altezza Z Hop"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Z Hop solo su parti stampate"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Velocità di sollevamento Z"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Z Hop durante la retrazione"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Allineamento delle giunzioni a Z"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Posizione della cucitura in Z"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Riferimento giunzione Z"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Giunzione Z X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Giunzione Z Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z esclude X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"

msgctxt "travel description"
msgid "travel"
msgstr "spostamenti"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>Se attivare le ventole di raffreddamento durante il cambio degli ugelli. Questo può aiutare a ridurre la fuoriuscita di liquido raffreddando l'ugello più velocemente:<ul><li><b>Invariato:</b>mantiene le ventole come prima</li><li><b>Solo ultimo estrusore:</b> attiva la ventola dell'ultimo estrusore utilizzato, ma spegne le altre (se presenti). Utile se hai estrusori completamente separati.</li><li><b>Tutte le ventole:</b> attiva tutte le ventole durante il cambio degli ugelli. Utile se hai una singola ventola di raffreddamento o più ventole vicine tra loro.</li></ul></html>"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "Tutte le ventole"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "Raffreddamento durante il cambio dell'estrusore"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "Gestisci la relazione spaziale tra la cucitura z della struttura di supporto e il modello 3D effettivo. Questo controllo è fondamentale in quanto consente agli utenti di rimuovere facilmente le strutture di supporto dopo la stampa, senza causare danni o lasciare segni sul modello stampato."

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "Distanza minima della cucitura z dal modello"

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "Moltiplicatore per il riempimento degli strati iniziali del supporto. Aumentarlo può aiutare l'adesione al piano."

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "Solo l'ultimo estrusore"

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "Posiziona la cucitura z su un vertice del poligono. Disattivando questa opzione è possibile posizionare la cucitura anche nello spazio tra i vertici. (Tieni presente che ciò non annullerà le restrizioni sul posizionamento della cucitura su una sporgenza non supportata.)"

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "Spessore minimo del guscio della Prime Tower"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "Flusso del basamento del raft"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "Sovrapposizione del riempimento del basamento del raft"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "Percentuale di sovrapposizione del riempimento del basamento del raft"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "Flusso del raft"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "Flusso dell'interfaccia del raft"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "Sovrapposizione del riempimento dell'interfaccia della zattera"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "Percentuale di sovrapposizione del riempimento dell'interfaccia della zattera"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "Offset Z dell'interfaccia Raft"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "Flusso della superficie del raft"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "Sovrapposizione del riempimento della superficie del raft"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "Percentuale di sovrapposizione del riempimento della superficie del raft"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "Offset Z della superficie del raft"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "Cucitura sporgente sull'angolo della parete"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "Strato iniziale del moltiplicatore di densità del riempimento di supporto"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "Supporta la cucitura Z lontano dal modello"

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "La quantità di materiale, rispetto a una normale linea di estrusione, da estrudere durante la stampa del basamento del raft. Un flusso maggiore può migliorare l'adesione e la resistenza strutturale del raft."

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "La quantità di materiale, relativa a una normale linea di estrusione, da estrudere durante la stampa dell'interfaccia del raft. Un flusso maggiore può migliorare l'adesione e la resistenza strutturale del raft."

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "La quantità di materiale, relativa ad una normale linea di estrusione, da estrudere durante la stampa del raft. Un flusso maggiore può migliorare l'adesione e la resistenza strutturale del raft."

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "La quantità di materiale, rispetto ad una normale linea di estrusione, da estrudere durante la stampa della superficie del raft. Avere un flusso maggiore può migliorare l'adesione e la resistenza strutturale del raft."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti del basamento del raft, come percentuale della larghezza della linea di riempimento. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al riempimento."

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti del basamento del raft. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al riempimento."

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti dell'interfaccia del raft, come percentuale della larghezza della linea di riempimento. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al riempimento."

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti dell'interfaccia del raft. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al tamponamento."

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti della superficie del raft, come percentuale della larghezza della linea di riempimento. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al riempimento."

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "La quantità di sovrapposizione tra il riempimento e le pareti della superficie del raft. Una leggera sovrapposizione consente alle pareti di connettersi saldamente al riempimento."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "La distanza tra il modello e la sua struttura di supporto in corrispondenza della cucitura dell'asse z."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "Lo spessore minimo del guscio della prime tower. Puoi aumentarlo per rendere più resistente la prime tower."

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "Cercare di evitare giunture su pareti che sporgono più di questo angolo. Quando il valore è 90, nessuna parete verrà considerata sporgente."

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "Invariato"

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "Quando si stampa il primo strato dell'interfaccia del raft, traslare con questo offset per modificare l'adesione tra il basamento e l'interfaccia. Un offset negativo dovrebbe migliorare l'adesione."

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "Quando si stampa il primo strato della superficie del raft, traslare con questo offset per modificare l'adesione tra l'interfaccia e la superficie. Un offset negativo dovrebbe migliorare l'adesione."

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "Cucitura Z sul vertice"

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "Aggiunge linee supplementari nel modello di riempimento per supportare gli strati sovrastanti. Questa opzione impedisce la formazione di vuoti o bolle di plastica che a volte compaiono nei modelli più complessi a causa del fatto che il riempimento sottostante non supporta correttamente lo strato stampato al di sopra."
"'Pareti' supporta solo i margini dello strato, mentre 'Pareti e linee' supporta anche le estremità delle file che compongono lo strato."

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "Velocità della ventola di costruzione per altezza "

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "Velocità della ventola di costruzione per strato"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "Numero ventola volume di stampa"

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "Determina la lunghezza di ciascun passo nella modifica del flusso durante l'estrusione lungo la cucitura a sciarpa. Una distanza minore determina un codice G più preciso ma anche più complesso."

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "Determina la lunghezza della cucitura a sciarpa, un tipo di cucitura che dovrebbe rendere la cucitura Z meno visibile. Deve essere superiore a 0 per essere efficace."

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Durata di ogni gradino per la variazione graduale del flusso"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Abilitare le variazioni graduali del flusso. Quando abilitate, il flusso viene aumentato/diminuito gradualmente fino al flusso target. Ciò è utile per le stampanti dotate di tubo bowden dove il flusso non viene modificato immediatamente all'avvio/arresto del motore dell'estrusore."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "Linee di rinforzo extra per sostenere gli strati superiori"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Per ogni spostamento del percorso superiore a questo valore, il flusso del materiale viene reimpostato su quello target dei percorsi."

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Dimensione del gradino di discretizzazione del flusso graduale"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Flusso graduale abilitato"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Accelerazione massima del flusso graduale"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Accelerazione massima del flusso per lo strato iniziale"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Accelerazione massima per le variazioni graduali del flusso"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Velocità minima per le variazioni graduali del flusso per il primo strato."

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "Nessuno"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr "Accelerazione parete esterna"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr "Decelerazione parete esterna"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "Rapporto di velocità finale parete esterna"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "Velocità distanza di divisione della parete esterna"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "Rapporto di velocità iniziale parete esterna"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Reimpostare la durata del flusso"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "Lunghezza cucitura a sciarpa"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "Altezza iniziale cucitura a sciarpa"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "Lunghezza passo cucitura a sciarpa"

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "L'altezza alla quale le ventole girano a velocità regolare. Nei livelli sottostanti la velocità delle ventole aumenta gradualmente da Velocità iniziale della ventola a Velocità regolare della ventola."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "Lo strato a cui le ventole di costruzione girano alla massima velocità. Questo valore viene calcolato e arrotondato a un numero intero."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "Il numero della ventola che raffredda il volume di stampa. Se è impostato su 0, significa che non è presente alcuna ventola per il volume di stampa."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "Il rapporto tra l'altezza dello strato selezionato e l'inizio della cucitura a sciarpa. Un numero più basso comporta un'altezza di cucitura maggiore. Per essere efficace, deve essere inferiore a 100."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "Si tratta dell'accelerazione con cui si raggiunge la velocità massima quando si stampa una parete esterna."

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "Si tratta della decelerazione con cui terminare la stampa di una parete esterna."

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "È la lunghezza massima di un percorso di estrusione se si divide un percorso più lungo per poter utilizzare l'accelerazione/decelerazione della parete esterna. Una distanza minore crea un codice G più preciso ma anche più laborioso."

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "Si tratta del rapporto della velocità massima con cui terminare la stampa di una parete esterna."

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "Questo è il rapporto della velocità massima da cui partire quando si stampa una parete esterna."

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "Solo pareti"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "Pareti e linee"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "Le pareti che sporgono più di questo angolo verranno stampate utilizzando le impostazioni per le pareti sporgenti. Con un valore di 90, nessun muro verrà considerato come sporgente. Anche le sporgenze sostenute da un supporto non verranno trattate come tali. Infine, qualsiasi linea che sia meno della metà della sporgenza non verrà gestita come tale."

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Una lista di direzioni delle linee rese in numeri interi, da utilizzare quando gli strati della superficie inferiore utilizzano il pattern a linee o a zig zag. Gli elementi della lista vengono usati in sequenza man mano che gli strati progrediscono e al raggiungimento del termine della lista, si ricomincia dall'inizio. Gli elementi della lista sono separati da virgole e l'intera lista è contenuta entro parentesi quadre. Il valore predefinito è un elenco vuoto, il che significa che verranno utilizzati gli angoli predefiniti tradizionali (45 e 135 gradi)"

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "Accelerazione delle pareti interne della superficie inferiore"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "Jerk delle pareti interne della superficie inferiore"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "Velocità delle pareti interne della superficie inferiore"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "Flusso della/e parete/i interna/e del layer inferiore"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "Accelerazione delle pareti esterne della superficie inferiore"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "Flusso delle pareti esterne della superficie inferiore"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "Jerk delle pareti esterne della superficie inferiore"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "Velocità delle pareti esterne della superficie inferiore"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "Accelerazione del rivestimento della superficie inferiore"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "Estrusore del rivestimento della superficie inferiore"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "Flusso del rivestimento della superficie inferiore"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "Jerk del rivestimento della superficie inferiore"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "Strati del rivestimento della superficie inferiore"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "Direzione linee del rivestimento della superficie inferiore"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "Spessore linee del rivestimento della superficie inferiore"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "Pattern del rivestimento della superficie inferiore"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "Velocità del rivestimento della superficie inferiore"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "Concentrico"

msgctxt "variant_name"
msgid "Extruder"
msgstr "Estrusore"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "Compensazione del flusso sulle linee delle pareti della superficie inferiore per tutte le linee delle pareti tranne quella più esterna."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "Compensazione del flusso sulle linee delle aree della superficie inferiore della stampa."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "Compensazione del flusso sulle linee delle pareti più esterne della superficie inferiore"

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "Griffin+Cheetah"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "Distanza di evitamento per lo spostamento interno"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "Linee"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "Tempo minimo del strato con sporgenza"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "Lunghezza minima del segmento con sporgenza"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "Ordine monotono della superficie inferiore"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "Decelerazione finale della parete esterna"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "Accelerazione iniziale della parete esterna"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "Velocità delle pareti in sporgenza"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "Le pareti in sporgenza verranno stampate ad una percentuale ridotta della velocità di stampa normale. Puoi specificare valori multipli in modo che le pareti con sporgenza maggiore vengano stampate ancora più lentamente, p.e. impostando [75, 50, 25] "

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "Fattore di anticipo della pressione"

msgctxt "variant_name"
msgid "Print Core"
msgstr "Core di stampa"

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Stampa le linee della superficie inferiore con un ordine che le fa sovrapporre sempre alle linee adiacenti in un'unica direzione. Questo richiede un tempo di stampa leggermente maggiore, ma rende le superfici piane più uniformi."

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "Il Codice G iniziale deve essere per primo"

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "L'accelerazione con la quale vengono stampati gli strati di rivestimento della superficie inferiore."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "L'accelerazione con la quale vengono stampate le pareti interne della superficie inferiore."

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "L'accelerazione con la quale vengono stampate le pareti più esterne della superficie inferiore."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "Le dimensioni della testina di stampa utilizzate per determinare la 'Distanza di sicurezza del modello' durante la stampa 'Uno alla volta'. Questi numeri si riferiscono alla linea centrale dell'ugello del primo estrusore. La parte sinistra dell'ugello è 'X Min' e deve essere negativa. La parte posteriore dell'ugello è 'Y Min' e deve essere negativa. X Max (destra) e Y Max (fronte) sono numeri positivi. L'altezza del carrello è la distanza calcolata dalla piattaforma di stampa alla guida del carrello X."

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "La distanza tra l'ugello e le pareti esterne già stampate durante il movimento all'interno di un modello."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "Il gruppo estrusore utilizzato per stampare il rivestimento più inferiore. Questo viene utilizzato nella stampa multi-estrusore."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "La variazione massima istantanea di velocità con cui vengono stampati gli strati di rivestimento della superficie inferiore."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "La variazione massima istantanea di velocità con cui vengono stampate le pareti interne della superficie inferiore."

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "La variazione massima istantanea di velocità con cui vengono stampate le pareti più esterne della superficie inferiore."

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Il tempo minimo impiegato in uno strato che contiene estrusioni in sporgenza. Questo costringe la stampante a rallentare, per impiegare almeno il tempo qui impostato in uno strato. Ciò permette al materiale stampato di raffreddarsi adeguatamente prima di stampare lo strato successivo. Gli strati potrebbero comunque richiedere meno del tempo minimo impostato se il Sollevamento della Testa è disabilitato e se altrimenti verrebbe violata la Velocità Minima."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "Il numero di strati di rivestimento più inferiori. Solitamente un solo strato più inferiore è sufficiente per generare superfici inferiori di qualità superiore."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "Il pattern dello strato più inferiore."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr "La velocità alla quale i rivestimenti della superficie inferiore vengono stampati."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "La velocità con cui vengono stampate le pareti interne della superficie inferiore."

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "La velocità con cui vengono stampate le pareti più esterne della superficie inferiore."

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "Questa impostazione controlla se il codice G iniziale deve essere forzatamente sempre il primo codice G. Senza questa opzione, altri codici G, come un T0, possono essere inseriti prima del codice G iniziale."

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "Fattore di regolazione per l'anticipo della pressione, che ha lo scopo di sincronizzare l'estrusione con il movimento"

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "Quando si cerca di applicare il tempo minimo dello strato specifico per gli strati in sporgenza, questo verrà applicato solo se almeno un movimento consecutivo di estrusione in sporgenza è più lungo di questo valore."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "Spessore di una singola linea delle aree della superficie inferiore della stampa."

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zig Zag"
