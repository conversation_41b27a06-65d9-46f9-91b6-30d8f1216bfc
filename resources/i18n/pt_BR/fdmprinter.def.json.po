# Cura
# Copyright (C) 2022 Ultimaker B.V.
# This file is distributed under the same license as the Cura package.
#
msgid ""
msgstr ""
"Project-Id-Version: Cura 5.7\n"
"Report-Msgid-Bugs-To: <EMAIL>\n"
"POT-Creation-Date: 2025-02-21 15:37+0000\n"
"PO-Revision-Date: 2025-03-23 23:56+0100\n"
"Last-Translator: <PERSON>l<PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Cláudi<PERSON> <<EMAIL>>\n"
"Language: pt_BR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=(n > 1);\n"
"X-Generator: Poedit 3.5\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>Como gerar a torre de purga:<ul><li><b>Normal:</b> cria-se um balde em que materiais secundários são purgados</li><li><b>Intercalados:</b> cria-se uma torre de purga tão esparsa quanto possível. Isto salvará material e filamento, mas só é praticável se os materiais aderirem entre si</li></ul></html>"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>Decide se se deve ativar as ventoinhas de refrigeração durante uma troca de bico. Isto pode ajudar a reduzir escorrimento por esfriar o bico mais rápido:<ul><li><b>Não alterado:</b> mantém as ventoinhas como estavam previamente</li><li><b>Somente o último extrusor:</b> liga a ventoinha do último extrusor usado, mas desliga as outras (se houver). Isto é útil se você tem extrusores completamente separados.</li><li><b>Todas as ventoinhas:</b> liga todas as ventoinhas durante a troca de bico. Isto é útil se você tiver uma única ventoinha para refrigeração ou múltiplas ventoinhas perto umas das outras.</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "O brim em volta de um modelo pode tocar outro modelo onde você não deseja. Isto remove todo o brim dentro desta distância de modelos sem brim."

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "A distância a manter das arestas do modelo. Passar a ferro as arestas da malha podem resultar em um aspecto entalhado da sua peça."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Um fator indicando em quanto o filamento é comprimido entre o alimentador do hotend e o bico, usado para determinar em quanto mover o material na troca de filamento."

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Uma lista de linhas de direções inteiras a usar quando as camadas de contorno da superfície inferior usa os padrões de linhas ou ziguezague. Elementos da lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela reinicia do começo. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O valor default é uma lista vazia que significa usar os ângulos default tradicionais (45 e 135 graus)."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Uma lista de direções inteiras de filete a usar quando as camadas superiores usam o padrão de linhas ou ziguezague. Elementos desta lista são usados sequencialmente de acordo com o progresso das camadas e quando se chega ao fim da lista, se volta ao começo. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O default é uma lista vazia que significa o uso dos ângulos default (45 e 135 graus)."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Uma lista de direções de linha inteiras para usar quando as camadas superiores e inferiores usarem os padrões de linha ou ziguezague. Elementos desta lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela inicia novamente. Os itens da lista são separados por vírgulas e a lita inteira é contida em colchetes. O default é uma lista vazia, o que significa usar os ângulos default (45 e 135 graus)."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Uma lista de direções inteiras de filete. Elementos da lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela recomeça do início. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O default é uma lista vazia, o que significa usar o ângulo default de 0 graus."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Uma lista de direções inteiras de filete. Elementos da lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela recomeça do início. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O default é uma lista vazia, o que significa usar os ângulos default (alternando entre 45 e 135 graus se as interfaces forem grossas, ou 90 se não)."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Uma lista de direções inteiras de filete. Elementos da lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela recomeça do início. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O default é uma lista vazia, o que significa usar os ângulos default (alternando entre 45 e 135 graus se as interfaces forem grossas, ou 90 se não)."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Uma lista de direções inteiras de filete. Elementos da lista são usados sequencialmente à medida que as camadas progridem e quando o fim da lista é alcançado, ela recomeça do início. Os itens da lista são separados por vírgulas e a lista inteira é contida em colchetes. O default é uma lista vazia, o que significa usar os ângulos default (alternando entre 45 e 135 graus se as interfaces forem grossas, ou 90 se não)."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Uma lista de direções de filetes em números inteiros a usar. Elementos da lista são usados sequencialmente de acordo com o progresso das camadas e quando o fim da lista é alcançado, ela volta ao começo. Os itens da lista são separados por vírgula e a lista inteira é contida em colchetes. O default é uma lista vazia que implica em usar os ângulos default tradicionais (45 e 135 graus para os padrões linha e ziguezague e 45 graus para todos os outros padrões)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Uma lista de polígonos com áreas em que o bico é proibido de entrar."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Uma lista de polígonos com áreas em que a cabeça de impressão é proibida de entrar."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Uma recomendação de quão distante galhos podem se mover dos pontos que eles suportam. Os galhos podem violar este valor para alcançar seu destino (plataforma de impressão ou parte chata do modelo). Abaixar este valor pode fazer o suporte ficar mais estável, mas aumentará o número de galhos (e por causa disso, ambos o uso de material e o tempo de impressão) "

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Posição Absoluta de Purga do Extrusor"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Máximo Variação das Camadas Adaptativas"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Tamanho da Topografia de Camadas Adaptativas"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Tamanho de Passo da Variação das Camadas Adaptativas"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Camadas adaptativas fazem a computação das alturas de camada depender da forma do modelo."

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "Adiciona filetes extras no padrão de preenchimento para apoiar os contornos acima.  Esta opção previne furos ou bolhas de plástico que algumas vezes aparecem em contornos de formas complexas devido ao preenchimento abaixo não apoiar corretamente o contorno de cima.  'Paredes' faz suportar apenas os extremos do contorno, enquanto que 'Paredes e Linhas' faz também suportar extremos de filetes que perfazem o contorno."

msgctxt "infill_wall_line_count description"
msgid ""
"Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\n"
"This feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr ""
"Adiciona paredes extras em torno da área de preenchimento. Tais paredes podem fazer filetes de contorno de topo e base afundarem menos, o que significa que você precisará de menos camadas de contorno de topo e base para a mesma qualidade, à custa de algum material extra.\n"
"Este recurso pode combinar com o Conectar Polígonos de Preenchimento para conectar todo o preenchimento em um único caminho de extrusão sem a necessidade de percursos ou retrações se os ajustes forem consistentes."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Aderência"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Tendência à Aderência"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajusta a quantidade de sobreposição entre as paredes e (os extremos de) linhas centrais do contorno, como uma porcentagem das larguras de filete de contorno e a parede mais interna. Uma sobreposição leve permite que as paredes se conectem firmemente ao contorno. Note que, dadas uma largura de contorno e filete de parede iguais, qualquer porcentagem acima de 50% pode fazer com que algum contorno ultrapasse a parede, pois a este ponto a posição do bico do extrusor de contorno pode já ter passado do meio da parede."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Ajusta a quantidade de sobreposição entre as paredes e (os extermos de) linhas centrais do contorno. Uma sobreposição pequena permite que as paredes se conectem firmemente ao contorno. Note que, dados uma largura de contorno e filete de parede iguais, qualquer valor maior que metade da largura da parede pode fazer com que o contorno ultrapasse a parede, pois a este ponto a posição do bico do extrusor de contorno pode já ter passado do meio da parede."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Ajusta a densidade de preenchimento da impressão."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajusta a densidade dos topos e bases da estrutura de suporte. Um valor maior resulta em seções pendentes melhores, mas os suportes são mais difíceis de remover."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Ajusta a densidade da estrutura de suporte usada para gerar as pontas dos galhos. Um valor mais alto resulta em melhores seções pendentes, mas os suportes ficam mais difíceis de remover. Use Teto de Suporte para valores muito altos ou assegure-se que a densidade de suporte é similarmente alta no topo."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Ajusta a densidade da estrutura de suporte. Um valor mais alto resulta em seções pendentes melhores, mas os suportes são mais difíceis de remover."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Ajusta o diâmetro do filamento utilizado. Acerte este valor com o diâmetro real do filamento."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Ajusta a colocação das estruturas de suporte. Pode ser ajustada para suportes que somente tocam a mesa de impressão ou suportes em todos os lugares com seções pendentes (incluindo as que não estão pendentes em relação à mesa)."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Depois de imprimir a torre de purga com um bico, limpar o material escorrendo do outro bico na torre de purga."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Quando a máquina troca de um extrusor para o outro, sobe-se um pouco em Z para criar um espaço entre o bico e a impressão. Isso impede que o bico escorra material em cima da impressão."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Tudo"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Todos de Uma Vez"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "Todas as ventoinhas"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Todos os ajustes que influenciam a resolução da impressão. Estes ajustes têm um impacto maior na qualidade (e tempo de impressão)"

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "Permite que você ordene a lista de objetos de forma a manualmente definir a sequência de impressão. O primeiro objeto da lista será impresso primeiro."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alternar Parede Adicional"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alternar a Remoção de Malhas"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Alternar Direções de Parede"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Alterna direções de parede a cada camada e reentrância. Útil para materiais que podem acumular stress, como em impressão com metal."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Alumínio"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Sempre Escrever a Ferramenta Ativa"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Sempre retrair quando se mover para iniciar uma parede externa."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Deslocamento adicional aplicado para todos os polígonos em cada camada. Valores positivos 'engordam' a camada e podem compensar por furos exagerados; valores negativos a 'emagrecem' e podem compensar por furos pequenos."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "Deslocamento adicional aplicado a todos os polígonos da primeira camada. Um valor negativo pode compensar pelo esmagamento da primeira camada conhecido como \"pata de elefante\"."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Quantidade de deslocamento aplicado a todos os polígonos do suporte em cada camada. Valores positivos podem amaciar as áreas de suporte e resultar em suporte mais estável."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Quantidade de deslocamento aplicado às bases do suporte."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Quantidade de deslocamento aplicado aos tetos do suporte."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Quantidade de deslocamento aplicado aos polígonos da interface de suporte."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "Quantidade a retrair do filamento tal que ele não escorra durante a sequência de limpeza."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Um adicional ao raio do centro de cada cubo para verificar a borda do modelo, de modo a decidir se este cubo deve ser subdividido. Valores maiores levam a uma cobertura mais espessa de pequenos cubos perto da borda do modelo."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Malha Anti-Pendente"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Posição Retraída Anti-escorrimento"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Velocidade de Retração Anti-escorrimento"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Aplicar o deslocamento de extrusor ao sistema de coordenadas. Afeta todos os extrusores."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "Nos lugares em que os modelos tocam, gerar uma estrutura de vigas interligada. Isto melhora a aderência entre modelos, especialmente modelos impressos com materiais diferentes."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Evitar Partes Impressas nas Viagens"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Evitar Suportes No Percurso"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Atrás"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Atrás à Esquerda"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Atrás à Direita"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Ambos"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Ambos se sobrepõem"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Camadas Inferiores"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Camada Inicial do Padrão da Base"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Distância de Expansão do Contorno Inferior"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Largura de Remoção do Contorno Inferior"

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "Aceleração da Parede Interna da Superfície Inferior"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "Jerk da Parede Interna da Superfície Inferior"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "Velocidade da Parede Interna da Superfície Inferior"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "Fluxo da(s) Parede(s) Interna(s) da Superfície Inferior"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "Aceleração da Parede Externa da Superfície Inferior"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "Fluxo da Parede Externa da Superfície Inferior"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "Jerk da Parede Externa da Superfície Inferior"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "Velocidade da Parede Externa da Superfície Inferior"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "Aceleração do Contorno da Superfície Inferior"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "Extrusor do Contorno da Superfície Inferior"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "Fluxo do Contorno da Superfície Inferior"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "Jerk do Contorno da Superfície Inferior"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "Camadas do Contorno da Superfície Inferior"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "Direções de Filete do Contorno da Superfície Inferior"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "Largura de Filete do Contorno da Superfície Inferior"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "Padrão do Contorno da Superfície Inferior"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "Velocidade do Contorno da Superfície Inferior"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Espessura Inferior"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Densidade de Galho"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Diâmetro de Galho"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Ângulo de Diâmetro de Galho"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Posição Retraída de Preparação de Quebra"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Velocidade de Retração de Preparação de Quebra"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Temperatura de Quebra de Preparação"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Posição Retraída de Quebra"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Velocidade de Retração de Quebra"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Temperatura de Quebra"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Quebrar Suportes em Pedaços"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Velocidade de Ventoinha da Ponte"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Ponte Tem Camadas Múltiplas"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Densidade de Segundo Contorno da Ponte"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Velocidade da Ventoinha no Segundo Contorno da Ponte"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Fluxo de Segundo Contorno da Ponte"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Velocidade de Segundo Contorno da Ponte"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Densidade do Contorno de Ponte"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Fluxo do Contorno de Ponte"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Velocidade do Contorno de Ponte"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Limiar de Suporte de Contorno de Ponte"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Densidade Máxima do Preenchimento Esparso de Ponte"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Densidade de Terceiro Contorno da Ponte"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Velocidade da Ventoinha no Terceiro Contorno da Ponte"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Fluxo de Terceiro Contorno da Ponte"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Velocidade de Terceiro Contorno da Ponte"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Desengrenagem de Parede de Ponte"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Fluxo da Parede de Ponte"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Velocidade da Parede de Ponte"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Brim"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "Margem de Prevenção de Brim"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Distância do Brim"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Contagem de Linhas do Brim"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "Localização do Brim"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Brim Substitui Suporte"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Largura do Brim"

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "Velocidade de Construção da Ventoinha na Altura"

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "Velocidade de Construção da Ventoinha na Camada"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Aderência à Mesa"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Extrusor de Aderência à Mesa"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Tipo de Aderência da Mesa de Impressão"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Material da Plataforma de Impressão"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Forma da Mesa"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Temperatura da Mesa de Impressão"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "Temperatura da Mesa de Impressão da Camada Inicial"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Temperatura do Volume de Impressão"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "Limite de temperature do Volume de Construção"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "Aviso de temperatura do Volume de Construção"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "Número da ventoinha de volume de construção"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "Ao habilitar este ajuste sua torre de purga ganhará um brim, mesmo que o modelo não tenha. Se você quiser uma base mais firme para uma torre alta, pode aumentar a altura."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Centralizar Objeto"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "Altera a geometria do modelo a ser impresso de tal modo que o mínimo de suporte seja exigido. Seções pendentes agudas serão torcidas pra ficar mais verticais. Áreas de seções pendentes profundas se tornarão mais rasas."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Permite escolher entre as técnicas para geração de suporte. Suporte \"normal\" cria a estrutura de suporte diretamente abaixo das seções pendentes e vai em linha reta pra baixo. Suporte \"em árvore\" cria galhos na direção das seções pendentes, suportando o modelo nas pontas destes, e permitndo que se distribuam em torno do modelo para apoiá-lo na plataforma de impressão tanto quanto possível."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Velocidade de Desengrenagem"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Volume de Desengrenagem"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "A desengrenagem ou 'coasting' troca a última parte do caminho de uma extrusão pelo caminho sem extrudar. O material escorrendo é usado para imprimir a última parte do caminho de extrusão de modo a reduzir fiapos."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Modo de Combing"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "O Combing mantém o bico dentro de áreas já impressas ao fazer o percurso. Isto causa movimentações de percurso um pouco mais demoradas mas reduz a necessidade de retrações. Se o combing estiver desligado, o material sofrerá retração eo bico se moverá em linha reta até o próximo ponto. É possível também evitar combing sobre contornos inferiores e superiores ou somente fazer combing dentro do preenchimento."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Ajustes de Linha de Comando"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Concêntrico"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Ângulo de Suporte Cônico"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Largura Mínima do Suporte Cônico"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Conectar Linhas de Preenchimento"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Conectar Polígonos do Preenchimento"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Conectar Linhas de Suporte"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Conectar os Ziguezagues do Suporte"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Conectar Polígonos do Topo e Base"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Conecta os caminhos de preenchimentos onde estiverem próximos um ao outro. Para padrões de preenchimento que consistam de vários polígonos fechados, a habilitação deste ajuste reduz bastante o tempo de percurso."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Conecta os ziguezagues. Isto aumentará a força da estrutura de suporte em ziguezague."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Conecta os extremos das linhas de suporte juntos. Habilitar este ajuste pode tornar seu suporte mais robusto e reduzir subextrusão, mas gastará mais material."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "Conecta as extremidades onde o padrão de preenchimento toca a parede interna usando uma linha que segue a forma da parede interna. Habilitar este ajuste pode fazer o preenchimento aderir melhor às paredes e reduzir o efeito do preenchimento na qualidade de superfícies verticais. Desabilitar este ajuda diminui a quantidade de material usado."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Conectar caminhos de contorno da base e topo quando estiverem próximos entre si. Para o padrão concêntrico, habilitar este ajuste reduzirá bastante o tempo de percurso, mas por as conexões poderem acontecer no meio do preenchimento, este recurso pode reduzir a qualidade da superfície superior."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Controla se os cantos do contorno do modelo influenciam a posição da costura. Nenhum significa que os cantos não terão influência na posição da costura. Ocultar Costura torna mais provável que a costura ocorra em um canto interior. Expôr Costura torna mais provável que a costura ocorra em um canto exterior. Ocultar ou Expôr Costura torna mais provável que a costura ocorra em um canto interior ou exterior. Ocultação Inteligente permite tanto cantos interiores quanto exteriores, mas escolhe os interiores mais frequentemente se apropriado."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Converte cada file de preenchimento para este número de filetes. Os filetes extras não se cruzam, se evitam. Isto torna o preenchimento mais rígido, mas aumenta o tempo de impressão e uso do material."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Velocidade de Resfriamento"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Refrigeração"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Refrigeração"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "Refrigerando durante a troca de extrusor"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Cruzado"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Cruzado"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Cruzado 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Tamanho de Bolso do Cruzado 3D"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Imagem de Densidade de Preenchimento Cruzado para Suporte"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Imagem de Densidade do Preenchimento Cruzado"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Material Cristalino"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Cúbico"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Subdivisão Cúbica"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Cobertura de Subdivisão Cúbica"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Malha de Corte"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Dados relacionando fluxo de material (em mm³ por segundo) a temperatura (graus Celsius)."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Aceleração Default"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Temperatura Default da Plataforma de Impressão"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Jerk Default do Filamento"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Temperatura Default de Impressão"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Jerk Default nos eixos X-Y"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "O Jerk Default em Z"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "O valor default de jerk para movimentos no plano horizontal."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "O valor default de jerk para movimento na direção Z."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "O valor default de jerk para movimentação do filamento."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Detectar pontes e modificar a velocidade de impressão, de fluxo e ajustes de fan onde elas forem detectadas."

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "Determina o comprimento de cada passo na mudança de fluxo ao extrudar pela emenda scarf. Uma distância menor resultará em um G-code mais preciso porém também mais complexo."

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "Determina o comprimento da emenda scarf, um tipo de emenda que procura tornar a junção do eixo Z menos visível. Deve ser maior que 0 pra se tornar efetivo."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Determina a ordem na qual paredes são impressas. Imprimir as paredes externas primeiro ajuda na acuracidade dimensional, visto que falhas das paredes internas não poderão propagar externamente. No entanto, imprimi-las no final ajuda a haver melhor empilhamento quando seções pendentes são impressas. Quando há uma quantidade ímpar de paredes internas totais, a 'última linha central' é sempre impressa por último."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Determina a prioridade desta malha ao considerar múltiplas malhas de preenchimento sobrepostas. Áreas onde múltiplas malhas de preenchimento se sobrepõem terão os ajustes da malha com a maior prioridade. Uma malha de preenchimento com prioridade maior modificará o preenchimento tanto das malhas de preenchimento com prioridade menor quanto das malhas normais."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Determina quando uma camada do preenchimento relâmpago deve suportar algo sobre si. Medido no ângulo de acordo com a espessura da camada."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Determina quando a camada de preenchimento relâmpago deve suportar o modelo sobre si. Medido no ângulo de acordo com a espessura."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Diâmetro"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Aumento de Diâmetro para o Modelo"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "O diâmetro que cada galho tenta alcançar quando se aproxima da plataforma de impressão. Melhora aderência à plataforma."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Diferentes opções que ajudam a melhorar a extrusão e a aderência à plataforma de impressão. Brim (bainha) adiciona uma camada única e chata em volta da base de seu modelo para impedir warping. Raft (balsa) adiciona uma grade densa com 'teto' abaixo do modelo. Skirt (saia) é uma linha impressa em volta do modelo, mas não conectada ao modelo, para apenas iniciar o processo de extrusão."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "Áreas Proibidas"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Distância entre as linhas de preenchimento impressas. Este ajuste é calculado pela densidade de preenchimento e a largura de extrusão do preenchimento."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Distância entre os filetes da camada inicial da camada de suporte. Este ajuste é calculado pela densidade de suporte."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Distância entre os filetes de impressão da base de suporte. Este ajuste é calculado pela densidade da Base de Suporte, mas pode ser ajustado separadamente."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Distância entre os filetes de impressão do teto de suporte. Este ajuste é calculado pela Densidade do Teto de Suporte mas pode ser ajustado separadamente."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Distância entre as linhas impressas da estrutura de suporte. Este ajuste é calculado a partir da densidade de suporte."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Distância da impressão até a base do suporte. Note que o valor é arredondado pra cima para a próxima altura de camada."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Distância do topo do suporte à impressão."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Distância da base ou topo do suporte à impressão. Esta lacuna provê uma folga pra remover os suporte depois da impressão do modelo. A camada de suporte superior abaixo do modelo pode ser uma fração de camadas regulares."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Distância do percurso inserido após cada linha de preenchimento, para fazer o preenchimento aderir melhor às paredes. Esta opção é similar à sobreposição de preenchimento mas sem extrusão e somente em uma extremidade do filete de preenchimento."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Distância do percurso inserido após a parede externa para esconder melhor a costura em Z."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Distância da Cobertura de Trabalho da impressão nas direções X e Y."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Distância da cobertura de escorrimento da impressão nas direções X e Y."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Distância da estrutura de suporte da seção pendente nas direções X/Y."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Distância da estrutura de suporte até a impressão nas direções X e Y."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Pontos de distância são deslocados para suavizar o caminho"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Pontos de distância são deslocados para suavizar o caminho"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Não gerar preenchimento para áreas menores que esta (usar contorno)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Altura da Cobertura de Trabalho"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Limitação da Cobertura de Trabalho"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Distância X/Y da Cobertura de Trabalho"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Malha de Suporte Abaixo"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "Extrusão Dual"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Duração de cada passo na mudança gradual de fluxo"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Elíptica"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "Habilitar Controle de Aceleração"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Habilitar Ajustes de Ponte"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Habilitar Desengrenagem"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Habilitar Suporte Cônico"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Habilitar Cobertura de Trabalho"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Habilitar Movimento Fluido"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Habilitar Passar a Ferro"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Habilitar Controle de Jerk"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Habilitar Controle de Temperatura do Bico"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Habilitar Cobertura de Escorrimento"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "Habilitar Massa de Purga"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "Habilitar Torre de Purga"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Habilitar Refrigeração de Impressão"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "Habilitar Relatório de Processo de Impressão"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Habilitar Retração"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Habilitar Brim de Suporte"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Habilitar Base de Suporte"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Habilitar Interface de Suporte"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Habilitar Teto de Suporte"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Habilitar Aceleração de Percurso"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Habilitar Jerk de Percurso"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Habilita a cobertura exterior de escorrimento. Isso criará uma casca ou cobertura em volta do modelo que ajudará a limpar o segundo bico se estiver na mesma altura do primeiro bico."

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Habilita mudanças graduais de fluxo. Quando habilitado, o fluxo é gradualmente aumentado ou diminuído ao fluxo-alvo. Isto é útil para impressoras com tubo bowden em que o fluxo não é imediatamente alterado quando o motor de extrusor para ou inicia."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "Habilita relatório de processo de impressão para definir valores-limite para possível detecção de falhas."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "Habilita pequenas regiões (até a 'Largura de Teto/Base Pequenos') na camada superior com contorno (exposta ao ar) pra serem preenchidas com paredes ao invés do padrão default."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "Permite ajustar o jerk da cabeça de impressão quando a velocidade nos eixos X ou Y muda. Aumentar o jerk pode reduzir o tempo de impressão ao custo de qualidade de impressão."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Permite ajustar a aceleração da cabeça de impressão. Aumentar as acelerações pode reduzir tempo de impressão ao custo de qualidade de impressão."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Habilita as ventoinhas de refrigeração ao imprimir. As ventoinhas aprimoram a qualidade de impressão em camadas de tempo curto de impressão e em pontes e seções pendentes."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "G-Code Final"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Comprimento de Purga do Fim do Filamento"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Velocidade de Purga do Fim do Filamento"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "Força que o brim seja impresso em volta do modelo mesmo se este espaço fosse ser ocupado por suporte. Isto substitui algumas regiões da primeira camada de suporte por regiões de brim."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "Em Todo Lugar"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Em Todo Lugar"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Exclusivo"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Experimental"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Expôr Costura"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Costura Extensa"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Costura Extensa tenta costurar furos abertos na malha fechando o furo com polígonos que o tocam. Esta opção pode adicionar bastante tempo ao fatiamento das peças."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "Linhas de Preenchimento Extras Para Apoiar Contornos"

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Contagem de Paredes de Preenchimento Extras"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Contagem de Paredes Extras de Contorno"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Material extra a avançar depois da troca de bico."

msgctxt "variant_name"
msgid "Extruder"
msgstr "Extrusor"

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Posição X da Purga do Extrusor"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Posição Y da Purga do Extrusor"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Posição Z de Purga do Extrusor"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Extrusores Compartilham Aquecedor"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Extrusores Compartilham o Bico"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Modificador de Velocidade de Resfriamento de Extrusão"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Fator de correção de largura de extrusão baseada na velocidade. Em 0%, a velocidade de movimento é mantida constante na Velocidade de Impressão. Em 100%, a velocidade de movimento é ajustada de forma que o fluxo (em mm³/s) seja mantido constante, isto é, filetes de metade da Largura de Filete normal são impressos duas vezes mais rápido e filetes duas vezes mais espessos são impressos na metade da velocidade. Um valor mais alto que 100% pode ajudar a compensar pela maior pressão necessária para extrudar filetes espessos."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Velocidade da Ventoinha"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Sobrepor Velocidade de Ventoinha"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Contornos de aspectos menores que este comprimento serão impressos usando a Velocidade de Aspecto Pequeno."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Recursos que não foram completamente desenvolvidos ainda."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Diâmetro da Engrenagem de Alimentação"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Temperatura de Impressão Final"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Retração de Firmware"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "Extrusor de Suporte da Primeira Camada"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Fluxo"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Raio de Equalização de Fluxo"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "Limite de Fluxo"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Fator de Compensação da Taxa de Fluxo"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Máximo Deslocamento de Extrusão de Compensação de Taxa de Fluxo"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Gráfico de Fluxo de Temperatura"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "Aviso de Fluxo"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "Compensação de fluxo para a primeira camada; a quantidade de material extrudado na camada inicial é multiplicada por este valor."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "Compensação de fluxo nos filetes da base da primeira camada"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "Compensação de fluxo nos filetes de parede da superfície inferior para todos os filetes exceto o mais externo."

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Compensação de fluxo em filetes de preenchimento."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Compensação de fluxo em filetes do teto ou base do suporte."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "Compensação de fluxo em filetes das áreas da base da impressão."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Compensação de Fluxo em filetes das áreas no topo da impressão."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Compensação de fluxo em filetes de torre de purga."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Compensação de Fluxo em filetes de Skirt e Brim."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Compensação de fluxo nos filetes da base do suporte."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Compensação de fluxo em filetes do teto de suporte."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Compensação de fluxo em filetes de estruturas de suporte."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "Compensação de fluxo na parede mais externa da superfície inferior."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "Compensação de fluxo no filete de parede mais externo da primeira camada."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "Compensação de fluxo no filete de parede mais externo."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Compensação de fluxo no filete de parede externo de superfície do topo."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Compensação de fluxo nos files de parede de superfície do topo excetuando o mais externo."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Compensação de fluxo em filetes do topo e base."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "Compensação de fluxo nos filetes de parede para todos os filetes exceto o mais externo, mas só para a primeira camada"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "Compensação de fluxo em todos os filetes de parede excetuando o mais externo."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Compensação de fluxo em filetes das paredes."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Compensação de fluxo: a quantidade de material extrudado é multiplicado por este valor."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Ângulo de Movimento Fluido"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Distância de Deslocamento do Movimento Fluido"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Distância Pequena do Movimento Fluido"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Comprimento da Descarga de Purga"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Velocidade de Descarga de Purga"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Para cada movimento de percurso menor que este valor, o fluxo de material é resetado para o fluxo-alvo dos caminhos"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Para estruturas finas por volta de uma ou duas vezes o tamanho do bico, as larguras de linhas precisam ser alteradas para aderir à grossura do modelo. Este ajuste controla a largura mínima de filete permite para as paredes. As larguras mínimas de filete inerentemente também determinam as larguras máximas, já que transicionamos de N pra N+1 parede na grossura de geometria onde paredes N são largas e as paredes N+1 são estreitas. A  maior largura possível de parede é duas vezes a Largura Mínima de Filete de Parede."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Frente"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Frente à Esquerda"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Frente à Direita"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Completo"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Contorno Felpudo"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Densidade do Contorno Felpudo"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Contorno Felpudo Externo Apenas"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Distância de Pontos do Contorno Felpudo"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Espessura do Contorno Felpudo"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "Sabor de G-Code"

msgctxt "machine_end_gcode description"
msgid ""
"G-code commands to be executed at the very end - separated by \n"
"."
msgstr ""
"Comandos G-Code a serem executados no final da impressão - separados por \n"
"."

msgctxt "machine_start_gcode description"
msgid ""
"G-code commands to be executed at the very start - separated by \n"
"."
msgstr ""
"Comandos G-Code a serem executados no início da impressão - separados por \n"
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "GUID do material. É ajustado automaticamente."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Altura do Eixo"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "Gerar Estrutura Interligada"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Gerar Suporte"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "Gera o brim dentro das regiões de preenchimento de suporte da primeira camada. Este brim é impresso sob o suporte, não em volta dele. Habilitar este ajuste aumenta a aderência de suporte à mesa de impressão."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Gera uma interface densa entre o modelo e o suporte. Isto criará um contorno no topo do suporte em que o modelo é impresso e na base do suporte, onde ele fica sobre o modelo."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Gera um bloco denso de material entre a base do suporte e o modelo. Isto criará uma divisória entre o modelo e o suporte."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Gera um bloco denso de material entre o topo do suporte e o modelo. Isto criará uma divisória entre o modelo e o suporte."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Gerar estrutura que suportem partes do modelo que tenham seções pendentes. Sem estas estruturas, tais partes desabariam durante a impressão."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Vidro"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Passa sobre a superfície superior uma vez a mais, mas extrudando muito pouco material. Isto serve para derreter mais o plástico em cima, criando uma superfície lisa. A pressão na câmara do bico é mantida alta tal que as rugas na superfície são preenchidas com material."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Altura de Passo do Preenchimento Gradual"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Passos Graduais de Preenchimento"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Altura de Passo do Preenchimento Gradual de Suporte"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Passos de Preenchimento Gradual de Suporte"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Tamanho de passo de discretização gradual de fluxo"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Fluxo gradual habilitado"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Aceleração máximo de fluxo gradual"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Gradualmente reduzir até esta temperatura quanto se estiver imprimindo a velocidades reduzidas devidas ao tempo mínimo de camada."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Grade"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Grade"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Grade"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Grade"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Grade"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "Griffin+Cheetah"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Agrupar Paredes Externas"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Giróide"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Giróide"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Tem Estabilização de Temperatura do Volume de Impressão"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Tem Mesa Aquecida"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Velocidade de Aquecimento"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Comprimento da Zona de Aquecimento"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Limitação de altura da cobertura de trabalho. Acima desta altura a cobertura não será impressa."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Ocultar Costura"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Ocultar ou Expor Costura"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Expansão Horizontal do Furo"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Diâmetro Máximo da Expansão Horizontal de Furo"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Furos e contornos de partes com diâmetro menor que este serão impressos usando a Velocidade de Aspecto Pequeno."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Expansão Horizontal"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Compensação de Fator de Encolhimento Horizontal"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Quanto o filamento pode ser esticado antes que quebre, quando aquecido."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "De quanto o material precisa ser retraído antes que pare de escorrer."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "Em quanto mover o filamento para compensar mudanças na taxa de fluxo, como uma porcentagem da distância que o filamento seria movido em um segundo de extrusão."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "De quanto o filamento deve ser retraído para se destacar completamente."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Qual a velocidade do material para que seja retraído antes de quebrar em uma retração."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Qual a velocidade do material para que seja retraído durante a troca de filamento sem escorrimento."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "Quão rápido purgar o material depois de trocar um carretel vazio por um novo carretel do mesmo material."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "Quão rápido purgar o material depois de alternar para um material diferente."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "Quanto tempo o material pode ser mantido fora de armazenamento seco com segurança."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Quantos passos do motor de passo resultarão em um milímetro de movimento na direção X."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Quantos passos do motor de passo resultarão em um milímetro de movimento na direção Y."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Quantos passos do motor de passo resultarão em um milímetro de movimento na direção Z."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Quantos passos dos motores resultarão no movimento da engrenagem de alimentação em um milímetro da circunferência."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "Quanto material usar para purgar o material anterior do bico (em comprimento de filamento) quando um carretel vazio for trocado por um carretel novo do mesmo material."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "Quanto material usar para purgar o material anterior do bico (em comprimento de filamento) quando alternar para um material diferente."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "Quanto é assumido que o filamento de cada extrusor  tenha retraído da ponta do bico ao completar o script g-code de início da impressora; o valor deve ser igual ou superior ao comprimento da parte comum dos dutos do bico."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Como a interface de suporte a o suporte interagirão quando eles se sobrepuserem. No momento implementado apenas para teto de suporte."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Quão alto um galho tem que ser para ser agregado ao modelo. Previne pequenos nódulos de suporte. Este ajuste é ignorado quando um galho está suportando teto de suporte."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Se uma região do contorno for suportada por menos do que esta porcentagem de sua área, imprimi-la com os ajustes de ponte. Senão, imprimir usando os ajustes normais de contorno."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Se um segmento do percurso do extrusor se desviar do movimento geral por um ângulo maior que esse, será suavizado."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Se habilitado, a segunda e terceira camadas sobre o ar serão impressas usando os ajustes seguintes. Senão, estas camadas serão impressas com ajustes normais."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Se for detectado que a cabeça de impressão estaria alternando em rápida sucessão entre números diferentes de parede, não fazer tal alternação. Remove transições se elas estiverem próximas até essa distância."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se a base do raft estiver habilitada, esta é a área de raft extra em torno do modelo que também a terá. Aumentar esta margem criará um raft mais forte mas também usará mais material e deixará uma área menor para sua impressão."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se o Raft estiver habilitado, esta é a área extra do raft em volta do modelo que também faz parte dele. Aumentar esta margem criará um raft mais forte mas também gastará mais material e deixará menos área para sua impressão."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se o meio do raft estiver habilitado, esta será a área extra de raft em torno do modelo que também o terá. Aumentar esta margem criará um raft mais forte mas também usará mais material e deixará uma área menor para a sua impressão."

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Se o topo do raft estiver habilitado, esta é a área extra de raft em torno do modelo que também o terá. Aumentar esta margem criará um raft mais forte mas usará mais material e deixará menos área para sua impressão."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Ignora a geometria interna de volumes sobrepostos dentro de uma malha e imprime os volumes como um único volume. Isto pode ter o efeito não-intencional de fazer cavidades desaparecerem."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Incluir Temperatura da Mesa"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Incluir Temperaturas de Material"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Inclusivo"

msgctxt "infill description"
msgid "Infill"
msgstr "Preenchimento"

msgctxt "infill label"
msgid "Infill"
msgstr "Preenchimento"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Aceleração do Preenchimento"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Preenchimento Antes das Paredes"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Densidade do Preenchimento"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Extrusor do Preenchimento"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Fluxo de Preenchimento"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Jerk do Preenchimento"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Espessura da Camada de Preenchimento"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Direções de Filetes de Preenchimento"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Distância da Linha de Preenchimento"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Multiplicador de Filete de Preenchimento"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Largura de Extrusão do Preenchimento"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Malha de Preenchimento"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Ângulo de Seções Pendentes do Preenchimento"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Sobreposição de Preenchimento"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Porcentagem de Sobreposição do Preenchimento"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Padrão de Preenchimento"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Velocidade de Preenchimento"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Suporte do Preenchimento"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Otimização de Percurso de Preenchimento"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Distância de Varredura do Preenchimento"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Deslocamento X do Preenchimento"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Deslocamento do Preenchimento Y"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "Camadas Inferiores Iniciais"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "Velocidade Inicial da Ventoinha"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "Aceleração da Camada Inicial"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "Fluxo da Base da Camada Inicial"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "Diâmetro da Camada Inicial"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "Fluxo Inicial de Camada"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "Altura da Primeira Camada"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "Expansão Horizontal da Camada Inicial"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "Fluxo de Parede Interna da Camada Inicial"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "Jerk da Camada Inicial"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "Largura de Extrusão da Camada Inicial"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "Fluxo de Parede Externa da Camada Inicial"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "Aceleração de Impressão da Camada Inicial"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "Jerk de Impressão da Camada Inicial"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "Velocidade de Impressão da Camada Inicial"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "Velocidade da Camada Inicial"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "Distância de Filetes da Camada Inicial de Suporte"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "Aceleração de Percurso da Camada Inicial"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "Jerk de Percurso da Camada Inicial"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "Velocidade de Percurso da Camada Inicial"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "Sobreposição em Z das Camadas Iniciais"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "Temperatura Inicial de Impressão"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "Aceleração máxima de fluxo da camada inicial"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "Aceleração das Paredes Interiores"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "Extrusor da Parede Interior"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "Jerk das Paredes Internas"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "Velocidade da Parede Interior"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "Fluxo da(s) Parede(s) Interna(s)"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "Largura de Extrusão das Paredes Internas"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Penetração adicional aplicada ao caminho da parede externa. Se a parede externa for menor que o bico, e impressa depois das paredes internas, use este deslocamento para fazer o orifício do bico se sobrepor às paredes internas ao invés de ao lado de fora do modelo."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "Dentro Somente"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "De Dentro Pra Fora"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "Distância de Desvio do Percurso Interior"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Linhas de interface preferidas"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Interface preferida"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "Intercalado"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "Contagem de Camadas das Vigas Interligadas"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "Largura da Viga Interligada"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "Prevenção de Fronteira de Interligação"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "Profundidade de Interligação"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "Orientação da Estrutura de Interligação"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Passar a Ferro Somente Camada Mais Alta"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Aceleração de Passar a Ferro"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Fluxo de Passagem a Ferro"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Penetração da Passagem a Ferro"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Jerk de Passar a Ferro"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Espaçamento de Passagem a Ferro"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Padrão de Passagem a Ferro"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Velocidade de Passar o Ferro"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Origem é no Centro"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "É material de suporte"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Este material é do tipo que se destaca completamente quando aquecido (cristalino), ou é o tipo que produz cadeias de polímero entrelaçadas (não-cristalino)?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Se esse material é ou não tipicamente usado como material de suporte durante a impressão."

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Flutuar movimento apenas nos contornos e não nos furos das peças."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Manter Faces Desconectadas"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Altura de Camada"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "X Inicial da Camada"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Y Inicial da Camada"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Espessura de camada da camada de base do raft. Esta camada deve ser grossa para poder aderir firmemente à mesa."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Espessura da camada intermediária do raft."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Espessura de camada das camadas superiores do raft."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Evita uma conexão entre linhas de suporte uma vez a cada N milímetros para fazer a estrutura de suporte mais fácil de ser removida."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Esquerda"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Levantar Cabeça"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Relâmpago"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Ângulo de Seção Pendente do Preenchimento Relâmpago"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Ângulo de Poda do Preenchimento Relâmpago"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Ângulo de Retificação do Preenchimento Relâmpago"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Ângulo de Suporte do Preenchimento Relâmpago"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Limitar Alcance de Galho"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Limita quão longe cada galho deve percorrer do ponto que ele suporta. Isto pode fazer o suporte mais estável, mas aumentará a quantidade de galhos (e por causa disso, uso de material e tempo de impressão)"

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "Limite do aviso de Temperatura de Volume de Construção para detecção."

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "Limite da Anomalia da temperatura de Volume de Construção para detecção."

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "Limite da anomalia da Temperatura de Construção para detecção."

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "Limite do aviso de temperatura de Impressão para detecção."

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "Limite da anomalia de fluxo para detecção."

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "Limite do aviso de fluxo para detecção."

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Limitar o volume desta malha para dentro de outras malhas. Você pode usar isto para fazer certas áreas de uma malha imprimirem com ajustes diferentes, incluindo extrusor diferente."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Limitado"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Largura de Extrusão"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "Filetes"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Linhas"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Máquina"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Profundidade da Mesa"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Polígono da Cabeça com Ventoinha"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Altura do Volume"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Tipo de Máquina"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Largura da Mesa"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Ajustes específicos da máquina"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Torna Seções Pendentes Imprimíveis"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Faz malhas que tocam uma à outra se sobreporem um pouco. Isto faz com que elas se combinem com mais força."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Faz as áreas de suporte menores na base que na seção pendente."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Cria suport em todo lugar abaixo da malha de suporte de modo que não haja seções pendentes nela."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Faz a posição de purga do extrusor absoluta ao invés de relativa à última posição conhecida da cabeça."

msgctxt "layer_0_z_overlap description"
msgid ""
"Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\n"
"It may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr ""
"Faz a primeira e segunda camadas do modelo se sobreporem na direção Z para compensar pelo filamento perdido no vão de ar. Todos os modelos sobre a primeira camada de modelo serão deslocados pra baixo por essa quantidade.\n"
"Deve ser notado que algumas vezes a segunda camada é impressa abaixo da camada inicial por causa deste ajuste. Este é o comportamento pretendido"

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Faz as malhas mais adequadas para impressão 3D."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "Gerencia a relação espacial entre a costura Z da estrutura de suporte e o modelo 3D. Este controle é crucial já que permite a usuários assegurar a remoção limpa das estruturas de suporte pós-impressão sem infligir dano ou marcas no modelo impresso."

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volumétrico)"

msgctxt "material description"
msgid "Material"
msgstr "Material"

msgctxt "material label"
msgid "Material"
msgstr "Material"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "Marca do Material"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID do Material"

msgctxt "material_type label"
msgid "Material Type"
msgstr "Tipo do Material"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Volume de Material Entre Limpezas"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Máxima Distância de Combing Sem Retração"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Aceleração Máxima em X"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Aceleração Máxima em Y"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Aceleração Máxima em Z"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Ângulo Máximo de Galho"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Desvio Máximo"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Desvio Máximo de Área de Extrusão"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Velocidade Máxima da Ventoinha"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Aceleração Máxima do Filamento"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Ângulo Máximo do Modelo"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Área Máxima de Furo de Seções Pendentes"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Duração Máxima de Descanso"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Resolução Máxima"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Contagem de Retrações Máxima"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Ângulo Máximo do Contorno para Expansão"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Velocidade Máxima de Extrusão"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Velocidade Máxima em X"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Velocidade Máxima em Y"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Velocidade Máxima em Z"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Diâmetro Máximo Suportado por Torres"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Máxima Resolução de Percurso"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Aceleração máxima para mudanças de fluxo gradual"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "A aceleração máxima para o motor da impressora na direção X"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "A aceleração máxima para o motor da impressora na direção Y."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "A aceleração máxima para o motor da impressora na direção Z."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Aceleração máxima para a entrada de filamento no hotend."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Densidade máxima do preenchimento considerado esparso. Contorno sobre o preenchimento esparso é considerado não-suportado e portanto será tratado como contorno de ponte."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Diâmetro máximo nas direções X e Y da pequena área que será suportada por uma torre especializada de suporte."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Material máximo que pode ser extrudado antes que outra limpeza de bico seja iniciada. Se este valor for menor que o volume de material requerido em uma camada, ele não terá efeito nenhum nesta camada, isto é, está limitado a uma limpeza por camada."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Sobreposição de Malhas Combinadas"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Correções de Malha"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Posição X da Malha"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Posição Y da Malha"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Posição Z da Malha"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Hierarquia do Processamento de Malha"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Matriz de Rotação da Malha"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Meio"

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "Distância Z Mínima da Costura ao Modelo"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Largura Mínima do Molde"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Tempo Mínima em Temperatura de Espera"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Comprimento de Parede de Ponte Mínimo"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Largura Mínima de Filete de Parede Par"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Janela de Distância de Extrusão Mínima"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Mínimo Tamanho de Detalhe"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Velocidade Mínima de Alimentação"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Altura Mínima Para O Modelo"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Área Mínima para Preenchimento"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Tempo Mínimo de Camada"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "Tempo Mínimo de Camada com Seção Pendente"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Largura Mínima de Filete de Parede Ímpar"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "Comprimento Mínimo do Segmento de Seção Pendente"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Mínima Circunferência do Polígono"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Largura Mínima de Contorno para Expansão"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Velocidade Mínima"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Área Mínima de Suporte"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Área Mínima de Base de Suporte"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Área Mínima de Interface de Suporte"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Área Mínima de Teto de Suporte"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Distância Mínima de Suporte X/Y"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Largura Mínima de Filete de Parede Fina"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Volume Mínimo Antes da Desengrenagem"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Largura Mínina de Filete de Parede"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Área mínima para os polígonos da interface de suporte. Polígonos que têm área menor que este valor serão impressos como suporte normal."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Área mínima para polígonos de suporte. Polígonos que tiverem uma área menor que essa não serão gerados."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Área mínima para as bases do suport. Polígonos que têm área menor que este valor serão impressos como suporte normal."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Área mínima para os tetos do suporte. Polígonos que têm área menor que este valor serão impressos como suporte normal."

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "Velocidade mínima para mudanças de fluxo gradual da primeira camada"

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "Espessura mínima de detalhes finos. Detalhes de modelo que forem mais finos que este valor não serão impressos, enquanto que detalhes mais espessos que o Tamanho Mínimo de Detalhe serão aumentados para a Largura Mínima de Filete de Parede."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Largura mínima para a qual a base do suporte cônico é reduzida. Pequenas larguras podem levar a estruturas de suporte instáveis."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Molde"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Ângulo do Molde"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Altura de Teto do Molde"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "Ordem Monotônica de Superfície Inferior"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Ordem de Passagem a Ferro Monotônica"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "Ordem do Raft Monotônico da Superfície Superior"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Ordem da Superfície Monotônica Superior"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Ordem Monotônica Superior/Inferior"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Múltiplas linhas de skirt te ajudam a fazer purga de sua extrusão melhor para pequenos modelos. Se o valor for zero o skirt é desabilitado."

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "Multiplicador para o preenchimento nas camadas iniciais do suporte. Aumentar este valor pode ajudar com aderência à mesa."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "Multiplicador da largura de extrusão da primeira camada. Aumentar este ajuste pode melhorar a aderência à mesa."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Fator de Movimento Sem Carga"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Sem Contorno nas Lacunas Z"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Jeitos não-tradicionais de imprimir seus modelos."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Nenhuma"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "Nenhuma"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Nenhum"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normalmente o Cura tenta costurar pequenos furos na malha e remover partes de uma camada com grandes furos. Habilitar esta opção mantém as partes que ele não consegue costurar. Esta opção deve ser usada como última alternativa quando tudo o mais falhar para produzir G-Code apropriado."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Não no Contorno"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Não na Superfície Externa"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Ângulo do Bico"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Diâmetro do bico"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Áreas Proibidas para o Bico"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "ID do Bico"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Quantidade de Avanço Extra da Troca de Bico"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Velocidade de Avanço da Troca de Bico"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Velocidade de Retração da Troca de Bico"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Distância de Retração da Troca de Bico"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Velocidade de Retração da Troca do Bico"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Número de extrusores"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Número de Extrusores Habilitados"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Número de Camadas Mais Lentas"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "O número de carros extrusores que estão habilitados; automaticamente ajustado em software"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Número de carros extrusores. Um carro extrusor é a combinação de um alimentador/tracionador, opcional tubo de filamento guiado e o hotend."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Número de vezes com que mover o bico através da varredura."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Número de vezes para reduzir a densidade de preenchimento pela metade quando estiver chegando mais além embaixo das superfícies superiores. Áreas que estão mais perto das superfícies superiores ganham uma densidade maior, numa gradação até a densidade configurada de preenchimento."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Número de vezes para reduzir a densidade de preenchimento de suporte pela metade quando avançando abaixo das superfícies inferiores. Áreas mais próximas ao topo terão maior densidade, até a Densidade de Preenchimento de Suporte."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Octeto"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Desligado"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Deslocamento aplicado ao objeto na direção X."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Deslocamento aplicado ao objeto na direção Y."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Deslocamento aplicado ao objeto na direção Z. Com isto você pode fazer afundamento do objeto na plataforma."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Deslocamento com o Extrusor"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Na plataforma de impressão quando possível"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "No modelo se requerido"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Um de Cada Vez"

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "Somente o último extrusor"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Somente fazer o Salto Z quando se mover sobre partes impressas que não podem ser evitadas pelo movimento horizontal quando a opção 'Evitar Peças Impressas nas Viagens' estiver ligada."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Somente executar a passagem a ferro na última camada da malha. Isto economiza tempo se as camadas abaixo não precisarem de um acabamento de superfície amaciado."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Ângulo da Cobertura de Escorrimento"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Distância da Cobertura de Escorrimento"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Alcance Ótimo de Galho"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Otimizar Ordem de Impressão de Paredes"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Otimiza a ordem em que as paredes são impressas, tais que o número de retrações e a distância percorrida sejam reduzidos. A maioria das peças se beneficiará deste ajuste habilitado mas outras poderão demorar mais, portanto compare as estimativas de tempo de impressão com e sem otimização. A primeira camada não é otimizada quando o brim é selecionado como tipo de aderência da mesa de impressão."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Diâmetro Externo do Bico"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Aceleração da Parede Exterior"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "Deceleração do Final da Parede Externa"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "Proporção de Velocidade do Fim da Parede Externa"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Extrusor da Parede Externa"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Fluxo da Parede Externa"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Penetração da Parede Externa"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Jerk da Parede Exterior"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Largura de Extrusão da Parede Externa"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Velocidade da Parede Exterior"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "Distância de Divisão de Velocidade da Parede Externa"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "Aceleração do Início da Parede Externa"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "Raio de Velocidade do Começo da Parede Externa"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Distância de Varredura da Parede Externa"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Paredes externas de ilhas diferentes na mesma camada são impressas em sequência. Quando habilitado, as mudanças de fluxo são limitadas porque as paredes são impressas um tipo a cada vez; quando desabilitado, o número de percursos entre as ilhas é reduzido porque as paredes nas mesmas ilhas são agrupadas."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "Fora Somente"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "De Fora Pra Dentro"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Ângulo de Parede Pendente"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "Velocidades das Paredes Pendentes"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "Paredes pendentes serão impressas em uma porcentagem de sua velocidade normal de impressão. Você pode especificar valores múltiplos, de forma que ainda mais paredes pendentes sejam impressas mais lentamente, por exemplo colocando [75, 50, 25]"

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Pausa após desfazimento da retração."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Porcentagem da velocidade de ventoinha a usar quando imprimir paredes e contornos em pontes."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "Porcentagem da velocidade da ventoinha a usar quando se imprimir a segunda camada de contorno da ponte."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Porcentagem de velocidade da ventoinha a usar ao imprimir as regiões de contorno imediatamente sobre o suporte. Usar uma velocidade de ventoinha alta pode fazer o suporte mais fácil de remover."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Porcentagem da velocidade da ventoinha a usar quando se imprimir a terceira camada de contorno da ponte."

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "Coloca a costura-z em um vértice de polígono. Desligar este ajuste permite colocar a costura entre vértices também. (Tenha em mente que isto não vai sobrepôr as restrições em colocar a costura em uma seção pendente não suportada.)"

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Polígonos em camadas fatiadas que tiverem uma circunferência menor que esta quantia serão excluídos. Menores valores levam a malha de maior resolução ao custo de tempo de fatiamento. Serve melhor para impressoras SLA de alta resolução e pequenos modelos 3D com muitos detalhes."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Ângulo Preferido de Galho"

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "Factor de avanço de pressão"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Impede de alternar entre uma parede a mais e uma a menos. Esta margem estende o alcance dos comprimentos de file a seguir para [Largura Mínima de Filete de Parede - Margem, 2 * Largura Mínima de Filete de Parede + Margem]. Aumentar esta margem reduz o número de transições, que por sua vez reduz o número de paradas e inícios de extrusão e tempo de percurso. No entanto, variação de largura de filete pode levar a problemas de subextrusão ou sobre-extrusão."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "Aceleração da Torre de Purga"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Base da Torre de Purga"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Altura da Base da Torre de Purga"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Tamanho da Base da Torre de Purga"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Inclinação da Base da Torre de Purga"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "Fluxo da Torre de Purga"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "Jerk da Torre de Purga"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "Largura de Extrusão da Torre de Purga"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "Distância Máxima de Ponte das Torres de Purga"

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "Espessura Mínima do Casco da Torre de Purga"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "Volume Mínimo da Torre de Purga"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Espaçamento do Filete de Raft da Torre de Purga"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "Tamanho da Torre de Purga"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "Velocidade da Torre de Purga"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "Tipo da Torre de Purga"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "Posição X da Torre de Purga"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "Posição Y da Torre de Purga"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Aceleração da Impressão"

msgctxt "variant_name"
msgid "Print Core"
msgstr "Núcleo de Impressão"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Jerk da Impressão"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "Relatório do Processo de Impressão"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Sequência de Impressão"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Velocidade de Impressão"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "Imprimir Paredes Finas"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "Imprime um brim na parte de fora do modelo, de dentro, ou ambos. Dependendo do modelo, isto ajuda a reduzir a quantidade de brim que você precisa remover depois, ao mesmo tempo em que assegura aderência apropriada na plataforma."

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Imprimir uma torre próxima à impressão que serve para purgar o material a cada troca de bico."

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime os filetes da superfície inferior em uma ordem que faz com que sempre se sobreponham com linhas adjacentes em uma direção única. Isso leva um pouco mais de tempo pra imprimir, mas faz superfícies chatas terem aspecto mais consistente."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Imprime estruturas de preenchimento somente onde os tetos do modelo devam ser suportados. Habilitar este ajuste reduz tempo de impressão e uso de material, mas leva a resistências não-uniformes no objeto."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime filetes de passagem a ferro em uma ordem que os faz com que sempre se sobreponham a linhas adjacentes em uma única direção. Isso faz levar um pouco mais de tempo na impressão, mas torna as superfícies mais consistentes."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Imprimir modelos como moldes com o negativo das peças de modo que se possa encher de resina para as gerar."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Imprime partes do modelo que são horizontalmente mais finas que o tamanho do bico."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "Imprime os filetes da superfície superior to raft em uma ordem que faz com que se sobreponham a filetes adjacentes na mesma direção. Isso leva um pouco mais de tempo para imprimir, mas faz a superfície parecer mais consistente, algo que também é visível na superfície inferior do modelo."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "Velocidade de impressão a usar quando imprimir a segunda camada de ponte."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Velocidade de impressão a usar quando imprimir a terceira camada de ponte."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "Limite de temperatura de impressão"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "Aviso de temperatura de impressão"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Imprime o preenchimento antes de imprimir as paredes. Imprimir as paredes primeiro pode levar a paredes mais precisas, mas seções pendentes são impressas com pior qualidade. Imprimir o preenchimento primeiro leva a paredes mais fortes, mas o padrão de preenchimento pode às vezes aparecer através da superfície."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime os filetes da superfície superior em uma ordem que faz com que sempre se sobreponham a linhas adjacentes em uma única direção. Faz levar um pouco mais de tempo na impressão, mas torna as superfícies planas mais consistentes."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Imprime filetes superiores e inferiores em uma ordem que os faz sempre se sobreporem a filetes adjacentes em uma única direção. Faz levar um pouco mais de tempo na impressão, mas torna superfícies planas mais consistentes."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Temperatura de Impressão"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "Temperatura de Impressão da Camada Inicial"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "Imprimir o filete mais interno de skirt com múltiplas camadas torna mais fácil removê-lo."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Imprime uma parede adicional a cada duas camadas. Deste jeito o preenchimento fica aprisionado entre estas paredes extras, resultando em impressões mais fortes."

msgctxt "resolution label"
msgid "Quality"
msgstr "Qualidade"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Quarto Cúbico"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Raft"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Vão Aéreo do Raft"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "Margem Extra da Base de Raft"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Extrusor da Base do Raft"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Velocidade de Ventoinha da Base do Raft"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "Fluxo da Base do Raft"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "Sobreposição do Preenchimento da Base do Raft"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "Porcentagem de Sobreposição do Preenchimento da Base do Raft"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Espaçamento de Filete de Base do Raft"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Largura de Linha da Base do Raft"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Aceleração de Impressão da Base do Raft"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Jerk de Impressão da Base do Raft"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Velocidade de Impressão da Base do Raft"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "Suavização da Base de Raft"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Espessura da Base do Raft"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Contagem de Paredes da Base do Raft"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Margem Adicional do Raft"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Velocidade de Ventoinha no Raft"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "Fluxo do Raft"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "Fluxo da Interface do Raft"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "Sobreposição do Preenchimento da Interface do Raft"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "Porcentagem de Sobreposição do Preenchimento da Interface do Raft"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "Deslocamento Z da Interface do Raft"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "Margem Extra do Meio do Raft"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Extrusor do Meio do Raft"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Velocidade de Ventoinha do Meio do Raft"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Camadas Centrais do Raft"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Largura da Linha do Meio do Raft"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Aceleração de Impressão do Meio do Raft"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Jerk de Impressão do Meio do Raft"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Velocidade de Impressão do Meio do Raft"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "Suavização do Meio do Raft"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Espaçamento do Meio do Raft"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Espessura do Meio do Raft"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "Contagem de Paredes do Meio do Raft"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Aceleração de Impressão do Raft"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Jerk de Impressão do Raft"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Velocidade de Impressão do Raft"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Amaciamento do Raft"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "Fluxo da Superfície do Raft"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "Sobreposição do Preenchimento da Superfície do Raft"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "Porcentagem de Sobreposição do Preenchimento da Superfície do Raft"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "Deslocamento Z da Superfície do Raft"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "Margem Extra do Topo do Raft"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Extrusor do Topo do Raft"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Velocidade da Ventoinha para o Topo do Raft"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Espessura da Camada Superior do Raft"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Camadas Superiores do Raft"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Largura do Filete Superior do Raft"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Aceleração de Impressão do Topo do Raft"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Jerk de Impressão do Topo do Raft"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Velocidade de Impressão do Topo do Raft"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "Suavização do Topo do Raft"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Espaçamento Superior do Raft"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "Contagem de Paredes do Topo do Raft"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "Contagem de Paredes do Raft"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Aleatório"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Aleatorizar o Começo do Preenchimento"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Aleatoriza qual linha do preenchimento é impressa primeiro. Isto evita que um segmento seja mais forte que os outros, mas ao custo de um percurso adicional."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Faz flutuações de movimento aleatório enquanto imprime a parede mais externa, de modo que a superfície do objeto ganhe uma aparência felpuda ou acidentada."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Retangular"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Velocidade Regular da Ventoinha"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Velocidade Regular da Ventoinha na Altura"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Velocidade Regular da Ventoinha na Camada"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Limite de Tempo para Mudança de Velocidade da Ventoinha"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Extrusão Relativa"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Remover Todos os Furos"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Remover Camadas Iniciais Vazias"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Remover Interseções de Malha"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "Remover Cantos Internos da Base do Raft"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Remover Cantos Internos de Raft"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "Remover Cantos Internos do Meio do Raft"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "Remover Cantos Internos do Topo do Raft"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Remove áreas onde várias malhas estão sobrepondo uma à outra. Isto pode ser usado se objetos de material duplo se sobrepõem um ao outro."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Remove camadas vazias entre a primeira camada impressa se estiverem presentes. Desabilitar este ajuste pode criar camadas iniciais vazias se a Tolerância de Fatiamento estiver configurada para Exclusivo ou Meio."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "Remove cantos interior da base do raft, fazendo com que o raft se torne convexo."

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "Remove cantos internos da parte média do raft, fazendo com que o raft se torne convexo."

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "Remove cantos internos da parte superior do raft, fazendo com que o raft se torne convexo."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Remove os cantos internos do raft, fazendo com que ele se torne convexo."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Remove os furos de cada camada e mantém somente aqueles da forma externa. Isto ignorará qualquer geometria interna invisível. No entanto, também ignorará furos de camada que poderiam ser vistos de cima ou de baixo."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Substitui a parte externa do padrão superior/inferir com um número de linhas concêntricas. Usar uma ou duas linhas melhora tetos e topos que começam a ser construídos em cima de padrões de preenchimento."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "Relatar eventos que saem dos limites estabelecidos"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Duração de reset de fluxo"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Preferência de Descanso"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Retrair Antes da Parede Externa"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Retrai em Mudança de Camada"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Retrair o filamento quando o bico estiver se movendo sobre uma área não-impressa."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Retrair o filamento quando o bico se mover sobre uma área não impressa."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Retrai o filamento quando o bico está se movendo para a próxima camada."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Distância da Retração"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Quantidade Adicional de Avanço da Retração"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Percurso Mínimo para Retração"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Velocidade de Avanço da Retração"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Velocidade de Recolhimento de Retração"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Velocidade de Retração"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Direita"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Velocidade de Escala da Ventoinha A 0-1"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Usa a escala da velocidade da ventoinha como um número entre 0 e 1 ao invés de 0 a 256."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Compensação de Fator de Encolhimento"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "Comprimento da Emenda Scarf"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "Altura Inicial da Emenda Scarf"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "Comprimento de Passo da Emenda Scarf"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "A Cena Tem Malhas de Suporte"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Preferência do Canto da Costura"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "Ângulo da Parede Pendente para Costura"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Definir sequência de impressão manualmente"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Estabelece a altura da cobertura de trabalho. Escolha imprimir a cobertura na altura total dos modelos ou até uma altura limitada."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Ajustes usados para imprimir com vários extrusores."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Ajustes que sào usados somentes se o CuraEngine não for chamado da interface do Cura."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Retração Inicial do Bico Compartilhado"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "Canto Mais Agudo"

msgctxt "shell description"
msgid "Shell"
msgstr "Perímetro"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "Mais Curto"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Exibir Variantes de Máquina"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Camadas do Suporte da Aresta de Contorno"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Espessura do Suporte da Aresta de Contorno"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Distância de Expansão do Contorno"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Sobreposição do Contorno"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Porcentagem de Sobreposição do Contorno"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Largura de Remoção de Contorno"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Áreas de contorno mais estreitas que esta não são expandidas. Isto evita expandir as áreas estreitas que são criadas quando a superfície do modelo tem inclinações quase verticais."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Evitar uma em cada N linhas de conexão para fazer a estrutura de suporte mais fácil de ser removida."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Evitar algumas conexões de linha de suporte para fazer a estrutura de suporte mais fácil de ser removida. Este ajuste é aplicável ao padrão de preenchimento de suporte de ziguezague."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Skirt"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Distância do Skirt"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Altura do Skirt"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Contagem de linhas de Skirt"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Aceleração para Skirt e Brim"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Extrusor do Skirt/Brim"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Fluxo de Skirt/Brim"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Jerk de Skirt e Brim"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Largura de Extrusão do Brim e Skirt"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Mínimo Comprimento do Skirt e Brim"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Velocidade do Skirt e Brim"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Tolerância de Fatiamento"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Velocidade de Camada Inicial de Aspecto Pequeno"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Comprimento Máximo do Aspecto Pequeno"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Velocidade de Aspecto Pequeno"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Tamanho Máximo de Furos Pequenos"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Temperatura de Impressão Final"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Pequena Base/Teto Na Superfície"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Largura do Teto/Base Pequenos"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Aspectos pequenos na primeira camada serão impressos nesta porcentagem de sua velocidade de impressão normal. Impressão mais lenta pode ajudar com aderência e precisão."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Aspectos pequenos serão impressos nessa porcentagem da velocidade normal. Impressão mais lenta pode ajudar com aderência e precisão."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Regiões pequenas de base/teto são preenchidas com paredes ao invés do padrão default de teto/base. Isto ajuda a prevenir movimentos bruscos. Desligado por default para a camada superior (exposta ao ar) (Veja 'Pequena Base/Teto Na Superfície')"

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Brim Inteligente"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Ocultação Inteligente"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Suavizar Contornos Espiralizados"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Suavizar os contornos espiralizados para reduzir a visibilidade da costura Z (a costura Z deve ser quase invisível na impressão mas ainda será visível na visão de camadas). Note que a suavização tenderá a embaçar detalhes finos de superfície."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Alguns materiais podem escorrer um pouco durante o percurso, o que pode ser compensando neste ajuste."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Um pouco de material pode escorrer durante os movimentos do percurso de limpeza e isso pode ser compensado neste ajuste."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Modos Especiais"

msgctxt "speed description"
msgid "Speed"
msgstr "Velocidade"

msgctxt "speed label"
msgid "Speed"
msgstr "Velocidade"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Velocidade com que mover o eixo Z durante o salto."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Espiralizar o Contorno Externo"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "'Espiralizar' faz com que o movimento vertical (em Z) seja contínuo e gradual seguindo o contorno da peça. Este recurso transforma um modelo sólido em uma simples linha contínua em espiral partindo de uma base sólida. O recurso só deve ser habilitado quando cada camada horizontal contiver somente um contorno."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Temperatura de Espera"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "G-Code Inicial"

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "O GCode de Início deve ser o primeiro"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Ponto de partida de cada caminho em uma camada. Quando caminhos em camadas consecutivas iniciam no mesmo ponto (X,Y), uma 'costura' vertical pode ser vista na impressão. Quando se alinha esta costura a uma coordenada especificada pelo usuário, a costura é mais fácil de remover pós-impressão. Quando colocada aleatoriamente as bolhinhas do início dos caminhos será menos perceptível. Quando se toma o menor caminho, a impressão será mais rápida."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Passos por Milímetro (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Passos por Milímetro (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Passos por Milímetro (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Passos por Milímetro (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Suporte"

msgctxt "support label"
msgid "Support"
msgstr "Suporte"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Aceleração do Suporte"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Distância Inferior do Suporte"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Contagem de Linhas de Parede de Suporte"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Número de Filetes do Brim de Suporte"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Largura do Brim de Suporte"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Contagem de Linhas de Pedaço de Suporte"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Tamanho do Pedaço de Suporte"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Densidade do Suporte"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Prioridade das Distâncias de Suporte"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Extrusor do Suporte"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Aceleração da Base do Suporte"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Densidade da Base do Suporte"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Extrusor da Base do Suporte"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Fluxo da Base de Suporte"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Expansão Horizontal da Base do Suporte"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Jerk da Base do Suporte"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Direções de Filete da Base do Suporte"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Distância de Filetes da Base de Suporte"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Largura de Extrusão da Base do Suporte"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Padrão de Base de Suporte"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Velocidade de Base do Suporte"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Espessura da Base de Suporte"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Fluxo de Suporte"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Expansão Horizontal do Suporte"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Aceleração do Preenchimento do Suporte"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "Camada Inicial do Multiplicador de Densidade de Preenchimento de Suporte"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Extrusor do Preenchimento do Suporte"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Jerk de Preenchimento de Suporte"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Espessura de Camada do Preenchimento de Suporte"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Direção de Filete do Preenchimento de Suporte"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Velocidade do Preenchimento do Suporte"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Aceleração da Interface de Suporte"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Densidade da Interface de Suporte"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Extrusor da Interface de Suporte"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Fluxo de Interface de Suporte"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Expansão Horizontal da Interface de Suporte"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Jerk da Interface de Suporte"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Direções do Filete de Interface de Suporte"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Largura de Extrusão da Interface do Suporte"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Padrão da Interface de Suporte"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Prioridade de Interface de Suporte"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Velocidade da Interface de Suporte"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Espessura da Interface de Suporte"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Contagem de Linhas de Parede de Suporte"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Jerk do Suporte"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Distância de União do Suporte"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Distância das Linhas do Suporte"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Largura de Extrusão do Suporte"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Malha de Suporte"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Ângulo para Caracterizar Seções Pendentes"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Padrão do Suporte"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Colocação dos Suportes"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Aceleração do Teto de Suporte"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Densidade do Teto de Suporte"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Extrusor do Teto do Suporte"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Fluxo do Teto de Suporte"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Expansão Horizontal do Teto de Suporte"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Jerk do Teto de Suporte"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Direções de Filete do Teto do Suporte"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Distância de Filetes do Teto de Suporte"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Largura de Extrusão do Teto do Suporte"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Padrão de Teto de Suporte"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Velocidade do Teto de Suporte"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Espessura do Topo do Suporte"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Contagem de Linhas de Parede de Suporte"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Velocidade do Suporte"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Altura do Passo de Suporte em Escada"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Largura Máxima do Passo de Suporte em Escada"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Ângulo Mínimo de Inclinação do Passo de Suporte em Escada"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Estrutura de Suporte"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Distância Superior do Suporte"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Contagem de Linhas de Parede de Suporte"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Distância X/Y do Suporte"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Distância em Z do Suporte"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "Suportar a Costura Z longe do Modelo"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Filetes de suporte preferidos"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Suporte preferido"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Velocidade de Ventoinha do Contorno Suportado"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Superfície"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Energia de Superfície"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Modo de Superficie"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Tendência de aderência da superfície."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Energia de superfície."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "Troca a ordem de impressão do filete de brim mais interno e o segundo mais interno. Isto melhora a remoção do brim."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Troca quais volumes sobrepondo malhas vão pertencer a cada camada, de modo que as malhas sobrepostas se tornem entrelaçadas. Desligar esta opção vai fazer com que uma das malhas obtenha todo o volume da sobreposiçào, removendo este volume das outras malhas."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "Trata da distância horizontal entre duas camadas adjacentes. Reduzir este ajuste faz com que camadas mais finas sejam usadas para reunir as bordas das camadas mais perto uma da outra."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "A coordenada X da posição próxima de onde achar a parte com que começar a imprimir cada camada."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "A coordenada X da posição onde iniciar a impressão de cada parte em uma camada."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "A coordenada X da posição onde o bico faz a purga no início da impressão."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "A coordenada Y da posição próxima de onde achar a parte com que começar a imprimir cada camada."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "A coordenada Y da posição onde iniciar a impressão de cada parte em uma camada."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "A coordenada Y da posição onde o bico faz a purga no início da impressão."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Coordenada Z da posição onde o bico faz a purga no início da impressão."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "Aceleração durante a impressão da camada inicial."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "Aceleração para a camada inicial."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "Aceleração para percursos na camada inicial."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "A mudança instantânea máxima de velocidade em uma direção nos percursos da camada inicial."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "Aceleração com que se imprimem as paredes interiores."

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "A aceleração com a qual as camadas do contorno da superfície inferior serão impressas."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "A aceleração com que o preenchimento é impresso."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "A aceleração com que o recurso de passar a ferro é feito."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "Aceleração com que se realiza a impressão."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "A aceleração com que as camadas de base do raft são impressas."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "A aceleração com que as paredes internas da superfície inferior são impressas."

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "A aceleração com que as paredes mais externas da superfície inferior são impressas."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "A aceleração com que as bases do suporte são impressas. Imprimi-las em aceleração menor pode melhorar aderência dos suportes no topo da superfície."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Aceleração com que se imprime o preenchimento dos suportes."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "A aceleração com que a camada intermediária do raft é impressa."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "Aceleração com que se imprime a parede exterior."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "Aceleração com que a torre de purga é impressa."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "A aceleração com que o raft é impresso."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "A aceleração com que os tetos e bases de suporte são impressos. Imprimi-los em aceleração menor pode melhorar a qualidade das seções pendentes."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "A aceleração com que os tetos de suporte são impressos. Imprimi-los em aceleração menor pode melhorar a qualidade das seções pendentes."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Aceleração com a qual o skirt e o brim são impressos. Normalmente isto é feito com a aceleração de camada inicial, mas às vezes você pode querer imprimir o skirt ou brim em uma aceleração diferente."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Aceleração com que as estruturas de suporte são impressas."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "A aceleração com que as camadas superiores do raft são impressas."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "A aceleração com que as paredes internas da superfície superior são impressas."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "A aceleração com que as paredes externas da superfície superior são impressas."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Aceleração com que se imprimem as paredes."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "A aceleração com a qual as camadas da superfície superior são impressas."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Aceleração com que as camadas superiores e inferiores são impressas."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Aceleração com que se realizam os percursos."

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "A quantidade de material relativa a um filamento normal de extrusão a extrudar durante a impressão da base do raft. Ter um fluxo mais alto pode melhorar a aderência e a força estrutural do raft."

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "A quantidade de material relativa a um filamento normal de extrusão a extrudar durante a impressão da interface de raft. Ter um fluxo mais alto pode melhorar a aderência e a força estrutural do raft."

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "A quantidade de material relativa a um filamento normal de extrusão a extrudar durante a impressão do raft. Ter um fluxo mais alto pode melhorar a aderência e a força estrutural do raft."

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "A quantidade de material relativa a um filamento normal de extrusão a extrudar durante a impressão da superfície do raft. Ter um fluxo mais alto pode melhorar a aderência e a força estrutural do raft."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "A quantidade de material, relativa ao filete normal de extrusão, para extrudar durante a passagem a ferro. Manter o bico com algum material ajuda a preencher algumas das lacunas e fendas da superfície superior, mas material demais resulta em superextrusão e verrugas nas laterais da superfície."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes como uma porcentagem da largura de extrusão de preenchimento. Uma leve sobreposição permite que as paredes se conectem firmemente ao preenchimento."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da base do raft, como uma porcentagem da largura do filete de preenchimento. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da base do raft. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da interface do raft, como uma porcentagem da largura do filete de preenchimento. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da interface do raft. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da superfície do raft, como uma porcentagem da largura do filete de preenchimento. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes da superfície do raft. Uma sobreposição leve permite às paredes conectarem-se firmemente ao preenchimento."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "A quantidade de sobreposição entre o preenchimento e as paredes. Uma leve sobreposição permite que as paredes se conectem firmemente ao preenchimento."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "A quantidade de retração ao mudar extrusores. Coloque em 0 para não haver retração. Isto deve geralmente ser o mesmo que o comprimento da zona de aquecimento do hotend."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "Ângulo entre o plano horizontal e a parte cônica logo acima da ponta do bico."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "Ângulo do Teto (parte superior) de uma torre. Um valor maior resulta em tetos pontiagudos, um valor menor resulta em tetos achatados."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "O ângulo de seção pendente das paredes externas criadas para o molde. 0° fará a superfície externa do molde vertical, enquanto 90° fará a superfície externa do molde seguir o contorno do modelo."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "O ângulo do diâmetro dos galhos enquanto se tornam gradualmente mais grossos na direção da base. Um ângulo de 0 fará com que os galhos tenham grossura uniforme no seu comrpimento. Um ângulo levemente maior que zero pode aumentar a estabilidade do suporte em árvore."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "O ângulo da inclinação do suporte cônico. Como 0 graus sendo vertical e 90 graus sendo horizontal. Ângulos menores farão o suporte ser mais firme, mas gastarão mais material. Ângulos negativos farão a base do suporte mais larga que o topo."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "A densidade média dos pontos introduzidos em cada polígono de uma camada. Note que os pontos originais do polígono são descartados, portanto uma densidade baixa resulta da redução de resolução."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "A distância média entre os pontos aleatórios introduzidos em cada segmento de linha. Note que os pontos originais do polígono são descartados, portanto umo alto alisamento resulta em redução da resolução. Este valor deve ser maior que a metade da Espessura do Contorno Felpudo."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "A marca de material usado."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "A aceleração default a ser usada nos eixos para o movimento da cabeça de impressão."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "A temperatura default usada para a impressão. Esta deve ser a temperatura \"base\" de um material. Todas as outras temperaturas de impressão devem usar diferenças baseadas neste valor"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "A temperatura default usada para a plataforma aquecida de impressão. Este valor deve ser a temperatura \"base\" da plataforma. Todas as outras temperaturas de impressão devem usar diferenças baseadas neste valor"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A densidade da camada de contorno de ponte. Valores menores que 100 aumentarão a lacuna entre as linhas de contorno."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "A densidade das bases da estrutura de suporte. Um valor maior resulta em melhor aderência do suporte no topo da superfície."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "A densidade dos tetos da estrutura de suporte. Um valor maior resulta em seções pendentes melhores, mas os suportes são mais difíceis de remover."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A densidade da segunda camada de contorno da ponte. Valores menores que 100 aumentarão a lacuna entre as linhas de contorno."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "A densidade da terceira camada de contorno da ponte. Valores menores que 100 aumentarão a lacuna entre as linhas de contorno."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "A profundidade (direção Y) da área imprimível."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "O diâmetro da torre especial."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "O diâmetro dos galhos mais finos do suporte em árvore. Galhos mais grossos são mais resistentes. Galhos na direção da base serão mais grossos que essa medida."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "O diâmetro do topo da ponta dos galhos de suporte em árvore."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "O diâmetro da engrenagem que traciona o material no alimentador."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "O diâmetro dos galhos mais espessos do suporte em árvore. Um tronco mais espesso é mais robusto; um tronco mais fino ocupa menos espaço na plataforma de impressão."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "A diferença em tamanho da próxima camada comparada à anterior."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "As dimensões da cabeça de impressão usadas para determinar a 'Distância de Modo Seguro' ao imprimir 'Um de Cada Vez'. Esses número se relacionam ao filete central do bico do primeiro extrusor. À esquerda do bico é 'X Mínimo' e deve ser negativo.  A parte de trás do bico é 'Y Mínimo' e deve ser negativa.  X Máximo (direita) e Y Máximo (frente) são números positivos.  Altura do eixo é a dimensão da plataforma de impressão até a barra do eixo X."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "A distância entre as trajetórias de passagem a ferro."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "A distância entre o modelo e sua estrutura de suporta na costura do eixo Z."

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "A distância entre o bico e paredes externas já impressas ao percorrer no interior do modelo."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "A distância entre o bico e as partes já impressas quando evitadas durante o percurso."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "A distância entre as linhas do raft para a camada de base do raft. Um espaçamento esparso permite a remoção fácil do raft da mesa."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "A distância entre as linhas do raft para a camada intermediária. O espaçamento do meio deve ser grande, ao mesmo tempo que deve ser denso o suficiente para suportar as camadas superiores."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Distância entre as linhas do raft para as camadas superiores. O espaçamento deve ser igual à largura de linha, de modo que a superfície seja sólida."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "A distância entre os filetes do raft para a camada única de raft de torre de purga. Espaçamento alto permite remoção fácil do raft da plataforma de impressão."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "A distância da fronteira entre os modelos para gerar a estrutura de interligação, medida em células. Poucas células resultam em baixa aderência."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "A distância do modelo à linha mais externa do brim. Um brim mais largo aumenta a aderência à mesa, mas também reduz a área efetiva de impressão."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "Distância da ponta do bico onde 'estacionar' o filamento quando seu extrusor não estiver sendo usado."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Distância da ponta do bico, em que calor do bico é transferido para o filamento."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "A distância com que os contornos inferiores são expandidos para dentro do preenchimento. Valores mais altos fazem o contorno se anexar melhor ao padrão de preenchimento e fazem as paredes da camada abaixo aderirem melhor ao contorno. Valores mais baixos economizam a quantidade de material usado."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "A distância em que os contornos são expandidos pra dentro do preenchimento. Valores mais altos fazem o contorno aderir melhor ao padrão de preenchimento e faz as paredes de camadas vizinhas aderirem melhor ao contorno. Valores menores diminuem a quantidade de material usado."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "A distância com que os contornos superiores são expandidos para dentro do preenchimento. Valores mais altos fazem o contorno se anexar melhor ao padrão de preenchimento e fazem as paredes da camada acima aderirem melhor ao contorno. Valores mais baixos economizam a quantidade de material usado."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "A distância com que mover a cabeça pra frente e pra trás durante a varredura."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "As pontas dos filetes de preenchimento são encurtadas para poupar material. Este ajuste é o ângulo da seção pendente das pontas desses filetes."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Velocidade adicional pela qual o bico resfria enquanto extruda. O mesmo valor é uso para denotar a velocidade de aquecimento quando se esquenta ao extrudar."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir a primeira camada de preenchimento de suporte. Isto é utilizado em multi-extrusão."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "O carro extrusor a ser usado para imprimir a primeira camada do Raft. Isto é usado em multi-extrusão."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir as bases dos suportes. Isto é utilizado em multi-extrusão."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir o preenchimento do suporte. Isto é utilizado em multi-extrusão."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "O carro extrusor a ser usado para imprimir a camada central do raft. Isto é usado em multi-extrusão."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir os tetos e bases dos suportes. Isto é utilizado em multi-extrusão."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir o teto do suporte. Isto é utilizado em multi-extrusão."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "O carro extrusor a ser usado para imprimir o skirt ou brim. Isto é usado em multi-extrusão."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "O extrusor usado ara imprimir skirt, brim ou raft. Usado em multi-extrusão."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "O extrusor a usar para imprimir os suportes. Isto é utilizado em multi-extrusão."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "O carro extrusor a ser usado para imprimir a(s) camada(s) central(is) do raft. Isto é usado em multi-extrusão."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir preenchimento. Este ajuste é usado em multi-extrusão."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "O carro de extrusor usado para imprimir o contorno mais inferior. Isto é usado em multi-extrusão."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir as paredes internas. Este ajuste é usado em multi-extrusão."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir a parede externa. Este ajuste é usado em multi-extrusão."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir as paredes superiores e inferiores. Este ajuste é usado na multi-extrusão."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir a parte superior da peça. Este ajuste é usado em multi-extrusão."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "O carro extrusor usado para imprimir paredes. Este ajuste é usado em multi-extrusão."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "A velocidade de ventoinha para a camada base do raft."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "A velocidade de ventoina para a camada intermediária do raft."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "A velocidade da ventoinha para a impressão do raft."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "A velocidade da ventoinha para as camadas superiores do raft."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "A localização do arquivo de imagem onde os valores de brilho determinam a densidade mínima no local correspondente do preenchimento da impressão."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "A localização do arquivo de imagem onde os valores de brilho determinam a densidade mínima no local correspondente do suporte."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "As poucas primeiras camadas são impressas mais devagar que o resto do modelo, para conseguir melhor aderência à mesa e melhorar a taxa de sucesso geral das impressão. A velocidade é gradualmente aumentada entre estas camadas."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "O vão entre a camada final do raft e a primeira camada do modelo. Somente a primeira camada é elevada por esta distância para enfraquecer a conexão entre o raft e o modelo, tornando mais fácil a remoção do raft."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "A altura (direção Z) do volume imprimível."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "A altura acima das partes horizontais do modelo onde criar o molde."

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "A altura em que as ventoinhas giram na velocidade regular. Nas camadas abaixo a velocidade de ventoinha gradualmente aumenta da Velocidade Inicial de Ventoinha para a Velocidade Regular."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "A altura em que as ventoinhas girarão na velocidade regular. Nas camadas abaixo a velocidade da ventoinha gradualmente aumenta da velocidade inicial para a velocidade regular."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Diferença de altura entre a ponta do bico e o sistema de eixos X ou X e Y (onde o extrusor desliza)."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "A diferença de altura ao executar um Salto Z após trocar extrusores."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Diferença de altura ao realizar um Salto Z."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "A diferença de altura ao executar um Salto Z."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "A altura das camadas em mm. Valores mais altos produzem impressões mais rápidas em resoluções baixas, valores mais baixos produzem impressão mais lentas em resolução mais alta. Recomenda-se não deixar a altura de camada maior que 80% do diâmetro do bico."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "A altura do preenchimento de uma dada densidade antes de trocar para a metade desta densidade."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "A altura do preenchimento de suporte de dada densidade antes de trocar para metade desta densidade."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "A altura das vigas da estrutura de interligação, medida em número de camadas. Menos camadas são mais fortes, mas mais susceptíveis a defeitos."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "A altura das vigas da estrutura de interligação, medidas em número de camadas. Menos camadas são mais fortes, mas mais susceptíveis a defeitos."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "A altura da camada inicial em mm. Uma camada inicial mais espessa faz a aderência à mesa de impressão ser maior."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "A altura da base da torre de purga. Aumentar este valor resultará em uma torre de purga mais firme porque a base se tornará mais larga. Se o ajuste for muito baixo, a torre de purga não terá uma base firme."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "A altura dos degraus da base estilo escada do suporte em cima do modelo. Um valor baixo faz o suporte mais difícil de remover, mas valores muito altos podem levar a estruturas de suporte instáveis. Deixe em zero para desligar o comportamento de escada."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "A distância horizontal entre o primeiro filete de brim e o contorno da primeira camada da impressão. Um pequeno vão pode fazer o brim mais fácil de remover sem deixar de prover os benefícios térmicos."

msgctxt "skirt_gap description"
msgid ""
"The horizontal distance between the skirt and the first layer of the print.\n"
"This is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr ""
"A distância horizontal entre o skirt a primeira camada da impressão.\n"
"Esta é a distância mínima. Linhas múltiplas de skirt estenderão além desta distância."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Os filetes de preenchimentos são retificados para poupar tempo de impressão. Este é o ângulo máximo de seção pendente permito através do comprimento do filete de preenchimento."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "O padrão de preenchimento é movido por esta distância no eixo X."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "O padrão de preenchimento é movido por esta distância no eixo Y."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "O diâmetro interior do bico (o orifício). Altere este ajuste quanto estiver usando um tamanho de bico fora do padrão."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "O jerk com o qual a camada de base do raft é impressa."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "O jerk com o qual a camada intermediária do raft é impressa."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "O jerk com o qual o raft é impresso."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "O jerk com o qual as camadas superiores do raft são impressas."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "A maior largura das áreas de contorno inferiores que serão removidas. Cada área de contorno menor que este valor desaparecerá. Isto pode ajudar em limitar a quantidade de tempo e material gastos em impressão de contornos inferiores em superfícies inclinadas do modelo."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "A maior largura das áreas de contorno que serão removidas. Cada área de contorno menor que este valor desaparecerá. Isto pode ajudar em limitar a quantidade de tempo e material gastos em impressão de contornos inferiores e superiores em superfícies inclinadas do modelo."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "A maior largura das áreas de contorno superiores que serão removidas. Cada área de contorno menor que este valor desaparecerá. Isto pode ajudar em limitar a quantidade de tempo e material gastos em impressão de contornos superiores em superfícies inclinadas do modelo."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "A camada em que as ventoinhas giram na velocidade total. Este valor é calculado e arredondado para um número inteiro."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "A camada em que as ventoinhas girarão na velocidade regular. Se a 'velocidade regular na altura' estiver ajustada, este valor é calculado e arredondado para um número inteiro."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "O tempo de camada que define o limite entre a velocidade regular da ventoinha e a máxima. Camadas cuja impressão é mais lenta que este tempo usarão a velocidade regular. Camadas mais rápidas gradualmente aumentarão até a velocidade máxima de ventoinha."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "O comprimento de filamento retornado durante uma retração."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "O fator de magnitude usado para a inclinação da base da torre de purga. Se você aumentar este valor, a base se tornará mais fina. Se você diminuí-lo, ela se tornará mais grossa."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "O material da plataforma de impressão presente na impressora."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "A variação de altura máxima permitida para a camada de base."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "O ângulo de separação máximo que partes da cobertura de escorrimento terão. Com 0 graus sendo na vertical e 90 graus sendo horizontal. Um ângulo menor leva a coberturas de escorrimento falhando menos, mas mais gasto de material."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "O ângulo máximo de seçọes pendentes depois de se tornarem imprimíveis. Com o valor de 0° todas as seções pendentes serão trocadas por uma parte do modelo conectada à mesa e 90° não mudará o modelo."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "O ângulo máximo dos galhos quando eles crescem em volta do modelo. Use um ângulo menor para torná-los mais verticais e estáveis. Use um ângulo maior para poder ter maior alcance."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "A área máxima de um furo na base do modelo antes que seja removido por \"Torna Seções Pendentes Imprimíveis\". Furos com área menor que esta serão retidos. O valor de 0 mm² preenche todos os furos na base do modelo."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "O desvio máximo permitido ao reduzir a resolução para o ajuste de Máxima Resolução. Se você aumentar isto, a impressão será menos precisa, mas o G-Code será menor. O Desvio Máximo é um limite para Resolução Máxima, portanto se os dois conflitarem o Desvio Máximo sempre será o valor dominante."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "A distância máxima entre as estruturas de suporte nas direções X/Y. Quando estruturas separadas estão mais próximas que este valor, elas são fundidas em uma só."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "A distância máxima em mm para mover o filamento para compensar mudanças na taxa de fluxo."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "O desvio máximo da área de extrusão permitido ao remover pontos intermediários de uma linha reta. Um ponto intermediário pode servir como ponto de mudança de largura em uma longa linha reta. Portanto, se ele for removido, fará com que a linha tenha uma largura uniforme e, como resultado, perderá (ou ganhará) um pouco de área de extrusão. Se você aumentar o valor, você poderá perceber uma sutil sobre-extrusão ou sub-extrusão no meio de paredes retas paralelas, já que mais pontos intermediários com espessura variante poderão ser removidos. Sua impressão será menos acurada, mas o G-Code será menor."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "A mudança instantânea máxima de velocidade em uma direção durante a impressão da camada inicial."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "A mudança instantânea máxima de velocidade em uma direção da cabeça de impressão."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que o recurso de passar a ferro é feito."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que as paredes internas são impressas."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "A máxima mudança instantânea de velocidade com que as camadas de contorno da superfície inferior são impressas."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "A mudança instantânea máxima de velocidade em uma direção com que o preenchimento é impresso."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "A máxima mudança instantânea de velocidade com que as paredes internas da superfície inferior são impressas."

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "A máxima mudança instantânea de velocidade com quem as paredes externas da superfície inferior são impressas."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "A máxima mudança de velocidade instantânea com que as bases dos suportes são impressas."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que o preenchimento do suporte é impresso."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que a parede externa é impressa."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "A mudança instantânea máxima de velocidade em uma direção com que a torre de purga é impressa."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "A máxima mudança de velocidade instantânea com a qual os tetos e bases dos suportes são impressos."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "A máxima mudança de velocidade instantânea com que os tetos dos suportes são impressos."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "A mudança instantânea máxima de velocidade em uma direção com que o skirt (saia) e brim (bainha) são impressos."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que as estruturas de suporte são impressas."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "A mudança máxima de velocidade instantânea com que as paredes internas da superfície superior são impressas."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "A mudança máxima de velocidade instantânea com que as paredes mais externas da superfície superior são impressas."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que as paredes são impressas."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que as camadas da superfície superior são impressas."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "A máxima mudança de velocidade instantânea em uma direção com que as camadas superiores e inferiores são impressas."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "A mudança instantânea máxima de velocidade em uma direção com que os percursos são feitos."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "O comprimento máximo dos galhos que serão impressos no ar."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "A velocidade máxima para o motor da impressora na direção X."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "A velocidade máxima para o motor da impressora na direção Y."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "A velocidade máxima para o motor da impressora na direção Z."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "A velocidade máxima de entrada de filamento no hotend."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "A largura máxima dos passos da base estilo escada do suporte em cima do modelo. Um valor baixo faz o suporte mais difícil de remover, mas valores muito altos podem levar a estruturas de suporte instáveis."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "A distância mínima entre o exterior do molde e o exterior do modelo."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Velocidade mínima de entrada de filamento no hotend."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "A temperatura mínima enquanto se esquenta até a Temperatura de Impressão na qual a impressão pode já ser iniciada."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Tempo mínimo em que um extrusor precisará estar inativo antes que o bico seja resfriado. Somente quando o extrusor não for usado por um tempo maior que esse, lhe será permitido resfriar até a temperatura de espera."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "O ângulo mínimo de seções pendentes internas para as quais o preenchimento é adicionado. Em um valor de 0°, objetos são completamente preenchidos no padrão escolhido, e 90° torna o volume oco, sem preenchimento."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "O ângulo mínimo de seções pendentes para os quais o suporte é criado. Com o valor de 0° todas as seções pendentes serão suportadas, e 90° não criará nenhum suporte."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "A distância mínima de percurso necessária para que uma retração aconteça. Isto ajuda a ter menos retrações em uma área pequena."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "O comprimento mínimo do skirt ou brim. Se este comprimento não for cumprido por todas as linhas do skirt ou brim juntas, mais linhas serão adicionadas até que o mínimo comprimento seja alcançado. Se a contagem de linhas estiver em 0, isto é ignorado."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "A mínima largura de extrusão para paredes multifiletes de preenchimento de vão de filete central. Este ajuste determina em que espessura de modelo nós alternamos de imprimir dois filetes de parede para imprimir duas paredes externas e uma parede central no meio. Uma Largura de Extrusão de Parede Ímpar Mínima mais alta leva a uma largura máxima de extrusão de parede par mais alta. A largura máxima de extrusão de parede ímpar é calculada como 2 * Largura Mínima de Extrusão de Parede Par."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "A mínima largura de filete para paredes poligonais normais. Este ajuste determina em que espessura do modelo nós alternamos da impressão de um file de parede fina único para a impressão de dois filetes de parede. Uma Largura Mínima de Filete de Parede Par mais alta leva a uma largura máxima de filete de parede ímpar também mais alta. A largura máxima de filete de parede par é calculada como a Largura de Filete da Parede Externa + 0.5 * Largura Mínima de Filete de Parede Ímpar."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "A velocidade mínima de impressão, mesmo que se tente desacelerar para obedecer ao tempo mínimo de camada. Quando a impressora desacelera demais, a pressão no bico pode ficar muito baixa, o que resulta em baixa qualidade de impressão."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "O tamanho mínimo de um segmento de linha após o fatiamento. Se você aumentar este valor, a malha terá uma resolução menor. Isto pode permitir que a impressora mantenha a velocidade que precisa para processar o G-Code e aumentará a velocidade de fatiamento ao remover detalhes da malha que não poderia processar de qualquer jeito."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "O tamanho mínimo de um segmento de linha de percurso após o fatiamento. Se o valor aumenta, os movimentos de percurso terão cantos menos suaves. Isto pode permitir que a impressora mantenha a velocidade necessária para processar o G-Code, mas pode fazer com que evitar topar no modelo fique menos preciso."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "A mínima inclinação da área para que o suporte em escada tenha efeito. Valores baixos devem tornar o suporte mais fácil de remover em inclinações rasas, mas muitos baixos resultarão em resultados bastante contra-intuitivos em outras partes do modelo."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "A espessura mínima do casco da torre de purga. Você pode aumentar este valor para tornar a torre de purga mais forte."

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "O tempo mínimo gasto em uma camada que contenha extrusões pendentes. Isto força a impressora a desacelerar para pelo menos gastar o tempo ajustado aqui em uma camada. E por sua vez isso permite que o material impresso esfrie apropriadamente antes de imprimir a próxima camada. Camadas ainda podem levar menos tempo que o tempo mínimo de camada se Levantar Cabeça estiver desabilitado e se a Velocidade Mínima fosse violada dessa forma."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "O tempo mínimo empregado em uma camada. Isto força a impressora a desacelerar para no mínimo usar o tempo ajustado aqui em uma camada. Isto permite que o material impresso resfrie apropriadamente antes de passar para a próxima camada. As camadas podem ainda assim levar menos tempo que o tempo mínimo de camada se Levantar Cabeça estiver desabilitado e se a Velocidade Mínima fosse violada com a lentidão."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "O volume mínimo para cada camada da torre de purga de forma a purgar material suficiente."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "O máximo que o diâmetro de um galho que tem que se conectar ao modelo pode aumentar ao mesclar-se com galhos que podem alcançar a plataforma de impressão. Aumentar este valor reduz tempo de impressão, mas aumenta a área de suporte que se apoia no modelo"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "Nome do seu modelo de impressora 3D."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "O identificador do bico para o carro extrusor, tais como \"AA 0.4\" ou \"BB 0.8\"."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "O bico evita partes já impressas quando está em uma percurso. Esta opção está disponível somente quando combing (penteamento) está habilitado."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "O bico evita suportes já impressos durante o percurso. Esta opção só está disponível quando combing estiver habilitado."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "O número de camadas inferiores. Quando calculado da espessura inferior, este valor é arredondado para um inteiro."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "O número de camadas do contorno mais inferior. Geralmente somente uma camada de contorno mais inferior é suficiente para gerar superfícies inferiores de maior qualidade."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "O número de contornos a serem impressos em volta do padrão linear na camada base do raft."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "O número de contornos a imprimir em torno do padrão linear nas camadas intermediárias do raft."

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "O número de contornos a imprimir em torno do padrão linear nas camadas superiores do raft."

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "O número de contornos a imprimir em torno do padrão linear do raft."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "O número de camadas de preenchimento que suportam arestas de contorno."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "O número de camadas inferiores iniciais da plataforma de impressão pra cima. Quanto calculado a partir da espessura inferior, esse valor é arrendado para um número inteiro."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "O número de camadas entre a base e a superfície do raft. Isso corresponde à espessura principal do raft. Aumentar este valor cria um raft mais espesso e resistente."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "O número de linhas usada para o brim. Mais linhas de brim melhoram a aderência à mesa, mas também reduzem a área efetiva de impressão."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "O número de filetes usado para o brim de suporte. Mais filetes melhoram a aderência na mesa de impressão, ao custo de material extra."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "O número da ventoinhas que refrigeram o volume de construção. Se isto for colocado em 0, significa que não há ventoinha do volume de construção."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "O número de camadas superiores acima da segunda camada do raft. Estas são camadas completamente preenchidas em que o modelo se assenta. 2 camadas resultam em uma superfície superior mais lisa que apenas uma."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "O número de camadas superiores. Quando calculado da espessura superior, este valor é arredondado para um inteiro."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "O número de camadas da superfície superior. Geralmente somente uma camada é suficiente para gerar superfícies de alta qualidade."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "O número de paredes com as quais contornar o preenchimento de suporte. Adicionar uma parede pode tornar a impressão de suporte mais confiável e apoiar seções pendentes melhor, mas aumenta tempo de impressão e material usado."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "O número de paredes com as quais contornar o preenchimento de suporte. Adicionar uma parede pode tornar a impressão de suporte mais confiável e apoiar seções pendentes melhor, mas aumenta tempo de impressão e material usado."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "O número de paredes com as quais contornar o preenchimento de suporte. Adicionar uma parede pode tornar a impressão de suporte mais confiável e apoiar seções pendentes melhor, mas aumenta tempo de impressão e material usado."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "O número de paredes com as quais contornar o preenchimento de suporte. Adicionar uma parede pode tornar a impressão de suporte mais confiável e apoiar seções pendentes melhor, mas aumenta tempo de impressão e material usado."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "O número de paredes, contadas a partir do centro, sobre as quais a variação será distribuída. Valores menores significam que as paredes mais externas não mudam de comprimento."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Número de filetes da parede. Quando calculado pela espessura de parede, este valor é arredondado para um inteiro."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Diâmetro exterior do bico (a ponta do hotend)."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "O padrão das camadas mais inferiores."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "O padrão do material de preenchimento da impressão. Os preenchimentos de linha e ziguezague trocam de direção em camadas alternadas, reduzindo custo de material. Os padrões de grade, triângulo, tri-hexágono, cúbico, octeto, quarto cúbico, cruzado e concêntrico são completamente impressos a cada camada. Os preenchimentos giroide, cúbico, quarto cúbico e octeto mudam a cada camada para prover uma distribuição de força mais uniforme em cada direção. O preenchimento de relâmpago tenta minimizar material somente suportando o teto do objeto."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "O padrão (estampa) das estruturas de suporte da impressão. As diferentes opções disponíveis resultam em suportes mais resistentes ou mais fáceis de remover."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "O padrão das camadas superiores."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Padrão ou Estampa das camadas superiores e inferiores."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "O padrão na base da impressão na primeira camada."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "O padrão a usar quando se passa a ferro as superfícies superiores."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "O padrão com o qual as bases do suporte são impressas."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Padrão (estampa) com a qual a interface do suporte para o modelo é impressa."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "O padrão com o qual o teto do suporte é impresso."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "A posição perto da qual se inicia a impressão de cada parte em uma camada."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "O ângulo preferido para os galhos, quando eles não têm que evitar o modelo. Use um ângulo menor para torná-los mais verticais e estáveis. Use um ângulo maior para que os galhos se mesclem mais rapidamente."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "O posicionamento preferido das estruturas de suporte.Se as estruturas não puderem ser colocadas na localização escolhida, serão colocadas em outro lugar, mesmo que seja no modelo."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "A mudança instantânea máxima de velocidade em uma direção para a camada inicial."

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "A proporção da altura de camada selecionada em que a emenda scarf começará. Um número menor resultará em maior altura de emenda. Deve ser menor que 100 para se tornar efetivo."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "A forma da mesa de impressão sem levar área não-imprimíveis em consideração."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "O tamanho dos bolso em cruzamentos quádruplos no padrão cruzado 3D em alturas onde o padrão esteja se tocando."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "O menor volume que um caminho de extrusão deve apresentar antes que lhe seja permitido desengrenar. Para caminhos de extrusão menores, menos pressão é criada dentro do hotend e o volume de desengrenagem é redimensionado linearmente. Este valor deve sempre ser maior que o Volume de Desengrenagem."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Velocidade (°C/s) pela qual o bico resfria tirada pela média na janela de temperaturas normais de impressão e temperatura de espera."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Velocidade (°C/s) pela qual o bico aquece tirada pela média na janela de temperaturas normais de impressão e temperatura de espera."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "A velocidade em que todas as paredes interiores são impressas. Imprimir a parede interior mais rapidamente que a parede externa reduzirá o tempo de impressão. Funciona bem ajustar este valor a meio caminho entre a velocidade da parede mais externa e a velocidade de preenchimento."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr "A velocidade com que as camadas do contorno da superfície inferior são impressas."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "A velocidade com a qual regiões de contorno de ponte são impressas."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Velocidade em que se imprime o preenchimento."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Velocidade em que se realiza a impressão."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "A velocidade em que a camada de base do raft é impressa. Deve ser impressa lentamente, já que o volume do material saindo do bico será bem alto."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "A velocidade com que as paredes internas da superfície inferior são impressas."

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "A velocidade com que a parede mais externa da superfície inferior é impressa."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "A velocidade com a qual as paredes de ponte são impressas."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "A velocidade em que as ventoinhas giram no início da impressão. Em camadas subsequentes a velocidade da ventoinha é gradualmente aumentada até a camada correspondente ao ajuste 'Velocidade Regular da Ventoinha na Altura'."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Velocidade em que as ventoinhas giram antes de dispararem o limite. Quando uma camada imprime mais rapidamente que o limite de tempo, a velocidade de ventoinha aumenta gradualmente até a velocidade máxima."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Velocidade em que as ventoinhas giram no tempo mínimo de camada. A velocidade da ventoinha gradualmente aumenta da regular até a máxima quando o limite é atingido."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "A velocidade com a qual o filamento é avançado durante o movimento de retração."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "A velocidade com que o filamento é purgado durante um movimento de retração de limpeza."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "A velocidade em que o filamento é empurrado para a frente depois de uma retração de troca de bico."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "A velocidade com a qual o filamento é recolhido e avançado durante o movimento de retração."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "A velocidade com que o filamento é retraído e purgado durante um movimento de retração de limpeza."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "A velocidade em que o filamento é retraído durante uma retração de troca de bico."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "A velocidade com a qual o filamento é recolhido durante o movimento de retração."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "A velocidade com que o filamento é retraído durante um movimento de retração de limpeza."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "A velocidade em que o filamento é retraído. Uma velocidade de retração mais alta funciona melhor, mas uma velocidade muito alta pode levar a desgaste do filamento."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "A velocidade em que a base do suporte é impressa. Imprimi-la em velocidade mais baixa pode melhorar a aderência do suporte no topo da superfície."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "A velocidade em que o preenchimento do suporte é impresso. Imprimir o preenchimento em velocidades menores melhora a estabilidade."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "A velocidade em que a camada intermediária do raft é impressa. Esta deve ser impressa devagar, já que o volume de material saindo do bico é bem alto."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "A velocidade em que as paredes mais externas são impressas. Imprimir a parede mais externa a uma velocidade menor melhora a qualidade final do contorno. No entanto, ter uma diferença muito grande entre a velocidade da parede interna e a velocidade da parede externa afetará a qualidade de forma negativa."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "A velocidade em que a torre de purga é impressa. Imprimir a torre de purga mais lentamente pode torná-la mais estável quando a aderência entre os diferentes filamentos é subótima."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "A velocidade em que as ventoinhas giram."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "A velocidade em que o raft é impresso."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "A velocidade com que os tetos e bases do suporte são impressos. Imprimi-los em velocidades mais baixas pode melhorar a qualidade de seções pendentes."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "A velocidade em que os tetos dos suportes são impressos. Imprimi-los em velocidade mais baixas pode melhorar a qualidade de seções pendentes."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Velocidade em que o Brim (Bainha) e Skirt (Saia) são impressos. Normalmente isto é feito na velocidade de camada inicial, mas você pode querer imprimi-los em velocidade diferente."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "A velocidade em que a estrutura de suporte é impressa. Imprimir o suporte a velocidades mais altas pode reduzir bastante o tempo de impressão. A qualidade de superfície das estruturas de suporte não é importante já que são removidas após a impressão."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "A velocidade em que as camadas superiores do raft são impressas. Elas devem ser impressas um pouco mais devagar, de modo que o bico possa lentamente alisar as linhas de superfície adjacentes."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "A velocidade com que as paredes internas da superfície superior são impressas."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "A velocidade com que a parede mais externa da superfície superior é impressa."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "A velocidade em que o movimento Z vertical é feito para os saltos Z. Tipicamente mais baixa que a velocidade de impressão já que mover a mesa de impressão ou eixos da máquina é mais difícil."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Velocidade em que se imprimem as paredes."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "A velocidade com a qual o ajuste de passar ferro é aplicado sobre a superfície superior."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "A velocidade com a qual retrair o filamento para que se destaque completamente."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "A velocidade com que as camadas superiores são impressas."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Velocidade em que as camadas superiores e inferiores são impressas."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Velocidade em que ocorrem os movimentos de percurso."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "A velocidade pela qual se mover durante a desengrenagem, relativa à velocidade do caminho de extrusão. Um valor ligeiramente menor que 100% é sugerido, já que durante a desengrenagem a pressão dentro do hotend cai."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "A velocidade para a camada inicial. Um valor menor é sugerido para melhorar aderência à mesa de impressão. Não afeta as estruturas de aderência à mesa de impressão como o brim e o raft."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "A velocidade de impressão para a camada inicial. Um valor menor é aconselhado para aprimorar a aderência à mesa de impressão."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "A velocidade dos percursos da camada inicial. Um valor mais baixo que o normal é aconselhado para prevenir o puxão de partes impressas da mesa de impressão. O valor deste ajuste pode ser automaticamente calculado do raio entre a Velocidade de Percurso e a Velocidade de Impressão."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "A temperatura em que o filamento é destacado completamente."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "A temperatura do ambiente em que imprimir. Se este valor for 0, a temperatura de volume de impressão não será ajustada."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "A temperatura do bico quando outro bico está sendo usado para a impressão."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "A temperatura para a qual se deve começar a esfriar pouco antes do fim da impressão."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "A temperatura usada para imprimir a primeira camada."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "A temperatura usada para impressão."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "A temperatura usada para a plataforma aquecida de impressão na primeira camada. Se for 0, a plataforma de impressão não será aquecida durante a primeira camada."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "A temperatura usada para a plataforma aquecida de impressão. Se for 0, a plataforma de impressão não será aquecida."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "A temperatura usada para purgar material, deve ser grosso modo a temperatura de impressão mais alta possível."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "A espessura das camadas inferiores da impressão. Este valor dividido pela altura de camada define o número de camadas inferiores."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "A espessura do preenchimento extra que suporta arestas de contorno."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "A espessura da interface do suporte onde ele toca o modelo na base ou no topo."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "A espessura das bases de suporte. Isto controla o número de camadas densas que são impressas no topo dos pontos do modelo em que o suporte se assenta."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "A espessura do topo do suporte. Isto controla a quantidade de camadas densas no topo do suporte em que o modelo se assenta."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "A espessura das camadas superiores da impressão. Este valor dividido pela altura de camada define o número de camadas superiores."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "A espessura das camadas superiores e inferiores da impressão. Este valor dividido pela altura de camada define o número de camadas superiores e inferiores."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "A espessura das paredes na direção horizontal. Este valor dividido pela largura de extrusão da parede define o número de filetes da parede."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "A espessura por camada de material de preenchimento. Este valor deve sempre ser um múltiplo da altura de camada e se não for, é arredondado."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "A espessura por camada do material de preenchimento de suporte. Este valor deve sempre ser um múltiplo da altura de camada e é arredondado."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "O tipo de G-Code a ser gerado."

msgctxt "material_type description"
msgid "The type of material used."
msgstr "O tipo de material usado."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Volume que seria escorrido. Este valor deve em geral estar perto do diâmetro do bico ao cubo."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "A largura (direção X) da área imprimível."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "A largura do brim a ser impresso sob o suporte. Um brim mais largo melhora a aderência à mesa de impressão, ao custo de material extra."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "A largura das faixas cruzadas de estrutura."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "A largura da base ou brim da torre de purga. Uma base maior melhora a aderência à mesa mas também reduz a área efetiva de impressão."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "A largura da torre de purga."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "A largura dentro da qual flutuar. É sugerido deixar este valor abaixo da largura da parede externa, já que as paredes internas não são alteradas pelo algoritmo."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "A janela em que a contagem de retrações máxima é válida. Este valor deve ser aproximadamente o mesmo que a distância de retração, de modo que efetivamente o número de vez que a retração passa pelo mesmo segmento de material é limitada."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "A coordenada X da posição da torre de purga."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "A coordenada Y da posição da torre de purga."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Há malhas de suporte presentes na cena. Este ajuste é controlado pelo Cura."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Este ajuste controla a distância que o extrusor deve parar de extrudar antes que a parede de ponte comece. Desengrenar antes da ponte iniciar pode reduzir a pressão no bico e produzir em uma ponte mais horizontal."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "Esta é a aceleração com a qual se alcança a velocidade máxima ao imprimir uma parede externa."

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "Esta é a deceleração com que terminar a impressão de uma parede externa."

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "Este é o máximo comprimento de um caminho de extrusão ao dividir um caminho maior para aplicar a aceleração ou deceleração de parede externa. Uma distância maior criará um G-code mais preciso mas também mais extenso."

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "Esta é a proporção da velocidade máxima com que terminar a impressão de uma parede externa."

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "Esta é a proporção da velocidade máxima com que começar a impressão de uma parede externa."

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Este ajuste controla quantos cantos internos no contorno da base do raft são arredondados. Cantos agudos são arredondados para um semicírculo com raio igual ao valor dado aqui. Este ajuste também remove furos no contorno do raft que sejam menores que tal círculo."

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Este ajuste controle quantos cantos internos no contorno do meio do raft são arredondados. Cantos agudos são arredondados para um semicírculo com raio igual ao valor dado aqui. Este ajuste também remove furos no contorno do raft que sejam menores que tal círculo."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Este ajuste controla quanto os cantos internos do contorno do raft são arredondados. Esses cantos internos são convertidos em semicírculos com raio igual ao valor dado aqui. Este ajuste também remove furos no contorno do raft que forem menores que o círculo equivalente."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Este ajuste controle quantos cantos internos no contorno do topo do raft são arredondados. Cantos agudos são arredondados para um semicírculo com raio igual ao valor dado aqui. Este ajuste também remove furos no contorno do raft que sejam menores que tal círculo."

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "Este ajuste controle se o gcode de início é forçado para sempre ser o primeiro g-code. Sem esta opção outro g-code, como um T0, pode ser inserido antes do g-code de início."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Este ajuste limita o número de retrações ocorrendo dentro da janela de distância de extrusão mínima. Retrações subsequentes dentro desta janela serão ignoradas. Isto previne repetidas retrações no mesmo pedaço de filamento, já que isso pode acabar ovalando e desgastando o filamento."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Isto criará uma parede em volta do modelo que aprisiona ar quente da mesa e protege contra fluxo de ar do exterior. Especialmente útil para materiais que sofrem bastante warp e impressoras 3D que não são cobertas."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Diâmetro da Ponta"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Para compensar pelo encolhimento do material enquanto ele esfria, o modelo será ampliado por este fator na direção XY (horizontalmente)."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Para compensar pelo encolhimento do material enquanto esfria, o modelo será ampliado por este fator na direção Z (verticalmente)."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Para compensar o encolhimento do material enquanto esfria, o modelo será redimensionado por este fator."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Camadas Superiores"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Distância de Expansão do Contorno Superior"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Largura de Remoção do Contorno Superior"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Aceleração da Parede Interna da Superfície Superior"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Jerk da Parede Interna da Superfície Superior"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Velocidade da Parede Interna da Superfície Superior"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Fluxo das Paredes Internas da Superfície Superior"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Aceleração da Parede Externa da Superfície Superior"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Fluxo da Parede Externa da Superfície Superior"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Jerk da Parede Externa da Superfície Superior"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Velocidade da Parede Externa da Superfície Superior"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Aceleração da Superfície Superior"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Extrusor da Superfície Superior"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Fluxo do Contorno da Superfície Superior"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Jerk da Superfície Superior"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Camadas da Superfície Superior"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Direções dos Filetes da Superfície Superior"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Largura de extrusão da Superfície Superior"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Padrão da Superfície Superior"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Velocidade da Superfície Superior"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Espessura Superior"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Superfícies superiores e/ou inferiores de seu objeto com um ângulo maior que este ajuste não terão seu contorno expandido. Isto permite evitar a expansão de áreas estreitas de contorno que são criadas quando a superfície do modelo tem uma inclinação quase vertical. Um ângulo de 0° é horizontal e não causará expansão no contorno, enquanto que um ângulo de 90° é vertical e causará expansão em todo o contorno."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Superior/Inferior"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Superior/Inferior"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Aceleração Superior/Inferior"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Extrusor Superior/Inferior"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Fluxo de Topo/Base"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Jerk Superior/Inferior"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Direções de Linha Superior/Inferior"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Largura de Extrusão Superior/Inferior"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Padrão Superior/Inferior"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Velocidade Superior/Inferior"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Espessura Superior/Inferior"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Tocando a Mesa"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Diâmetro da Torre"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Ângulo do Teto da Torre"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Matriz de transformação a ser aplicada ao modelo após o carregamento do arquivo."

msgctxt "travel label"
msgid "Travel"
msgstr "Percurso"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Aceleração de Percurso"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Distância de Desvio de Percurso"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Jerk de Percurso"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Velocidade de Percurso"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Tratar o modelo como apenas superfície, um volume ou volumes com superfícies soltas. O modo de impressão normal somente imprime volumes fechados. O modo \"superfície\" imprime uma parede única traçando a superfície da malha sem nenhun preenchimento e sem paredes superiores ou inferiores. O modo \"ambos\" imprime volumes fechados como o modo normal e volumes abertos como superfícies."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Árvore"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Tri-Hexágono"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Triângulos"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Triângulo"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Triângulos"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Triângulos"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Triângulos"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Diâmetro do Tronco"

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "Tentar prevenir costuras nas paredes que tenham seção pendente com ângulo maior que esse. Quando o valor for 90, nenhuma parede será tratada como seção pendente."

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "Fator de sintonização de avanço de pressão, que tem a função de sincronizar a extrusão com o movimento"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "Não alterado"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Volumes de Sobreposição de Uniões"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Paredes não-suportadas mais curtas que esta quantia serão impressas usando ajustes normais de paredes. Paredes mais longas serão impressas com os ajustes de parede de ponte."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Usar Camadas Adaptativas"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Usar Torres"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Usar taxa de aceleração separada para movimentos de percurso. Se desabilitado, os movimentos de percurso usarão o valor de aceleração da linha impressa em seu destino."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Usar taxa de jerk separada para movimentos de percurso. Se desabilitado, os movimentos de percurso usarão o valor de jerk da linha impressa em seu destino."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Usar extrusão relativa ao invés de extrusão absoluta. Passos de extrusão relativos no G-Code tornam o pós-processamento mais fácil. No entanto, isso não é suportado por todas as impressoras e pode produzir pequenos desvios na quantidade de material depositado comparado a passos de extrusão absolutos. Independente deste ajuste, o modo de extrusão sempre será ajustado para absoluto antes que qualquer script G-Code seja processado."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Usa torres especializadas como suporte de pequenas seções pendentes. Essas torres têm um diâmetro mais largo que a região que elas suportam. Perto da seção pendente, o diâmetro das torres aumenta, formando um 'teto'."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Utilize esta malha para modificar o preenchimento de outras malhas com as quais ela se sobrepõe. Substitui regiões de preenchimento de outras malhas com regiões desta malha. É sugerido que se imprima com somente uma parede e sem paredes superiores e inferiores para esta malha."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Use esta malha para especificar áreas obrigatoriamente suportadas. Isto será usado para gerar estruturas de suporte."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Use esta malha para especificar onde nenhuma parte do modelo deverá ser detectada como seção Pendente e por conseguinte não elegível a receber suporte. Com esta malha sobreposta a um modelo, você poderá marcar onde ele não deverá receber suporte."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Especificado pelo Usuário"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Compensação de Fator de Encolhimento Vertical"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Tolerância vertical das camadas fatiadas. Os contornos de uma camada são normalmente gerados se tomando seções cruzadas pelo meio de cada espessura de camada (Meio). Alternativamente, cada camada pode ter as áreas que caem fora do volume por toda a espessura da camada (Exclusivo) ou a camada pode ter as áreas que caem dentro de qualquer lugar dentro da camada (Inclusivo). Inclusivo retém mais detalhes, Exclusivo proporciona o melhor encaixe e Meio permanece mais próximo da superfície original."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Aguardar o Aquecimento da Mesa"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Aguardar Aquecimento do Bico"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Aceleração da Parede"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Contagem de Distribuição de Parede"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Extrusor das Paredes"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Fluxo de Parede"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Jerk da Parede"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Número de Filetes da Parede"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Largura de Extrusão da Parede"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Ordem de Parede"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Velocidade da Parede"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Espessura de Parede"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Comprimento de Transição de Parede"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Distância de Filtro da Transição de Parede"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Margem de Filtro de Transição de Parede"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Ângulo-Limite de Transição de Parede"

msgctxt "shell label"
msgid "Walls"
msgstr "Paredes"

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "Paredes Somente"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "Paredes e Linhas"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "Paredes com seção pendente maior que este ângulo serão impressas usando ajustes de seções pendentes de perede. Quando este valor for 90, nenhuma parede será tratada como seção pendente. Seções pendentes apoiadas por suporte não serão tratadas como pendentes também. Além disso, qualquer filete que for menos da metade pendente também não será tratado como pendente."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Quando habilitado os percursos do extrusor são corrigidos para impressora com planejadores de movimento suavizado. Pequenos movimentos que se desviariam da direção geral do percurso do extrusor são suavizados para melhorar os movimentos fluidos."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Quando habilitado, a ordem em que os filetes de preenchimento são impressos é otimizada para reduzir a distância percorrida. A redução em tempo de percurso conseguida depende bastante do modelo sendo fatiado, do padrão de preenchimento, da densidade, etc. Note que, para alguns modelos que têm áreas bem pequenas de preenchimento, o tempo de fatiamento pode ser aumentado bastante."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Quando habilitado, a velocidade da ventoinha de resfriamento é alterada para as regiões de contorno imediatamente acima do suporte."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Quando habilitado, as coordenadas da costura Z são relativas ao centro de cada parte. Quando desabilitado, as coordenadas definem uma posição absoluta na plataforma de impressão."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Quando maior que zero, os movimentos de percurso de combing que forem maiores que essa distância usarão retração. Se deixado em zero, não haverá máximo e os movimentos de combing não usarão retração."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Quando maior que zero, a Expansão Horizontal de Furo é gradualmente aplicada em pequenos furos (eles são mais expandidos). Quanto é deixada em zero, a Expansão Horizontal de Furo será aplicada a todos os furos. Furos maiores que o Diâmetro Máximo de Expansão Horizontal de Furo não serão expandidos."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Quando maior que zero, a Expansão Original do Furo designa a distância de compensação aplicada a todos os furos em cada camada. Valores positivos aumentam os tamanhos dos furos, valores negativos reduzem os tamanhos dos furos. Quando este ajuste é habilitado, ele pode ser ainda aprimorado com o ajuste 'Diâmetro Máximo da Expansão Horizontal de Furo'."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Ao imprimir regiões de contorno de ponte, a quantidade de material extrudado é multiplicada por este valor."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Ao se imprimir paredes de ponte, a quantidade de material extrudado é multiplicada por este valor."

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "Quando a primeira camada da interface de raft for impressa, traduza pelo valor deste deslocamento para personalizar a aderência entre a base e a interface. Um deslocamento negative deve melhorar a aderência."

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "Quando a primeira camada da superfície de raft for impressora, traduza pelo valor deste deslocamento para personalizar a aderência entre a base e a superfície. Um deslocamento negative deve melhorar a aderência."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Ao imprimir a segunda camada de contorno de ponte, a quantidade de material é multiplicada por este valor."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Ao imprimir a terceira de contorno da ponte, a quantidade de material é multiplicada por este valor."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Quando a velocidade mínima acaba sendo usada por causa do tempo mínimo de camada, levanta a cabeça para longe da impressão e espera tempo extra até que o tempo mínimo de camada seja alcançado."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Quando o modelo tem pequenas lacunas verticais de apenas umas poucas camadas, normalmente há contorno em volta dessas camadas no espaço estreito. Habilite este ajuste para não gerar o contorno se a lacuna vertical for bem pequena. Isso melhora o tempo de impressão e fatiamento, mas tecnicamente deixa preenchimento exposto ao ar."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Quanto criar transições entre números de paredes pares e ímpares. A forma de cunha em ângulo maior que este ajuste não terá transições e nenhuma parede será impressa no centro para preencher o espaço remanescente. Reduzir este ajuste faz reduzir o número e comprimento das paredes centrais, mas pode deixar vãos ou sobre-extrudar."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Ao transicionar entre diferentes números de paredes à medida que a peça fica mais fina, uma certa quantidade de espaço é alocada para partir ou juntar os filetes de parede."

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "Ao tentar aplicar o tempo mínimo de camada específico para camadas pendentes, o ajuste valerá somente se no mínimo um movimento de extrusão pendente consecutivo demorar mais que esse valor."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Quando limpando, a plataforma de impressão é abaixada para criar uma folga entre o bico e a impressão. Isso previne que o bico bata na impressão durante movimentos de percurso, reduzindo a chance de descolar o objeto da plataforma."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Sempre que uma retração é feita, sobe-se um pouco em Z para criar um espaço entre o bico e a impressão. Isso evita que o bico fique batendo nas impressões durante o percurso, reduzindo a chance de chutar a peça para fora da mesa."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Decide se a distância XY substitui a distância Z de suporte ou vice-versa. Quando XY substitui Z a distância XY pode afastar o suporte do modelo, influenciando a distância Z real até a seção pendente. Podemos desabilitar isso não aplicando a distância XY em volta das seções pendentes."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Decide se as coordenadas X/Y da posição zero da impressão estão no centro da área imprimível (senão, estarão no canto inferior esquerdo)."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "Decide se o endstop do eixo X está na direção positiva (coordenada X alta) ou negativa (coordenada X baixa)."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Decide se o endstop do eixo Y está na direção positiva (coordenada Y alta) ou negativa (coordenada Y baixa)."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Decide se o endstop do eixo Z está na direção positiva (coordenada Z alta) ou negativa (coordenada Z baixa)."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Decide se os extrusores usam um único aquecedor combinado ou cada um tem o seu respectivo aquecedor."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Decide se os extrusores compartilham um único bico ao invés de cada extrusor ter seu próprio. Quando colocado em verdadeiro, é esperado que o script g-code de início da impressora configure todos os extrusores em um estado inicial de retração que seja conhecido e mutuamente compatível (ou zero ou filamento não retraído); neste caso, o status de retração inicial é descrito, por extrusor, pelo parâmetro 'machine_extruders_shared_nozzle_initial_retraction'."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Decide se a plataforma de impressão pode ser aquecida."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Decide se a máquina consegue estabilizar a temperatura do volume de construção."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Decide se o objeto deve ser centralizado no meio da plataforma de impressão, ao invés de usar o sistema de coordenadas em que o objeto foi salvo."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Decide se a temperatura deve ser controlada pelo Cura. Desligue para controlar a temperatura do bico fora do Cura."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Decide se haverá a inclusão de comandos de temperatura da mesa de impressão no início do G-Code. Quando o G-Code Inicial já contiver comandos de temperatura da mesa, a interface do Cura automaticamente desabilitará este ajuste."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Decide se haverá a inclusão de comandos de temperatura do bico no início do G-Code. Quando o G-Code Inicial já contiver comandos de temperatura do bico, a interface do Cura automaticamente desabilitará este ajuste."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Decide se haverá inclusão de G-Code de limpeza de bico entre camadas (no máximo 1 por camada). Habilitar este ajuste pode influenciar o comportamento de retração na mudança de camada. Por favor use ajustes de Retração de Limpeza para controlar retração nas camadas onde o script de limpeza estará atuando."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Decide se haverá inserção do comando para aguardar que a temperatura-alvo da mesa de impressão estabilize no início."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Decide se é preciso descarregar o filamento com uma massa de purga antes de imprimir. Ligar este ajuste assegurará que o extrusor tenha material pronto no bico antes de imprimir. Imprimir um Brim ou Skirt pode funcionar como purga também, em cujo caso desligar esse ajuste faz ganhar algum tempo."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Decide se os modelos devem ser impressos todos de uma vez só, uma camada por vez, ou se se deve esperar a cada modelo terminar antes de prosseguir para o próximo. O modo um de cada vez só é possível se a) somente um extrusor estiver habilitado e b) todos os modelos estiverem separados de modo que a cabeça de impressão pode se mover entre todos e todos os modelos estiverem em altura mais baixa que a distância entre o bico e os eixos X e Y."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Decide se deseja exibir as variantes desta máquina, que são descrita em arquivos .json separados."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Decide se serão usados comandos de retração de firmware (G10/G11) ao invés da propriedade E dos comandos G1 para retrair o material."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Decide se haverá a inserção do comando para aguardar que a temperatura-alvo do bico estabilize no início."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Largura de um filete de preenchimento."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Largura de um filete usado no teto ou base do suporte."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "Largura de um filete singular das áreas na base da impressão."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Largura de extrusão de um filete das áreas no topo da peça."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Largura de uma única linha de filete extrudado. Geralmente, a largura da linha corresponde ao diâmetro do bico. No entanto, reduzir ligeiramente este valor pode produzir impressões melhores."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Largura de um filete usado na torre de purga."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Largura de um filete do brim (bainha) ou skirt (saia)."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Largura de um filete usado na base do suporte."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Largura de um filete usado no teto do suporte."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Largura de um filete usado nas estruturas de suporte."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Largura de extrusão dos filetes das paredes do topo e base dos modelos."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "Largura de extrusão das paredes internas (todas menos a mais externa)."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Largura de um filete que faz parte de uma parede."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Largura das linhas na camada de base do raft. Devem ser grossas para auxiliar na aderência à mesa."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Largura das linhas na camada intermediária do raft. Fazer a segunda camada extrudar mais faz as linhas grudarem melhor na mesa."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Largura das linhas na superfície superior do raft. Estas podem ser linhas finas de modo que o topo do raft fique liso."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "Largura de Extrusão somente da parede mais externa do modelo. Diminuindo este valor, níveis de detalhes mais altos podem ser impressos."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Largura da parede que substituirá detalhes finos (de acordo com o Tamanho Mínimo de Detalhe) do modelo. Se a Largura Mínima de Filete de Parede for mais fina que a espessura do detalhe, a parede se tornará tão espessa quanto o próprio detalhe."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Posição X da Varredura de Limpeza"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Velocidade do Salto de Limpeza"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "Limpar Bico Inativo na Torre de Purga"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Distância de Movimentação da Limpeza"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Limpar o Bico Entre Camadas"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Pausa de Limpeza"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Contagem de Repetições de Limpeza"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Distância de Retração da Limpeza"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Habilitar Retração de Limpeza"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Quantidade Extra de Purga da Retração de Limpeza"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Velocidade de Purga da Retração de Limpeza"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Velocidade da Retração da Retração de Limpeza"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Velocidade da Retração de Limpeza"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Salto Z da Limpeza"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Altura do Salto Z da Limpeza"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Dentro do Preenchimento"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Escreve a ferramenta ativa depois de enviar comandos de temperatura para a ferramenta inativa. Requerido para impressão de Extrusor Duplo com Smoothie ou outros firmwares com comandos modais de ferramenta."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "Endstop X na Direção Positiva"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Localização X onde o script de limpeza iniciará."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y substitui Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Endstop Y na Direção Positiva"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Endstop Z na Direção Positiva"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Salto Z Após Troca de Extrusor"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Salto Z Após Troca de Altura do Extrusor"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Altura do Salto Z"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Salto Z Somente Sobre Partes Impressas"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Velocidade do Salto Z"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Salto Z Ao Retrair"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Alinhamento da Costura em Z"

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "Costura Z No Vértice"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Posição da Costura Z"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Costura Z Relativa"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Coordenada X da Costura Z"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Coordenada Y da Costura Z"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z substitui X/Y"

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Ziguezague"

msgctxt "travel description"
msgid "travel"
msgstr "percurso"

#~ msgctxt "brim_inside_margin description"
#~ msgid "A part fully enclosed inside another part can generate an outer brim that touches the inside of the other part. This removes all brim within this distance from internal holes."
#~ msgstr "Uma peça completamente contida em outra peça pode gerar um brim externo que toca o interior da outra parte. Este ajuste remove todo o brim dentro desta distância dos buracos internos."

#~ msgctxt "user_defined_print_order_enabled description"
#~ msgid "Allows to order the object list to set the print sequence manually. First object from the list will be printed first."
#~ msgstr "Permite ordenar a lista de objetos para definir a sequência de impressão manualmente. O primeiro objeto da lista será impresso primeiro."

#~ msgctxt "brim_inside_margin label"
#~ msgid "Brim Inside Avoid Margin"
#~ msgstr "Brim Dentro da Margem a Evitar"

#~ msgctxt "brim_outside_only label"
#~ msgid "Brim Only on Outside"
#~ msgstr "Brim Somente Para Fora"

#~ msgctxt "layer_0_z_overlap description"
#~ msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount."
#~ msgstr "Faz a primeira e segunda camadas do modelo se sobreporem na direção Z para compensar pelo filamento perdido no vão aéreo. Todos os modelos acima da primeira camada de modelo serão deslocados para baixo por essa distância."

#~ msgctxt "machine_nozzle_head_distance label"
#~ msgid "Nozzle Length"
#~ msgstr "Comprimento do Bico"

#~ msgctxt "variant_name"
#~ msgid "Nozzle Size"
#~ msgstr "Tamanho do bico"

#~ msgctxt "brim_outside_only description"
#~ msgid "Only print the brim on the outside of the model. This reduces the amount of brim you need to remove afterwards, while it doesn't reduce the bed adhesion that much."
#~ msgstr "Imprimir o Brim somente no lado de fora do modelo. Isto reduz a quantidade de brim a ser removida no final, e não reduz tanto a aderência à mesa."

#~ msgctxt "wall_0_acceleration label"
#~ msgid "Outer Wall Acceleration"
#~ msgstr "Aceleração da Parede Externa"

#~ msgctxt "wall_0_deceleration label"
#~ msgid "Outer Wall Deceleration"
#~ msgstr "Deceleração da Parede Externa"

#~ msgctxt "wall_overhang_speed_factor label"
#~ msgid "Overhanging Wall Speed"
#~ msgstr "Velocidade de Parede Pendente"

#~ msgctxt "wall_overhang_speed_factor description"
#~ msgid "Overhanging walls will be printed at this percentage of their normal print speed."
#~ msgstr "Paredes pendentes serão impressas com esta porcentagem de sua velocidade de impressão normal."

#~ msgctxt "support_interface_skip_height label"
#~ msgid "Support Interface Resolution"
#~ msgstr "Resolução da Interface de Suporte"

#~ msgctxt "machine_nozzle_head_distance description"
#~ msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
#~ msgstr "Diferença de altura entre a ponta do bico e a parte mais baixa da cabeça de impressão."

#~ msgctxt "machine_head_with_fans_polygon description"
#~ msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
#~ msgstr "A forma da cabeça de impressão. Essas são coordenadas relativas à posição da cabeça de impressão, que é geralmente a posição do seu primeiro extrusor. As dimensões à esquerda e na frente da cabeça devem ser coordenadas negativas."

#~ msgctxt "wall_overhang_angle description"
#~ msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
#~ msgstr "Paredes que pendem por mais do que esse ângulo serão impressas usando ajustes de paredes pendentes. Quando este valor for 90, nenhuma parede será tratada como pendente. Seções pendentes que têm suportes também não serão tratadas como pendentes."

#~ msgctxt "support_interface_skip_height description"
#~ msgid "When checking where there's model above and below the support, take steps of the given height. Lower values will slice slower, while higher values may cause normal support to be printed in some places where there should have been support interface."
#~ msgstr "Quando verificar se há partes do modelo abaixo e acima do suporte, usar passos de dada altura. Valores baixos fatiarão mais lentamente, enquanto que valores altos farão com que suporte convencional seja impresso em lugares em que deveria haver interface de suporte."
