msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-13 09:02+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: tr_TR\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>Asal kule nasıl oluşturulur:<ul><li><b>Normal:</b> İkincil malzemelerin astarlandığı bir kova oluşturur.</li><li><b>Aralıklı:</b> Olabildiğince seyrek bir asal kule oluşturur. Bu, zamandan ve filamentten tasarruf sağlayacaktır ama bu yalnızca kullanılan malzemelerin birbirine yapışması durumunda mümkündür.</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "Bir modelin etrafındaki brim, istemediğiniz yerden başka bir modele değebilir. Bu, brim olmayan modellerde bu mesafedeki tüm brimleri ortadan kaldırır."

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "Modelin kenarlarından bırakılması gereken mesafe. Ağın kenarlarına kadar ütülemek baskınızın kenarlarının pürüzlü olmasına neden olabilir."

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "Besleme ünitesi ile nozül haznesi arasına sıkıştırılacak filamenti belirten faktördür ve filament değişimi için malzemenin ne kadar hareket ettirileceğini belirlemek için kullanılır."

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Üst yüzey katmanları çizgi veya zikzak biçimindeyken kullanılacak tam sayı hat yönü listesi. Listedeki öğeler, katmanlar ilerledikçe sıralı olarak kullanılır. Listenin sonuna ulaşıldığında tekrar başa dönülür. Liste öğeleri virgülle ayrılır ve tüm liste köşeli parantez içine alınır. Varsayılan ayar boş listedir ve geleneksel varsayılan açılar (45 ve 135 derece) kullanılır."

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Üst/alt katmanlar çizgi veya zikzak şekillerini kullandığında kullanılacak tam sayı çizgi yönü listesi. Listedeki öğeler, katmanlar ilerledikçe sıralı olarak kullanılır. Listenin sonuna ulaşıldığında baştan başlanır. Liste öğeleri virgülle ayrılır ve tüm liste köşeli parantez içine alınır. Varsayılan ayar boş listedir ve geleneksel varsayılan açılar (45 ve 135 derece) kullanılır."

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "Kullanılacak tam hat yönlerinin listesi. Katmanlar ilerledikçe listedeki öğeler sırayla kullanılır ve listenin sonuna gelindiğinde tekrar baştan başlanır. Liste öğeleri virgülle ayrılır ve listenin tamamı köşeli paranteze alınır. Varsayılan ayar listenin boş olmasıdır ve bu durumda varsayılan açı 0'dır."

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Kullanılacak tam hat yönlerinin listesi. Listedeki öğeler katmanlar ilerledikçe sırayla kullanılır ve listenin sonuna gelindiğinde tekrar baştan başlanır. Liste öğeleri virgülle ayrılır ve listenin tamamı köşeli paranteze alınır. Varsayılan ayar, varsayılan açıların kullanıldığı (ara birimler biraz kalınsa 45 ile 135 derece arasında değişir veya 90 derecedir) boş listedir."

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Kullanılacak tam hat yönlerinin listesi. Katmanlar ilerledikçe listedeki öğeler sırayla kullanılır ve listenin sonuna gelindiğinde tekrar baştan başlanır. Liste öğeleri virgülle ayrılır ve listenin tamamı köşeli paranteze alınır. Varsayılan ayar, varsayılan açıların kullanıldığı (ara birimler biraz kalınsa 45 ile 135 derece arasında değişir veya 90 derecedir) boş listedir."

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "Kullanılacak tam hat yönlerinin listesi. Katmanlar ilerledikçe listedeki öğeler sırayla kullanılır ve listenin sonuna gelindiğinde tekrar baştan başlanır. Liste öğeleri virgülle ayrılır ve listenin tamamı köşeli paranteze alınır. Varsayılan ayar, varsayılan açıların kullanıldığı (ara birimler biraz kalınsa 45 ile 135 derece arasında değişir veya 90 derecedir) boş listedir."

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "Kullanılacak tam sayı hat yönü listesi. Listedeki öğeler, katmanlar ilerledikçe sıralı olarak kullanılır. Listenin sonuna ulaşıldığında baştan başlanır. Liste öğeleri virgülle ayrılır ve tüm liste köşeli parantez içine alınır. Varsayılan ayar boş listedir ve geleneksel varsayılan açılar kullanılır (çizgiler ve zikzak şekiller için 45 ve 135 derece; diğer tüm şekiller için 45 derece)."

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "Nozülün girmesine izin verilmeyen alanlara sahip poligon listesi."

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "Yazıcı başlığının giremediği alanları olan poligon listesi."

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "Dalların destekledikleri noktalardan ne kadar uzağa hareket edebileceğine dair bir tavsiye. Dallar hedeflerine (yapı levhası veya modelin düz bir parçası) ulaşmak için bu değeri ihlal edebilir. Bu değeri düşürmek, desteği daha sağlam hale getirecek, ancak dal miktarını (ve bu nedenle, malzeme kullanımı/baskı süresini) artıracaktır"

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "Mutlak Ekstruder İlk Konumu"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "Uyarlanabilir Katmanların Azami Değişkenliği"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "Uyarlanabilir Katman Topografisi Boyutu"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "Uyarlanabilir Katmanların Değişkenlik Adım Boyu"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "Uyarlanabilir katmanlar modelin şekline bağlı olarak katman yüksekliğini hesaplar."

msgctxt "infill_wall_line_count description"
msgid "Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\nThis feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr "Dolgu alanının etrafına ekstra duvar ekle. Bu duvarlar üst/alt yüzey hatlarının daha az aşağı düşmesini sağlar. Yani biraz fazla materyal kullanarak, daha az üst/alt yüzey katmanı ile aynı kaliteyi yakalayabilirsiniz."
"Bu özellik, doğru konfigüre edildiğinde, harekete veya geri çekmeye gerek kalmadan Dolgu Poligonlarını Bağlama ile birlikte tüm dolguyu tek bir ekstrüzyon yoluna bağlayabilir."

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "Yapıştırma"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "Yapışma Eğilimi"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Duvarlar ile yüzey ekseni (uçları) arasındaki çakışma miktarını yüzey hatlarının hat genişliği ile en içteki duvarın bir yüzdesi olarak ayarlayın. Az miktar çakışma duvarların yüzeye sıkıca bağlanmasını sağlar. Eşit yüzey ve duvar hattı genişliği söz konusu olduğunda, %50’nin üstündeki yüzdelerde bu noktada yüzey ekstrüderinin nozül konumu halihazırda duvarın ortasına ulaşmış olacağından yüzeyin duvarı geçmiş olabileceğini unutmayın."

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "Duvarlar ile yüzey ekseni (uçları) arasındaki çakışma miktarını ayarlayın. Az miktar çakışma duvarların yüzeye sıkıca bağlanmasını sağlar. Eşit yüzey ve duvar hattı genişliği söz konusu olduğunda, duvar kalınlığının yarısından fazla değerlerde bu noktada yüzey ekstrüderinin nozül konumu halihazırda duvarın ortasına ulaşmış olacağından yüzeyin duvarı geçmiş olabileceğini unutmayın."

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "Yazdırma dolgusunun yoğunluğunu ayarlar."

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Destek yapısının çatılarının ve zeminlerinin yoğunluğunu ayarlar. Daha yüksek bir değer daha iyi çıkıntılar ortaya çıkarırken, desteklerin kaldırılmasını zorlaştırır."

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "Dalların uçlarını oluşturmak için kullanılan destek yapısının yoğunluğunu ayarlar. Daha yüksek bir değer daha iyi çıkıntılar sağlar, ancak desteklerin çıkarılması daha zordur. Çok yüksek değerler için Destek Çatısı’nı kullanın veya destek yoğunluğunun en üstte benzer şekilde yüksek olmasını sağlayın."

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Destek yapısının yoğunluğunu ayarlar. Daha yüksek bir değer daha iyi çıkıntılar ortaya çıkarırken desteklerin kaldırılmasını zorlaştırır."

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "Kullanılan filamanın çapını ayarlar. Bu değeri kullanılan filaman çapı ile eşitleyin."

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "Destek yapılarının yerleştirilmesini ayarlar. Yerleştirme, temas eden yapı levhasına veya her bölüme ayarlanabilir. Her bölüme ayarlandığında, destek yapıları da modelde yazdırılacaktır."

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "Bir nozül ile ilk direği yazdırdıktan sonra, diğer nozülden ilk direğe sızdırılan malzemeyi silin."

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "Makine bir ekstruderden diğerine geçtikten sonra, nozül ve baskı arasında açıklık oluşması için yapı levhası indirilir. Nozülün baskı dışına malzeme sızdırmasını önler."

msgctxt "retraction_combing option all"
msgid "All"
msgstr "Tümü"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "Tümünü birden"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "Yazdırma çözünürlüğünü etkileyen tüm ayarlar. Bu ayarların (ve yazdırma süresinin) kalite üzerinde büyük bir etkisi vardır"

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "Yazdırma sırasını manuel olarak ayarlamak için nesne listesini sıralamanızı sağlar. Listedeki ilk nesne ilk olarak yazdırılacaktır."

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "Alternatif Ek Duvar"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "Alternatif Örgü Giderimi"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "Duvar Yönlerini Değiştir"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "Duvar yönlerini her katmanda ve iç dolguda değiştirin. Metal baskıdaki gibi stres oluşturabilen malzemeler için kullanışlıdır."

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "Alüminyum"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "Her Zaman Aktif Aracı Yaz"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "Dış duvar başlatmaya giderken her zaman geri çeker."

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "Her katmandaki poligonlara uygulanan ofset miktarı. Pozitif değerler büyük boşlukları telafi ederken negatif değerler küçük boşlukları telafi edebilir."

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "İlk katmandaki tüm poligonlara uygulanan ofset miktarı. Negatif bir değer “fil ayağı” olarak bilinen ilk katman ezilmesini dengeleyebilir."

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "Her katmandaki tüm destek poligonlarına uygulanan ofset miktarı. Pozitif değerler destek alanlarını pürüzsüzleştirebilir ve daha sağlam destek sağlayabilir."

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "Destek zeminlerine uygulanan ofset miktarı."

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "Destek çatılarına uygulanan ofset miktarı."

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "Destek arayüzü poligonlarına uygulanan ofset miktarı."

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "Filamanın sürme dizisi sırasında sızıntı yapmaması için filanın geri çekilme miktarı."

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "Bu küpün bölünüp bölünmemesine karar vermek için modelin sınırını kontrol eden ve her bir küpün merkezinden alınan yarıçapa ekleme. Büyük değerler modelin sınırının yanında daha kalın küçük küp kalkanları oluşmasına neden olur."

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "Çıkıntı Önleme Örgüsü"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "Sızma Önleme Geri Çekme Mesafesi"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "Sızma Önleme Geri Çekme Hızı"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "Ekstrüder ofsetini koordinat sistemine uygulayın. Tüm ekstrüderleri etkiler."

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "Modellerin temas ettiği yerlerde, iç içe geçen bir kiriş yapısı oluşturun. Bu, özellikle farklı malzemelerden basılmış modellerde, modeller arasındaki yapışmayı iyileştirir."

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "Hareket Sırasında Yazdırılan Bölümleri Atlama"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "Hareket Sırasında Destekleri Atla"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "Geri"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "Sol Arka"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "Sağ Arka"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "Her İkisi"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "Her ikisi de çakışıyor"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "Alt katmanlar"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "Alt Şekil İlk Katmanı"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "Alt Yüzey Genişleme Mesafesi"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "Alt Yüzey Kaldırma Genişliği"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "Alt Kalınlık"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "Dal Yoğunluğu"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "Dal Çapı"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "Dal Çapı Açısı"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "Geri Çekme Pozisyonunda Durma Mesafesi"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "Durma Payına Uygun Geri Çekme Hızı"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "Kopma Hazırlığı Sıcaklığı"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "Kopma Geri Çekme Mesafesi"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "Kopma Geri Çekme Hızı"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "Kopma Sıcaklığı"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "Parçalarda Döküm Desteği"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "Köprü Fan Hızı"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "Köprüde Birden Fazla Katman Var"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "Köprü İkinci Yüzey Alanı Yoğunluğu"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "Köprü İkinci Yüzey Alanı Fan Hızı"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "Köprü İkinci Yüzey Alanı Akışı"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "Köprü İkinci Yüzey Alanı Hızı"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "Köprü Yüzey Alanı Yoğunluğu"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "Köprü Yüzey Alanı Akışı"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "Köprü Yüzey Alanı Hızı"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "Köprü Yüzey Alanı Destek Eşiği"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "Maksimum Köprü Seyrek Dolgu Yoğunluğu"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "Köprü Üçüncü Yüzey Alanı Yoğunluğu"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "Köprü Üçüncü Yüzey Alanı Fan Hızı"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "Köprü Üçüncü Yüzey Alanı Akışı"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "Köprü Üçüncü Yüzey Alanı Hızı"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "Köprü Duvarı Tarama"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "Köprü Duvarı Akışı"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "Köprü Duvarı Hızı"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Kenar"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "Brim Engelleme Toleransı"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "Uç Mesafesi"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Kenar Hattı Sayısı"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "Brim Konumu"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Kenar, Desteği Değiştirir"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Kenar Genişliği"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "Yapı Levhası Yapıştırması"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "Yapı Levhası Yapıştırma Ekstruderi"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "Yapı Levhası Türü"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "Yapı Levhası Malzemesi"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "Yapı Levhası Şekli"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "Yapı Levhası Sıcaklığı"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "İlk Katman Yapı Levhası Sıcaklığı"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "Yapı Disk Bölümü Sıcaklığı"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "Yapı Hacmi Sıcaklık Sınırı"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "Yapı Hacmi sıcaklığı Uyarısı"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "Bu ayarı etkinleştirmeniz, modelinizde olmasa bile prime tower'ınıza bir brim kazandırır. Eğer yüksek bir kule için daha sağlam bir taban istiyorsanız, taban yüksekliğini artırabilirsiniz."

msgctxt "center_object label"
msgid "Center Object"
msgstr "Nesneyi ortalayın"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "En az desteğin istenmesi için yazdırılan modelin geometrisini değiştirin. Dik çıkıntılar sığlaşacaktır. Çıkıntılı alanlar daha dikey biçimde olmak için alçalacaktır."

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "Destek oluşturmak için kullanılabilir teknikler arasından seçim yapar. \"Normal\" destek, çıkıntılı parçaların hemen altında bir destek yapısı oluşturur ve bu alanları dümdüz aşağı indirir. \"Ağaç\"destek, çıkıntılı alanlara doğru dallar oluşturur ve bu dalların uçlarıyla model desteklenir; dallar modelin etrafına sarılarak yapı plakasından olabildiğince destek alır."

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "Tarama Hızı"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "Tarama Hacmi"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "Tarama, ekstrüzyon yolunun son parçasını hareket parça ile değiştirir. Dizimli azaltmak amacıyla sızdırılan malzeme ekstrüzyon yolunun son parçasını yazdırmak için kullanılır."

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "Tarama Modu"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "Tarama, hareket sırasında nozülü daha önce yazdırılmış alanlarda tutar. Bu durum hareketleri biraz uzatır ancak geri çekme ihtiyacını azaltır. Tarama kapalıysa malzeme geri çekilecektir, nozül ise bir sonraki noktaya düz bir çizgi üzerinden gider. Üst/alt yüzey alanlarının üzerinde tarama yapılmayabilir veya sadece dolgu içerisinde tarama yapılabilir."

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "Komut Satırı Ayarları"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Eş Merkezli"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "Eş Merkezli"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "Konik Destek Açısı"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "Koni Desteğinin Minimum Genişliği"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "Dolgu Hatlarını Bağlayın"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "Dolgu Poligonlarını Bağla"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "Destek Çizgilerini Bağla"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "Destek Zikzaklarını Bağla"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "Üst/Alt Poligonları Bağla"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "Yan yana giden dolgu yollarını bağla. Birkaç kapalı poligondan oluşan dolgu şekilleri için bu ayarı etkinleştirmek hareket süresini büyük ölçüde kısaltır."

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "Zikzakları Bağla Zik zak destek yapısının sağlamlığını artırır."

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "Destek çizgilerinin uçlarını birbirine bağlayın. Bu ayarın etkinleştirilmesi, desteğinizi daha sağlam hale getirebilir ve ekstruzyonu azaltabilir ancak bu daha fazla malzemeye mal olacaktır."

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "İç duvarın şeklini takip eden bir hattı kullanarak dolgu şeklinin iç duvarla buluştuğu noktada uçları bağlar. Bu ayarın etkinleştirilmesi, dolgunun duvarlara daha iyi yapışmasını sağlayabilir ve dolgunun dikey yüzeylerin kalitesinin etkilerini azaltabilir. Bu ayarın devre dışı bırakılması, kullanılan malzemenin miktarını azaltır."

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "Üst/alt yüzey yollarını yan yana ise bağla. Eş merkezli şekil için bu ayarı etkinleştirmek, hareket süresini önemli ölçüde kısaltır ancak bağlantılar dolgunun üzerinde meydana gelebileceğinden bu özellik üst yüzeyin kalitesini düşürebilir."

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "Modelin ana hatlarında yer alan köşelerin dikişin konumunu etkileyip etkilemediğini kontrol edin. Hiçbiri, köşelerin dikişin konumunu etkilemediği anlamına gelir. Dikişi Gizle, dikişin daha büyük olasılıkla bir iç köşe üzerinde oluşmasını sağlar. Dikişi Açığa Çıkar, dikişin daha büyük olasılıkla bir dış köşe üzerinde oluşmasını sağlar. Dikişi Gizle veya Açığa Çıkar, dikişin daha büyük olasılıkla bir iç veya dış köşe üzerinde oluşmasını sağlar. Akıllı Gizleme, hem iç hem de dış köşelere izin verir ancak uygun olduğu durumlarda iç köşeleri daha sık seçer."

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "Her bir dolgu hattını bu sayıda hatta dönüştür. Ekstra hatlar birbirlerini kesmez, birbirlerinden bağımsız kalırlar. Bu dolguyu sertleştirir, ancak yazdırma süresini uzatırken materyal kullanımını artırır."

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "Soğuma hızı"

msgctxt "cooling description"
msgid "Cooling"
msgstr "Soğuma"

msgctxt "cooling label"
msgid "Cooling"
msgstr "Soğuma"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "Çapraz"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "Çapraz"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "Çapraz 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "Çapraz 3D Cebin Boyutu"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "Destek için Çapraz Dolgu Yoğunluğu Görüntüsü"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "Çapraz Dolgu Yoğunluğu Görüntüsü"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "Kristalli Malzeme"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "Kübik"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "Kübik Alt Bölüm"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "Kübik Alt Bölüm Kalkanı"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "Kesme Örgüsü"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "Malzeme akışını (saniye başına mm3 bazında) sıcaklığa (santigrat derece) bağlayan veri."

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "Varsayılan İvme"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "Varsayılan Yapı Levhası Sıcaklığı"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "Varsayılan Filaman Salınımı"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "Varsayılan Yazdırma Sıcaklığı"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "Varsayılan X-Y Salınımı"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "Varsayılan Z Salınımı"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "Yatay düzlemdeki hareketler için varsayılan salınım."

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Z yönü motoru için varsayılan salınım."

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "Filaman motoru için varsayılan salınım."

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "Köprüleri tespit edin ve köprüler yazdırılırken yazdırma hızını, akışı ve fan ayarlarını değiştirin."

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "Duvarların basılacağı sırayı belirler. Dış duvarların önce basılması, iç duvarlardaki hataların dışarıya taşmasını önleyerek boyutların doğru olmasını sağlar. Bu duvarların daha sonra basılması ise çıkıntılar basılırken daha iyi yığınlanma sağlar. Toplam iç duvar miktarı eşit değilse ”ortadaki son hat” her zaman en son yazdırılır."

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "Çakışan birden çok dolgu örgüsünü göz önüne alarak bu örgünün önceliğini belirler. Birden çok dolgu örgüsünün çakıştığı alanlar en yüksek sıralamaya sahip örgünün ayarlarını alacaktır. Daha yüksek sıralamaya sahip dolgu örgüsü, dolgu örgülerinin dolgusunu daha düşük sıralı ve normal örgüler ile değiştirecektir."

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "Bir yıldırım dolgu tabakasının üstünde kalanları ne zaman desteklenmesi gerektiğini belirler. Bir katmanın kalınlığı verilen açıyla ölçülür."

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "Bir yıldırım dolgu tabakasının üstündeki modeli ne zaman desteklemesi gerektiğini belirler. Dalların açısı olarak ölçülür."

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "Çap"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "Modele Göre Çap Artışı"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "Her dalın yapı levhasına ulaşırken elde etmeye çalıştığı çap. Yatak yapışmasını geliştirir."

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "Ekstrüzyon işlemine hazırlamayı ve yapı levhasına yapışmayı artıran farklı seçenekler. Kenar, eğilmeyi önlemek için model tabanının etrafına tek katmanlı düz bir alan ekler. Radye, modelin altına çatısı olan kalın bir ızgara ekler. Etek modelin etrafına yazdırılan bir hattır fakat modele bağlı değildir."

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "İzin Verilmeyen Alanlar"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "Yazdırılan dolgu hatları arasındaki mesafe. Bu ayar, dolgu yoğunluğu ve dolgu hattı genişliği ile hesaplanır."

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "Yazdırılan ilk katman destek yapı hatları arasındaki mesafedir. Bu ayar destek yoğunluğuna göre hesaplanır."

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "Yazdırılan destek zemini çizgileri arasındaki mesafe. Bu ayar Destek Zemini Yoğunluğu ile hesaplanır, ancak ayrıca ayarlanabilir."

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "Yazdırılan destek çatısı çizgileri arasındaki mesafe. Bu ayar Destek Çatısı Yoğunluğu ile hesaplanır, ancak ayrıca ayarlanabilir."

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "Yazdırılan destek yapısı hatları arasındaki mesafe. Bu ayar, destek yoğunluğu ile hesaplanır."

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "Baskıdan desteğin altına kadar olan mesafe. Bunun bir sonraki katman yüksekliğine yuvarlandığını unutmayın."

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "Yazdırılıcak desteğin üstüne olan mesafe."

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "Desteğin üstü/altı ile baskı arasındaki mesafe. Bu boşluk, model basıldıktan sonra desteklerin kolayca çıkarılabilmesini sağlar. Modelin altındaki en üst destek katmanı, düzenli katmanların bir kısmı olabilir."

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "Dolgunun duvarlara daha iyi yapışması için her dolgu hattından sonra eklenen hareket mesafesi. Bu seçenek, dolgu çakışmasına benzer, ancak ekstrüzyon yoktur ve sadece dolgu hattının bir ucunda çakışma vardır."

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "Z dikişini daha iyi gizlemek için dış duvardan sonra eklenen hareket mesafesi."

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "Cereyan kalkanını X/Y yönlerindeki baskıya mesafesi."

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "Sızdırma kalkanını X/Y yönlerindeki baskıya mesafesi."

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "Destek yapısının X/Y yönlerindeki çıkıntıya mesafesi."

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "Destek yapısının X/Y yönlerindeki baskıya mesafesi."

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Yolu düzeltmek için mesafe noktaları kaydırılır"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "Yolu düzeltmek için mesafe noktaları kaydırılır"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "Bundan küçük dolgu alanları oluşturma (onun yerine yüzey kullan)."

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "Cereyan Kalkanı Yüksekliği"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "Cereyan Kalkanı Sınırlaması"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "Cereyan Kalkanı X/Y Mesafesi"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "Alçalan Destek Örgüsü"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "İkili ekstrüzyon"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "Eliptik"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "İvme Kontrolünü Etkinleştir"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "Köprü Ayarlarını Etkinleştir"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "Taramayı Etkinleştir"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "Konik Desteği Etkinleştir"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "Cereyan Kalkanını Etkinleştir"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "Akışkan Hareketini Etkinleştir"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "Ütülemeyi Etkinleştir"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "Salınım Kontrolünü Etkinleştir"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "Nozül Sıcaklığı Kontrolünü Etkinleştir"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "Sızdırma Kalkanını Etkinleştir"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "İlk Damlayı Etkinleştir"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "İlk Direği Etkinleştir"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "Yazdırma Soğutmayı Etkinleştir"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "Yazdırma İşlemi Raporlamasını Etkinleştir"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "Geri Çekmeyi Etkinleştir"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "Destek Kenarını Etkinleştir"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "Destek Zeminini Etkinleştir"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "Destek Arayüzünü Etkinleştir"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "Destek Çatısını Etkinleştir"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "Hareket İvmesini Etkinleştir"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "Hareket Salınımını Etkinleştir"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "Dış sızdırma kalkanını etkinleştirir. Modelin etrafında, ilk nozül ile aynı yükseklikte olması halinde ikinci bir nozülü temizleyebilecek olan bir kalkan oluşturacaktır."

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "Olası hata tespitine yönelik eşik değerlerini ayarlamak için yazdırma işlemi raporlamasını etkinleştirin."

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "En üstteki yüzey alanı katmanındaki (havaya maruz kalan) küçük (\"Küçük Üst/Alt Genişliği\"ne kadar) bölgeleri varsayılan desen yerine duvarlarla dolduran ayarı etkinleştirin."

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "X veya Y eksenlerindeki hareket hızı değiştiğinde yazıcı başlığının salınımının ayarlanmasını sağlar. Salınımı artırmak, yazdırma süresini azaltırken yazma kalitesinden ödün verir."

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "Yazıcı başlığı ivmesinin ayarlanmasını sağlar. İvmeleri artırmak, yazdırma süresini azaltırken yazma kalitesinden ödün verir."

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "Yazdırma sırasında yazdırma soğutma fanlarını etkinleştirir. Fanlar, katman süresi kısa olan katmanlar ve kemerlerde/çıkıntılarda yazdırma kalitesini artırır."

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "G-code’u Sonlandır"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "Filament Temizliği Bitiş Uzunluğu"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "Filament Temizliği Bitiş Hızı"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "İlgili alan üzerinde destek olsa bile kenarı modelin çevresine yazdırmaya zorlayın. Desteğin ilk katmanının bazı alanlarını kenar alanları ile değiştirir."

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "Her Yerde"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "Her bölüm"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "Dışlayıcı"

msgctxt "experimental label"
msgid "Experimental"
msgstr "Deneysel"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "Dikişi Açığa Çıkar"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "Geniş Dikiş"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "Geniş Dikiş, bitişik poligonlarla dikişleri kapatarak ağdaki açık boşlukların dikmeye çalışır. Bu seçenek çok fazla işlem süresi ortaya çıkarabilir."

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "Ekstra Dolgu Duvar Sayısı"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "Ek Dış Katman Duvar Sayısı"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "Nozül değişiminin ardından çalışmaya hazırlanacak ek malzemedir."

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "Extruder İlk X konumu"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "Extruder İlk Y konumu"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "Ekstruder İlk Z konumu"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "Ekstrüderler Isıtıcıyı Paylaşır"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "Ekstrüder Nozül Paylaşımı"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "Ekstrüzyon Sırasında Soğuma Hızı Düzenleyici"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "Hız için ekstrüzyon genişliği bazlı düzeltme faktörüdür. %0 değerinde hareket hızı Baskı Hızında sabit tutulur. %100 değerinde ise hareket hızı akış (mm³/s cinsinden) sabit tutulacak şekilde ayarlanır, yani normal Hat Genişliğinin yarısı iki kat daha hızlı basılır ve hatlar iki kat daha hızlı basılır. %100'den büyük değerler belirlenmesi, geniş hatların ekstrüde edilmesi için gereken yüksek basıncın telafi edilmesine yardımcı olabilir."

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "Fan Hızı"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "Fan Hızı Geçersiz Kılma"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "Bu uzunluktan kısa olan özellik ana hatları Kısa Özellik Hızı kullanılarak basılacaktır."

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "Henüz tamamen detaylandırılmamış özelliklerdir."

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "Besleyici Çark Çapı"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "Son Yazdırma Sıcaklığı"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "Üretici Yazılımı Geri Çekme"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "İlk Katman Destek Ekstruderi"

msgctxt "material_flow label"
msgid "Flow"
msgstr "Akış"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "Akış Eşitleme Oranı"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "Akış Sınırı"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "Akış hızı dengeleme çarpanı"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "Akış hızı dengelemesi maksimum ekstrüzyon kayması"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "Akış Sıcaklık Grafiği"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "Akış Uyarısı"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "İlk katman için akış dengelemesi: ilk katmana ekstrude edilen malzeme miktarı bu değerle çarpılır."

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "İlk katmanın alt hatlarında akış telafisi"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "Dolgu hatlarının akış telafisidir."

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "Destek çatı ve zemin hatlarının akış telafisidir."

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "Baskının üst bölümlerindeki hatların akış telafisidir."

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "Temel kule hatlarının akış telafisidir."

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "Etek veya kenar hatlarının akış telafisidir."

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "Destek zemin hatlarının akış telafisidir."

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "Destek çatı hatlarının akış telafisidir."

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "Destek yapı hatlarının akış telafisidir."

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "İlk katmanın en dış duvar hattında akış telafisi."

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "En dıştaki duvar hattının akış telafisidir."

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "Üst Yüzeyin En Dış Duvar Hattında Akış Telafisi."

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "Tüm duvar hatları için dıştaki hariç üst yüzey duvar hatlarında akış telafi."

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "Üst/alt hatların akış telafisidir."

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "En dıştaki duvar hatları hariç tüm duvar hatları için duvar hatlarında akış telafisi, ancak sadece ilk katman içindir."

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "En dıştaki duvar hattı hariç diğer duvar hatlarının akış telafisidir."

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "Duvar hatlarının akış telafisidir."

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "Akış dengeleme: sıkıştırılan malzeme miktarı bu değerle çoğaltılır."

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "Akışkan Hareket Açısı"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "Akışkan Hareketi Kaydırma Mesafesi"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "Akışkan Hareketi Küçük Mesafe"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "Temizleme Uzunluğu"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "Temizleme Hızı"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "Nozül boyutunun bir veya iki katı kadar olan ince yapılarda modelin kalınlığına bağlı olarak hat genişliklerinin değiştirilmesi gerekir. Bu ayar, duvarlar için izin verilen minimum hat genişliğini kontrol eder. Minimum hat genişlikleri, N duvarlarının geniş ve N+1 duvarlarının dar olduğu bazı geometrik kalınlıklarda N duvardan N+1 duvara geçildiği için maksimum hat genişliklerini de belirler. Mümkün olan en geniş duvar hattı Minimum Duvar Hattı Genişliğinin iki katıdır."

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "Ön"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "Sol Ön"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "Sağ Ön"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "Tam"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "Belirsiz Dış Katman"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "Belirsiz Dış Katman Yoğunluğu"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "Yalnızca Belirsiz Dış Katman"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "Belirsiz Dış Katman Noktası Mesafesi"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "Belirsiz Dış Katman Kalınlığı"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "G-code türü"

msgctxt "machine_end_gcode description"
msgid "G-code commands to be executed at the very end - separated by \n."
msgstr "En son çalıştırılacak G-code komutları ("
" ile ayrılır)."

msgctxt "machine_start_gcode description"
msgid "G-code commands to be executed at the very start - separated by \n."
msgstr "ile ayrılan, başlangıçta yürütülecek G-code komutları"
"."

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "Malzemedeki GUID Otomatik olarak ayarlanır."

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "Portal Yüksekliği"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "İç İçe Geçen Yapı Oluşturma"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "Oluşturma Desteği"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "İlk katmanın destek dolgu alanı içinde bir kenar oluşturun. Bu kenar, desteğin çevresine değil, altına yazdırılır. Bu ayarı etkinleştirmek, desteğin baskı tablasına yapışma alanını artırır."

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "Model ve destek arasında yoğun bir arayüz oluştur. Modelin yazdırıldığı desteğin üstünde ve modelin üzerinde durduğu desteğin altında bir yüzey oluşturur."

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "Desteğin alt kısmı ile model arasında yoğun bir levha oluşturur. Bu işlem, model ile destek arasında bir yüzey alanı oluşturacaktır."

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "Desteğin üst kısmı ile model arasında yoğun bir levha oluşturur. Bu işlem, model ile destek arasında bir yüzey alanı oluşturacaktır."

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "Modellerin askıda kalan kısımlarını destekleyen yapılar oluşturun. Bu yapılar olmadan, yazdırma sırasında söz konusu kısımlar düşebilir."

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "Cam"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "Üst yüzey üzerinden bir kere daha geçilir, ancak bu defa çok küçük malzeme ekstrüde edilir. Bu işlem en üstte bulunan plastiği eriterek daha pürüzsüz bir yüzey oluşturur. Nozül haznesindeki baskı yüksek tutularak yüzeydeki kıvrımların malzemeyle dolması sağlanır."

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "Aşamalı Dolgu Basamak Yüksekliği"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "Aşamalı Dolgu Basamakları"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "Aşamalı Destek Dolgusu Basamak Yüksekliği"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "Kademeli Destek Dolgusu Aşamaları"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "Minimum katman süresi nedeniyle düşük hızlarda yazdırırken bu sıcaklığa kademeli olarak düşürün."

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "Izgara"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "Izgara"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "Izgara"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "Izgara"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "Izgara"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "Dış Duvarları Grupla"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "Gyroid"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "Yapı Hacmi Sıcaklığı Dengesi Mevcut"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "Isıtılmış Yapı Levhası İçerir"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "Isınma Hızı"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "Isı Bölgesi Uzunluğu"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "Cereyan kalkanının yükseklik sınırı. Bundan daha fazla bir yükseklikte cereyan kalkanı yazdırılmayacaktır."

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "Dikişi Gizle"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "Dikişi Gizle veya Açığa Çıkar"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "Delik Yatay Büyüme"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "Delik Yatay Büyüme Maksimum Çapı"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "Bu değerden daha küçük çaptaki delik ve parça ana hatları Küçük Özellik Hızı kullanılarak basılacaktır."

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "Yatay Büyüme"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "Yatay Ölçekleme Faktörü Büzülme Telafisi"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "Filamentin ısıtıldığında kopmadan esneyebileceği mesafedir."

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "Malzemenin sızma yapmaması için gereken geri çekilme mesafesidir."

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "Akış hızındaki değişiklikleri telafi edebilmek için filamentin bir saniyelik ekstrüzyonda hareket ettirileceği mesafenin yüzdesi olarak filamentin ne kadar uzağa hareket ettirileceği."

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "Sorunsuz kopması için filamentin geri çekilmesi gereken mesafedir."

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "Filamentin kopmadan ne kadar hızlı geri çekilmesi gerektiğidir."

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "Filament değişimi sırasında malzemenin sızma yapmaması için gereken geri çekilme hızıdır."

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "Boş bir makara aynı malzemeden yeni bir makara ile değiştirildikten sonra malzemenin kullanıma hazır hale getirileceği süredir."

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "Farklı bir malzemeye geçildikten sonra malzemenin kullanıma hazır hale getirileceği süredir."

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "Malzemenin kuru olmadığı durumda güvenli şekilde saklanabileceği süredir."

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "Kademeli motorun kaç adımının, X yönünde bir milimetre hareketle sonuçlanacağı."

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "Kademeli motorun kaç adımının, Y yönünde bir milimetre hareketle sonuçlanacağı."

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "Kademeli motorun kaç adımının, Z yönünde bir milimetre hareketle sonuçlanacağı."

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "Kademeli motorların kaç adımının besleme ünitesi tekerleğini çevresi etrafında bir milimetre hareket ettirmekle sonuçlanacağı."

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "Boş bir makara aynı malzemeden yeni bir makara ile değiştirilirken nozülün önceki malzemeden temizlenmesi için kullanılacak malzeme (filament parçası) miktarıdır."

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "Farklı bir malzemeye geçilirken nozülün önceki malzemeden temizlenmesi için kullanılacak malzeme (filament parçası) miktarıdır."

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "Printer-start gcode betiğinin tamamlanmasında her bir ekstrüder filamentinin paylaşılan nozül ucundan ne kadar geri çekildiğinin varsayıldığıdır. Değer, nozül kanallarının ortak parçasının uzunluğuna eşit veya daha büyük olmalıdır."

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "Destek arayüzü ve destek çakıştıklarında nasıl etkileşime girerler? Şu anda sadece destek çatısı için uygulanmaktadır."

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "Model üzerine yerleştirilmesi gereken bir dalın ne kadar uzun olması gerektiği. Küçük destek lekelerini önler. Bir dalın bir destek çatısını desteklemesi durumunda bu ayar göz ardı edilir."

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "Eğer bir yüzey alanı bölgesi, alanının bu yüzdeden daha azı için destekleniyorsa, köprü ayarlarını kullanarak yazdırın. Aksi halde normal yüzey alanı ayarları kullanılarak yazdırılır."

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "Bir takım yolu parçası, genel harekete göre bu açıdan daha fazla bir sapma gösterirse düzeltilir."

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "Eğer etkinleştirilirse, havanın üzerindeki ikinci ve üçüncü katmanlar aşağıdaki ayarlar kullanılarak yazdırılır. Aksi halde bu katmanlar normal ayarlar kullanılarak yazdırılır."

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "Farklı sayıda duvar arasında arka arkaya hızlıca ileri geri geçiş yapılacaksa duvarlar arasında geçiş yapmayın. Duvarlar bir arada bu mesafeden daha yakındaysa geçişleri kaldırın."

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Raft tabanı etkinleştirilirse bu, ayrıca bir raft verilen modelin etrafındaki ekstra raft alanıdır. Bu toleransın arttırılması daha güçlü bir raft oluşturacak, daha fazla malzeme kullanacak ve baskınız için daha az alan bırakacaktır."

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Radye etkinleştirildiğinde, ayrıca radye verilen model etrafındaki ek radye alanıdır. Bu boşluğu artırmak, daha fazla malzeme kullanırken ve yazdırma için daha az alan bırakırken daha sağlam bir radye oluşturacaktır."

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Raft ortası etkinleştirilirse bu, ayrıca bir raft verilen modelin etrafındaki ekstra raft alanıdır. Bu toleransın arttırılması daha güçlü bir raft oluşturacak, daha fazla malzeme kullanacak ve baskınız için daha az alan bırakacaktır."

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "Raft üstü etkinleştirilmişse bu, ayrıca bir raft verilen modelin etrafındaki ekstra raft alanıdır. Bu toleransın arttırılması daha güçlü bir raft oluşturacak, daha fazla malzeme kullanacak ve baskınız için daha az alan bırakacaktır."

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "Bir örgü içinde çakışan hacimlerden kaynaklanan iç geometriyi yok sayın ve hacimleri tek bir hacim olarak yazdırın. Bu durum, istenmeyen iç boşlukların kaybolmasını sağlar."

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "Yapı Levhası Sıcaklığını Ekle"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "Malzeme Sıcaklıklarını Ekle"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "Kapsayıcı"

msgctxt "infill description"
msgid "Infill"
msgstr "Dolgu"

msgctxt "infill label"
msgid "Infill"
msgstr "Dolgu"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "Dolgu İvmesi"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "Duvarlardan Önce Dolgu"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "Dolgu Yoğunluğu"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "Dolgu Ekstruderi"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "Dolgu Akışı"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "Dolgu Salınımı"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "Dolgu Katmanı Kalınlığı"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "Dolgu Hattı Yönleri"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "Dolgu Hattı Mesafesi"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "Dolgu Hattı Çoğaltıcı"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "Dolgu Hattı Genişliği"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "Dolgu Ağı"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "Dolum Çıkıntı Açısı"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "Dolgu Çakışması"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "Dolgu Çakışma Oranı"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "Dolgu Şekli"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "Dolgu Hızı"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "Dolgu Desteği"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "Dolgu Hareket Optimizasyonu"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "Dolgu Sürme Mesafesi"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "Dolgu X Kayması"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "Dolgu Y Kayması"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "İlk Alt Katmanlar"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "İlk Fan Hızı"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "İlk Katman İvmesi"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "İlk Katman Alt Akışı"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "İlk Katman Çapı"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "İlk Katman Akışı"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "İlk Katman Yüksekliği"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "İlk Katmanın Yatay Genişlemesi"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "İlk Katman İç Duvar Akışı"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "İlk Katman Salınımı"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "İlk Katman Hat Genişliği"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "İlk Katman Dış Duvar Akışı"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "İlk Katman Yazdırma İvmesi"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "İlk Katman Yazdırma Salınımı"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "İlk Katman Yazdırma Hızı"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "İlk Katman Hızı"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "İlk Katman Destek Hattı Mesafesi"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "İlk Katman Hareket İvmesi"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "İlk Katman Hareket Salınımı"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "İlk Katman Hareket Hızı"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "İlk Katman Z Çakışması"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "İlk Yazdırma Sıcaklığı"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "İç Duvar İvmesi"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "İç Duvar Ekstrüderi"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "İç Duvar Salınımı"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "İç Duvar Hızı"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "İç Duvar Akışı"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "İç Duvar(lar) Hattı Genişliği"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "Dış duvar yoluna uygulanan ilave. Dış duvar nozülden küçükse ve iç duvardan sonra yazdırılmışsa, nozüldeki deliği modelin dış kısmı yerine iç duvarlar ile üst üste bindirmek için bu ofseti kullanın."

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "Yalnızca İçeride"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "İçten Dışa"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "Tercih edilen arayüz hatları"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "Tercih edilen arayüz"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "Aralıklı"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "İç İçe Geçen Kiriş Katman Sayısı"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "İç İçe Geçme Genişliği"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "İç İçe Geçme Sınırından Kaçınma"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "İç İçe Geçme Derinliği"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "İç İçe Geçen Yapı Uyumlaması"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "Sadece En Yüksek Katmanı Ütüle"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "Ütüleme İvmesi"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "Ütüleme Akışı"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "Ütüleme İlave Mesafesi"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "Ütüleme İvmesi Değişimi"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "Ütüleme Hattı Boşluğu"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "Ütüleme Modeli"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "Ütüleme Hızı"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "Merkez Nokta"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "Destek malzemesi mi"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "Bu malzeme ısıtıldığında temiz bir şekilde parçalanan tür de mi (kristalli) yoksa uzun iç içe polimer zincirler (kristal olmayan) oluşturan türde mi?"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "Bu malzeme genellikle baskı sırasında destek malzemesi olarak mı kullanılır"

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "Parçalardaki delikleri değil, yalnızca ana hatlarını titretir."

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "Bağlı Olmayan Yüzleri Tut"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "Katman Yüksekliği"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "Katman Başlangıcı X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "Katman Başlangıcı Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "Radyenin taban katmanının katman kalınlığı. Bu, yazıcı yapı levhasına sıkıca yapışan kalın bir katman olmalıdır."

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "Radyenin orta katmanının katman kalınlığı."

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "Üst radye katmanlarının katman kalınlığı."

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "Destek yapısının daha kolay kırılması için her N milimetresinde bir destek hatları arasında bağlantı atlayın."

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "Sol"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "Yazıcı Başlığını Kaldır"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "Yıldırım"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "Yıldırım Dolgu Çıkıntı Açısı"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "Yıldırım Dolgu Budama Açısı"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "Yıldırım Dolgu Düzleştirme Açısı"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "Yıldırım Dolgu Destek Açısı"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "Dal Erişimini Sınırla"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "Her dalın desteklediği noktadan ne kadar uzağa gitmesi gerektiğini sınırlayın. Bu sınırlama, desteği daha sağlam hale getirebilir, ancak dalların miktarını (ve bu nedenle, malzeme kullanımı/baskı süresini) artıracaktır"

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "Tespit için Yapı Hacmi Sıcaklığı Sınırı uyarısı."

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "Tespit için Yapı Hacmi Sıcaklığı Anormallik Sınırı."

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "Tespit için Yazdırma Sıcaklığı anormalliği sınırı."

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "Tespit için Yazdırma sıcaklığı uyarısı sınır."

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "Tespit için akış anormalliği sınırı."

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "Tespit için akış uyarısı sınırı."

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "Bu örgünün hacmini diğer örgülere göre sınırlandırın. Bir örgünün belirli alanlarını farklı ayarlarla ve tamamen farklı bir ekstrüder ile yazdırmak için bunu kullanabilirsiniz."

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "Sınırlı"

msgctxt "line_width label"
msgid "Line Width"
msgstr "Hat Genişliği"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "Çizgiler"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "Makine"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "Makine Derinliği"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "Makinenin Başlığı ve Fan Poligonu"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "Makine Yüksekliği"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "Makine Türü"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "Makine Genişliği"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "Makine özel ayarları"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "Çıkıntıyı Yazdırılabilir Yap"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "Birbirine dokunan örgülerin az oranda üst üste binmesini sağlayın. Böylelikle bunlar daha iyi birleşebilir."

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "Alttaki destek alanlarını çıkıntıda olanlardan daha küçük yapın."

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "Destek örgüsünde askıda kalan herhangi bir kısım olmaması için destek örgüsünün altındaki her yere destek yapın."

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "Ekstruder ilk konumunu, yazıcı başlığının son konumuna göre ayarlamak yerine mutlak olarak ayarlayın."

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\nIt may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr "Hava boşluğundaki filament kaybını telafi etmek için modelin birinci ve ikinci katmanını Z yönünde üst üste getirir. İlk model katmanının üzerindeki tüm modeller bu miktarda aşağı kaydırılacaktır."
"Bu ayar nedeniyle bazen ikinci katmanın ilk katmanın altına yazdırıldığı belirtilebilir. Bu istenilen davranıştır"

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "Kafesleri 3D baskı için daha uygun hale getirir."

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin (Volümetrik)"

msgctxt "material description"
msgid "Material"
msgstr "Malzeme"

msgctxt "material label"
msgid "Material"
msgstr "Malzeme"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "Malzeme Markası"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "GUID malzeme"

msgctxt "material_type label"
msgid "Material Type"
msgstr "Malzeme Türü"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "Sürme Hareketleri Arasındaki Malzeme Hacmi"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "Geri Çekmesiz Maks. Tarama Mesafesi"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "Maksimum X İvmesi"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "Maksimum Y İvmesi"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Maksimum Z İvmesi"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "Maksimum Dal Açısı"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "Maksimum Sapma"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "Maksimum Ekstrüzyon Alanı Sapması"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "Maksimum Fan Hızı"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "Maksimum Filaman İvmesi"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "Maksimum Model Açısı"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "Maksimum Çıkıntı Deliği Alanı"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "Maksimum Durma Süresi"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "Maksimum Çözünürlük"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "Maksimum Geri Çekme Sayısı"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "Genişleme için Maksimum Yüzey Açısı"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "Maksimum Hız E"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "Maksimum X Hızı"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Maksimum Y Hızı"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Maksimum Z Hızı"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "Kule Destekli Maksimum Çap"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "Maksimum Hareket Çözünürlüğü"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "X yönü motoru için maksimum ivme"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Y yönü motoru için maksimum ivme."

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Z yönü motoru için maksimum ivme."

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "Filaman motoru için maksimum ivme."

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "Seyrek olması düşünülen dolgunun maksimum yoğunluğu. Seyrek dolgu üzerindeki kaplama, desteksiz olacağı düşünülerek köprü kaplaması olarak değerlendirilir."

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "Özel bir destek kulesiyle desteklenecek küçük bir alanın X/Y yönlerindeki maksimum çapıdır."

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "Başka bir nozül sürme işlemi başlatılmadan önce ekstrüde edilebilecek maksimum malzeme miktarı. Bu değer, bir katmanda gereken malzeme hacminden daha düşükse ayarın bu katmanda bir etkisi olmayacaktır, yani katman başına bir sürme sınırı vardır."

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "Birleştirilmiş Bileşim Çakışması"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "Ağ Onarımları"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "Bileşim konumu X"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "Bileşim konumu Y"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "Bileşim konumu Z"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "Örgü İşleme Sıralaması"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "Bileşim Rotasyon Matrisi"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Ortalayıcı"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "Minimum Kalıp Genişliği"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "Minimum Sürede Bekleme Sıcaklığı"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "Minimum Köprü Duvarı Uzunluğu"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "Minimum Çift Duvar Hattı Genişliği"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "Minimum Geri Çekme Mesafesi Penceresi"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "Minimum Yüz Hattı Boyutu"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "Minimum Besleme Hızı"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "Modele Göre Minimum Yükseklik"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "Minimum Dolgu Alanı"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "Minimum Katman Süresi"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "Minimum Tek Duvar Hattı Genişliği"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "Minimum Poligon Çevre Uzunluğu"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "Genişleme için Minimum Yüzey Genişliği"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "Minimum Hız"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "Minimum Destek Bölgesi"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "Minimum Destek Zemini Bölgesi"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "Minimum Destek Arayüzü Bölgesi"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "Minimum Destek Çatısı Bölgesi"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "Minimum Destek X/Y Mesafesi"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "Minimum İnce Duvar Hattı Genişliği"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "Tarama Öncesi Minimum Hacim"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "Minimum Duvar Hattı Genişliği"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Destek arayüzü çokgenlerinin minimum alan boyutu. Alanı bu değerden küçük olan poligonlar normal destekle basılacaktır."

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "Destek poligonları için minimum alan boyutu. Alanı bu değerden daha düşük olan poligonlar oluşturulmayacaktır."

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Destek tabanlarının minimum alan boyutu. Alanı bu değerden küçük olan poligonlar normal destekle basılacaktır."

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "Destek çatılarının minimum alan boyutu. Alanı bu değerden küçük olan poligonlar normal destekle basılacaktır."

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "İnce yüz hatlarının minimum kalınlığıdır. Bu değerden daha ince olan model yüz hatları yazdırılmaz, Minimum Yüz Hattı Boyutundan daha kalın olan modeller ise Minimum Duvar Hattı Genişliği değerine kadar genişletilir."

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "Koni desteği tabanının indirildiği minimum genişlik. Küçük genişlikler, destek tabanlarının dengesiz olmasına neden olur."

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "Kalıp"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "Kalıp Açısı"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "Kalıp Çatı Yüksekliği"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "Monotonik Ütüleme Düzeni"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "Monotonik Raft Üst Yüzey Düzeni"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "Monotonik Üst Yüzey Düzeni"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "Monotonik Üst/Alt Düzeni"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "Çoklu etek hatları küçük modeller için daha iyi ekstrüzyon işlemi hazırlanmasına yardımcı olur. Bu değeri 0’a ayarlamak eteği devre dışı bırakacaktır."

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "İlk katman üzerinde bulunan hat genişliği çoğaltıcı. Çoğaltmayı artırmak yatak yapışmasını iyileştirebilir."

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "Yük Taşıma Çarpanı Yok"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Z Boşluklarında Dış Katman Oluşturma"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "Modellerinizi yazdırmanın geleneksel olmayan yollarıdır."

msgctxt "adhesion_type option none"
msgid "None"
msgstr "Hiçbiri"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "Hiçbiri"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "Normal"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "Normal koşullarda, Cura ağdaki küçük boşlukları diker ve büyük boşluklu katman parçalarını ortadan kaldırır. Bu seçeneği etkinleştirmek, dikilemeyen parçaları muhafaza eder. Bu seçenek, hiçbir işlemin uygun bir g-code oluşturamaması durumunda başvurulacak son seçenek olarak kullanılmalıdır."

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "Yüzey Alanında Değil"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "Dış Yüzeyde Değil"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "Nozül Açısı"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "Nozül Çapı"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "Nozül İzni Olmayan Alanlar"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "Nozül Kimliği"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "Nozül Uzunluğu"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "Nozül Değişimiyle Çalışmaya Hazırlanacak Ek Miktar"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "Nozül Değişiminin İlk Hızı"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "Nozül Değişiminin Geri Çekme Hızı"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "Nozül Anahtarı Geri Çekme Mesafesi"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "Nozül Anahtarı Geri Çekme Hızı"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "Ekstrüder Sayısı"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "Etkinleştirilmiş Ekstruder Sayısı"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "Daha Yavaş Katman Sayısı"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "Etkinleştirilmiş ekstruder dişli çarklarının sayısı; yazılımda otomatik olarak ayarlanır"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "Ekstruder dişli çarklarının sayısı. Ekstruder dişli çarkı besleyici, bowden tüpü ve nozülden oluşur."

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "Nozülün fırçadan geçirilme sayısı."

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "Üst yüzeylerin altına indikçe dolgu yoğunluğunu yarıya indirme sayısı. Üst yüzeylere daha yakın olan alanlarda, Dolgu Yoğunluğuna kadar yoğunluk daha yüksektir."

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "Üst yüzeylerin altına inerken destek dolgusu yoğunluğunu yarıya indirmek için inilecek yüzey sayısı. Üst yüzeylere daha yakın olan alanlarda yoğunluk daha fazladır ve Destek Dolgusu Yoğunluğuna kadar çıkabilir."

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "Sekizlik"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "Kapalı"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "Nesneye x yönünde uygulanan ofset."

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "Nesneye y yönünde uygulanan ofset."

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "Nesneye z yönünde uygulanan ofset. Bununla birlikte “Nesne Havuzu” olarak adlandırılan malzemeyi de kullanabilirsiniz."

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "Ekstruder Ofseti"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "Mümkün olduğunda yapı levhası üzerinde"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "Gerektiğinde model üzerinde"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "Birer Birer"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "Sadece Hareket Sırasında Yazdırılan Bölümleri Atlama yoluyla yatay hareket sayesinde atlanamayan yazdırılmış parçalar üzerinde hareket ederken Z Sıçramasını gerçekleştirin."

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "Ütüleme işlemini bileşimin sadece en son katmanı üzerinde gerçekleştirin. Bu, alt katmanlarda pürüzsüz bir yüzey tesviyesine gerek olmadığı durumlarda zaman kazandırır."

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "Sızdırma Kalkanı Açısı"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "Sızdırma Kalkanı Mesafesi"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "Optimum Dal Aralığı"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "Duvar Yazdırma Sırasını Optimize Et"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "Geri çekmelerin sayısını ve kat edilen mesafeyi azaltmak için duvarların yazdırıldığı sırayı optimize edin. Çoğu parça, bunun etkinleştirilmesinden yararlanır, ancak bazılarının yararlanması için gerçekte daha uzun süre gerekebilir. Bu yüzden, yazdırma süresi tahminlerini optimizasyonlu ve optimizasyonsuz olarak karşılaştırın. Kenar, yapı levhası yapıştırması tipi olarak seçildiğinde ilk katman optimize edilmez."

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "Dış Nozül Çapı"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "Dış Duvar İvmesi"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "Dış Duvar Ekstruderi"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "Dış Duvar Akışı"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "Dış Duvar İlavesi"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "Dış Duvar Salınımı"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "Dış Duvar Hattı Genişliği"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "Dış Duvar Hızı"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "Dış Duvar Sürme Mesafesi"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "Aynı katman içindeki farklı adalardaki dış duvarlar sırayla basılır. Etkinleştirildiğinde, akış değişiklik miktarı duvarlar tür türüne basıldığı için sınırlıdır, devre dışı bırakıldığında ise aynı adalardaki duvarlar gruplandığı için adalar arasındaki seyahat sayısı azalır."

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "Yalnızca Dışarıda"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "Dıştan İçe"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "Çıkıntılı Duvar Açısı"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "Çıkıntılı Duvar Hızı"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "Çıkıntılı duvarlar, normal yazdırma hızına göre bu yüzdeye denk bir hızda yazdırılacaktır."

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "Geri çekmenin geri alınmasından sonraki duraklama."

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "Köprü duvarları ve yüzey alanı yazdırılırken kullanılacak yüzde fan hızı."

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "İkinci köprü yüzey alanı katmanı yazdırılırken kullanılacak yüzde fan hızı."

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "Desteğin hemen üzerindeki yüzey bölgeleri yazdırılırken kullanılacak yüzdelik fan hızıdır. Yüksek fan hızı kullanmak desteğin daha kolay kaldırılmasını sağlayabilir."

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "Üçüncü köprü yüzey alanı katmanı yazdırılırken kullanılacak yüzde fan hızı."

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "Bu miktardan daha kısa çevre uzunluğuna sahip dilimlenmiş katmanlardaki poligonlar filtre ile elenecektir. Daha düşük değerler dilimleme süresini uzatacak ancak daha yüksek çözünürlükte bir ağ oluşturacaktır. Genellikle yüksek çözünürlüklü SLA yazıcılarına yöneliktir ve çok fazla detay içeren çok küçük 3D modellerinde kullanılır."

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "Tercih Edilen Dal Açısı"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "Bir fazla ve bir az duvar arasında ileri geri geçişi önleyin. Bu kenar boşluğu, [Minimum Duvar Hattı Genişliği - Kenar Boşluğu, 2 * Minimum Duvar Hattı Genişliği+Kenar Boşluğu] olarak takip edilen hat genişliklerinin aralığını genişletir. Bu kenar boşluğunun artırılması geçişlerin sayısını azaltır, bu da ekstrüzyon başlatma/durdurma sayısını ve hareket süresini azaltır. Bununla birlikte, geniş hat varyasyonları düşük veya aşırı ekstrüzyon sorunlarına yol açabilir."

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "İlk Direk İvmesi"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "Başlangıç Kulesi Tabanı"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "Başlangıç Kulesi Taban Yüksekliği"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "Başlangıç Kulesi Taban Boyutu"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Prime Tower Taban Eğimi"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "İlk Direk Akışı"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "İlk Direk Salınımı"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "İlk Direk Hattı Genişliği"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "Asal Kule Maksimum Köprüleme Mesafesi"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "İlk Direğin Minimum Hacmi"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "Başlangıç Kulesi Salı İzi Aralığı"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "İlk Direk Boyutu"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "İlk Direk Hızı"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "Asal Kule Türü"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "İlk Direk X Konumu"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "İlk Direk Y Konumu"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "Yazdırma İvmesi"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "Yazdırma İvmesi Değişimi"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "Yazdırma Süreci Raporlaması"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "Yazdırma Dizisi"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "Yazdırma Hızı"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "İnce Duvarları Yazdır"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "Modelin dışına, içine veya her ikisine de bir brim yazdırın. Modele bağlı olarak bu, daha sonra çıkarmanız gereken brim miktarının azaltılmasına yardımcı olurken yatağın düzgünce yapışmasını sağlar."

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "Malzemenin hazırlanmasına yardımcı olan yazıcının yanındaki direği her nozül değişiminden sonra yazdırın."

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "Yazdırma dolgusu, yalnızca model tepelerinin desteklenmesi gereken yerleri yapılandırır. Bu özelliğin etkinleştirilmesi yazdırma süresini ve malzeme kullanımını azaltır ancak üniform olmayan nesne kuvvetine yol açar."

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Her zaman bitişik hatlarla tek yönde çakışmaya neden olan bir düzenle hatları ütüleyerek baskı yapın. Bu baskı biraz daha uzun sürer, fakat düz yüzeylerin daha tutarlı görünmesini sağlar."

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "Yapı levhası üzerinde modelleri toplayan bir model elde etmek amacıyla döküm olabilecek modelleri kalıp olarak yazdırır."

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "Yatay olarak nozül boyutundan daha ince olan model parçalarını yazdırır."

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "Raftın üst yüzey çizgilerini, her zaman bitişik çizgilerle tek bir yönde üst üste binmelerine neden olacak bir sırayla yazdırır. Bu, yazdırmanın biraz daha fazla zaman almasını sağlar ama yüzeyin daha tutarlı görünmesini sağlar, bu da modelin alt yüzeyinde de görülebilir."

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "İkinci köprü yüzey alanı katmanı yazdırılırken kullanılacak yazdırma hızı."

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "Üçüncü köprü yüzey alanı katmanı yazdırılırken kullanılacak yazdırma hızı."

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "Yazdırma sıcaklığı Sınırı"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "Yazdırma sıcaklığı Uyarısı"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "Duvarları yazdırmadan önce dolguyu yazdırın. Önce duvarları yazdırmak daha düzgün duvarlar oluşturabilir ama yazdırmayı olumsuz etkiler. Önce dolguyu yazdırmak duvarların daha sağlam olmasını sağlar, fakat dolgu şekli bazen yüzeyden görünebilir."

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Her zaman bitişik hatlarla tek yönde çakışmaya neden olan bir düzenle üst yüzey hatlarının baskısını yapın. Bu baskı biraz daha uzun sürer, fakat düz yüzeylerin daha tutarlı görünmesini sağlar."

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Her zaman bitişik hatlarla tek yönde çakışmaya neden olan bir düzenle üst/alt hat baskısı yapın. Bu baskı biraz daha uzun sürer, fakat düz yüzeylerin daha tutarlı görünmesini sağlar."

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "Yazdırma Sıcaklığı"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "İlk Katman Yazdırma Sıcaklığı"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "En içteki etek çizgisinin birden fazla katmanla yazdırılması, eteğin çıkarılmasını kolaylaştırır."

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "Her katmanda ek duvar yazdırır. Bu şekilde dolgu ek duvarların arasında alır ve daha sağlam baskılar ortaya çıkar."

msgctxt "resolution label"
msgid "Quality"
msgstr "Kalite"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "Çeyrek Kübik"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Radye"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Radye Hava Boşluğu"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "Raft Tabanı Ekstra Tolerans"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Radye Taban Ekstrüderi"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Radyenin Taban Fan Hızı"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Radye Taban Hat Genişliği"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Radyenin Taban Hat Genişliği"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Radyenin Taban Yazdırma İvmesi"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Radyenin Taban Yazdırma Salınımı"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Radyenin Taban Yazdırma Hızı"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "Raft Tabanı Düzeltme"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Radye Taban Kalınlığı"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Radye Tabanı Duvar Sayısı"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Ek Radye Boşluğu"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Radye Fan Hızı"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "Raft Ortası Ekstra Tolerans"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Radye Orta Ekstrüderi"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Radyenin Orta Fan Hızı"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Radye Orta Katmanları"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Radyenin Orta Hat Genişliği"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Radyenin Orta Yazdırma İvmesi"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Radyenin Orta Yazdırma Salınımı"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Radyenin Orta Yazdırma Hızı"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "Raft Orta Düzeltme"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Radye Orta Boşluğu"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Radye Orta Kalınlığı"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "Raft Orta Duvar Sayısı"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Radye Yazdırma İvmesi"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Radye Yazdırma Salınımı"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Radye Yazdırma Hızı"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Radye Düzeltme"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "Raft Üstü Ekstra Tolerans"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Radye Üst Ekstrüderi"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Radye Üst Fan Hızı"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Radyenin Üst Katman Kalınlığı"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Radyenin Üst Katmanları"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Radyenin Üst Hat Genişliği"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Radye Üst Yazdırma İvmesi"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Radye Üst Yazdırma Salınımı"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Radye Üst Yazdırma Hızı"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "Raft Üst Düzeltme"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Radyenin Üst Boşluğu"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "Raft Üstü Duvar Sayısı"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "Raft Duvar Sayısı"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "Gelişigüzel"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "Rastgele Boşluk Doldurma Başlat"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "Önce hangi boşluk doldurma hattının yapılacağını rastgele belirler. Böylece tek bir segmentin en güçlü yapıda olması önlenir ancak bu işlem ilave gezinti hamlelerine neden olabilir."

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "Yüzeyin sert ve belirsiz bir görüntü alması için dış duvarları yazdırırken rastgele titrer."

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "Dikdörtgen"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "Olağan Fan Hızı"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "Yüksekteki Olağan Fan Hızı"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "Katmandaki Olağan Fan Hızı"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "Olağan/Maksimum Fan Hızı Sınırı"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "Bağıl Ekstrüzyon"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "Tüm Boşlukları Kaldır"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "Boş İlk Katmanları Kaldır"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "Bileşim Kesişimini Kaldırın"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "Raft Tabanını İç Köşelerini Kaldır"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "Radye İç Köşelerini Kaldır"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "Raft Ortası İç Köşelerini Kaldır"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "Raft Üstü İç Köşelerini Çıkar"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "Birden fazla bileşimin çakıştığı alanları kaldırın. Bu, birleştirilmiş ikili malzemeler çakıştığında kullanılabilir."

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "Basılan ilk katmanın altındaki varsa boş katmanları kaldır. Bu ayarın devre dışı bırakılması, Dilimleme Toleransı Dışlayıcı veya Ortalayıcı olarak ayarlanmışsa, boş ilk katmanlar oluşmasına neden olabilir."

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "Raftın dışbükey olmasına neden olan raftın tabanındaki iç köşeleri çıkarır."

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "Raftın dışbükey olmasına neden olan raftın orta kısmındaki iç köşeleri çıkarır."

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "Raftın dışbükey olmasına neden olan raftın üst kısmındaki iç köşeleri çıkarır."

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "Radyenin iç köşelerini kaldırır ve radyenin dışbükey olmasına yol açar."

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "Her katmandaki boşlukları ortadan kaldırır ve sadece dış şekli korur. Görünmez tüm iç geometriyi yok sayar. Bununla birlikte, üstten ve alttan görünebilen katman boşluklarını da göz ardı eder."

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "Üst/alt şeklin en dıştaki parçasını eş merkezli hatlar ile değiştirir. Bir veya iki hat kullanmak, dolgu malzemesinde başlayan tavanları geliştirir."

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "Belirlenen eşiklerin dışına çıkan etkinliklerin raporlanması"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "Yerleştirme Tercihi"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "Dış Duvardan Önce Geri Çek"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "Katman Değişimindeki Geri Çekme"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Nozül yazdırılamayan alana doğru hareket ettiğinde filamanı geri çeker."

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "Nozül yazdırılamayan alana doğru hareket ettiğinde filamanı geri çeker."

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "Nozül bir sonraki katmana doğru hareket ettiğinde filamanı geri çekin."

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "Geri Çekme Mesafesi"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "Geri Çekme Sırasındaki İlave Astar Miktarı"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "Minimum Geri Çekme Hareketi"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "Geri Çekme Sırasındaki Astar Hızı"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "Geri Çekme Sırasındaki Çekim Hızı"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "Geri Çekme Hızı"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "Sağ"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "Fan Hızını 0 - 1 Arasında Ölçeklendir"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "Fan hızını 0 - 256 arasında değil 0 - 1 arasında ölçeklendirin."

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "Ölçekleme Faktörü Büzülme Telafisi"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "Sahnede Destek Örgüsü Var"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "Dikiş Köşesi Tercihi"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "Baskı Sırasını Manuel Olarak Ayarla"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "Cereyan kalkanının yüksekliğini ayarlayın. Cereyan kalkanını model yüksekliğinde veya sınırlı yükseklikte yazdırmayı seçin."

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "Çoklu ekstruderler ile yapılan yazdırmalar için kullanılan ayarlar."

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "Sadece Cura ön ucundan CuraEngine istenmediğinde kullanılan ayarlar."

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "Paylaşılan Nozül İlk Geri Çekme"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "En Keskin Köşe"

msgctxt "shell description"
msgid "Shell"
msgstr "Kovan"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "En kısa"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "Makine Varyantlarını Göster"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "Kaplamanın Kenar Desteği Katmanları"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "Kaplamanın Kenar Desteği Kalınlığı"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "Yüzey Genişleme Mesafesi"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "Yüzey Çakışması"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "Yüzey Çakışma Oranı"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "Yüzey Kaldırma Genişliği"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "Bu değerden daha dar olan yüzey alanları genişletilmez. Böylece model yüzeyinin dikeye yakın bir eğime sahip olduğu durumlarda ortaya çıkan dar yüzey alanlarının genişletilmesi önlenmiş olur."

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "Destek yapısının daha kolay kırılması için her N bağlantı hattında bir zikzak atlayın."

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "Destek yapısının daha kolay kırılması için bazı destek hattı bağlantılarını atlayın. Bu ayar, Zikzak destek dolgusu şekli için geçerlidir."

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Etek"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Etek Mesafesi"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "Etek Yüksekliği"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Etek Hattı Sayısı"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Etek/Kenar İvmesi"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Etek/Kenar Ekstrüderi"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "Etek/Kenar Akışı"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Etek/Kenar İvmesi Değişimi"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "Etek/Kenar Hattı Genişliği"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Minimum Etek/Kenar Uzunluğu"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Etek/Kenar Hızı"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "Dilimleme Toleransı"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "Küçük Özellik İlk Katman Hızı"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "Maksimum Küçük Özellik Uzunluğu"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "Küçük Özellik Hızı"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "Maksimum Küçük Delik Boyutu"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "Küçük Katman Yazdırma Sıcaklığı"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "Yüzeyde Küçük Üst/Alt"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "Küçük Üst/​Alt Genişlik"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "İlk katman üzerindeki küçük özellikler normal baskı hızının bu yüzdesinde basılacaktır. Daha yavaş baskı, yapışma ve doğruluğu artırmaya yardımcı olabilir."

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "Küçük özellikler normal baskı hızının bu yüzdesinde basılacaktır. Daha yavaş baskı, yapışma ve doğruluğu artırmaya yardımcı olabilir."

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "Küçük üst/alt bölgeler, varsayılan üst/alt deseni yerine duvarlarla doldurulur. Bu, sarsıntılı hareketleri önlemeye yardımcı olur. Varsayılan ayarda, en üstteki (havaya maruz kalan) katman için kapalıdır (bkz. 'Yüzeyde Küçük Üst/Alt')."

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "Akıllı Kenar"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "Akıllı Gizleme"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "Helezon Şeklinde Düzeltme"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "Z dikişinin görünürlüğünü azaltmak için helezon şeklindeki konturları düzeltin (Z dikişi baskıda zor görünmeli ancak katman görünümünde görünür olmalıdır). Düzeltme işleminin ince yüzey detaylarında bulanıklığa neden olabileceğini göz önünde bulundurun."

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "Hareket sırasında bazı malzemeler eksilebilir, bu malzemeler burada telafi edebilir."

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "Sürme hareketi sırasında bazı malzemeler eksilebilir; bu malzemeler burada telafi edebilir."

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "Özel Modlar"

msgctxt "speed description"
msgid "Speed"
msgstr "Hız"

msgctxt "speed label"
msgid "Speed"
msgstr "Hız"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "Sıçrama sırasında z eksenini hareket ettirmek için gerekli hız."

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "Spiral Dış Çevre"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "Dış kenarın Z hareketini helezon şeklinde düzeltir. Böylece yazdırmanın tamamında sabit bir Z artışı oluşur. Bu özellik katı bir modeli, tabanı katı tek bir duvar yazdırmasına dönüştürür. Bu özelliğin sadece tek bir parça içeren tüm tabakalarda etkinleştirilmesi gerekir."

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "Bekleme Sıcaklığı"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "G-code’u Başlat"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "Bir katmandaki her yolun başlangıç noktası. Ardışık katmanlardaki yollar aynı noktadan başladığında, çıktıda dikey bir ek yeri görünebilir. Bunları kullanıcının belirlediği bir konumun yakınına hizalarken ek yerinin kaldırılması kolaylaşır. Gelişigüzel yerleştirildiğinde yolların başlangıcındaki düzensizlikler daha az fark edilecektir. En kısa yol kullanıldığında yazdırma hızlanacaktır."

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "Milimetre Başına Adım (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "Milimetre Başına Adım (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "Milimetre Başına Adım (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "Milimetre Başına Adım (Z)"

msgctxt "support description"
msgid "Support"
msgstr "Destek"

msgctxt "support label"
msgid "Support"
msgstr "Destek"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "Destek İvmesi"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "Destek Alt Mesafesi"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "Destek Alt Duvar Hattı Sayısı"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "Destek Kenar Hattı Sayısı"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "Destek Kenar Genişliği"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "Destek Parçası Hattı Sayısı"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "Destek Parçasının Boyutu"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "Destek Yoğunluğu"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "Destek Mesafesi Önceliği"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "Destek Ekstruderi"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "Destek Zemini İvmesi"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "Destek Zemini Yoğunluğu"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "Destek Zemini Ekstrüderi"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "Destek Zemin Akışı"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "Destek Zemini Yatay Büyüme"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "Destek Zemini Sarsıntısı"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "Destek Zemin Hattı Yönleri"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "Destek Zemini Çizgi Mesafesi"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "Destek Zemini Çizgi Genişliği"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "Destek Zemini Deseni"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "Destek Zemini Hızı"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "Destek Zemini Kalınlığı"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "Destek Akışı"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "Destek Yatay Büyüme"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "Destek Dolgusu İvmesi"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "Destek Dolgu Ekstruderi"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "Destek Dolgu İvmesi Değişimi"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "Destek Dolgusu Katmanı Kalınlığı"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "Destek Dolgu Hattı Yönü"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "Destek Dolgu Hızı"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "Destek Arayüzü İvmesi"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "Destek Arayüzü Yoğunluğu"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "Destek Arayüz Ekstruderi"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "Destek Ara Yüzeyi Akışı"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "Destek Arayüzü Yatay Büyüme"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "Destek Arayüz Salınımı"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "Destek Arabirim Hattı Yönleri"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "Destek Arayüz Hattı Genişliği"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "Destek Arayüzü Şekli"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "Destek Arayüzü Önceliği"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "Destek Arayüzü Hızı"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "Destek Arayüzü Kalınlığı"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "Destek Arayüzü Duvar Hattı Sayısı"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "Destek Salınımı"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "Destek Birleşme Mesafesi"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "Destek Hattı Mesafesi"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "Destek Hattı Genişliği"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "Destek Örgüsü"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "Destek Çıkıntı Açısı"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "Destek Şekli"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "Destek Yerleştirme"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "Destek Çatısı İvmesi"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "Destek Çatısı Yoğunluğu"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "Destek Çatısı Ekstrüderi"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "Destek Çatı Akışı"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "Destek Çatısı Yatay Büyüme"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "Destek Çatısı Sarsıntısı"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "Destek Çatı Hattı Yönleri"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "Destek Çatısı Çizgi Mesafesi"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "Destek Çatısı Çizgi Genişliği"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "Destek Çatısı Deseni"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "Destek Çatısı Hızı"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "Destek Tavanı Kalınlığı"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "Destek Çatı Duvar Hattı Sayısı"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "Destek Hızı"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "Destek Merdiveni Basamak Yüksekliği"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "Destek Merdiveni Maksimum Basamak Genişliği"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "Basamak Desteğinin Minimum Eğim Açısı"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "Destek Yapısı"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "Destek Üst Mesafesi"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "Duvar Hattı Sayısını Destekle"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "Destek X/Y Mesafesi"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "Destek Z Mesafesi"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "Tercih edilen destek hatları"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "Tercih edilen destek"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "Desteklenen Yüzey Fan Hızı"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "Yüzey"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "Yüzey Enerjisi"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "Yüzey Modu"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "Yüzeye yapışma eğilimi."

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "Yüzey enerjisi."

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "En içteki ve ikinci en içteki kenar çizgilerinin baskı sırasını değiştirin. Bu, kenarın çıkarılmasını kolaylaştırır."

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "Çakışan bileşimlerin birbirine karışması için her bir katmanda bileşim kesişimi hacimlerine göre değişiklik yapın. Bu ayarın kapatılması, bir bileşimin diğer bileşimlerden ayrılarak çakışmadaki tüm hacmi almasına neden olur."

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "İki bitişik katman arasındaki hedef yatay mesafe. Bu ayarın azaltılması, katmanların kenarlarını birbirine yakınlaştırmak için daha ince katmanlar kullanılmasına neden olur."

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "Her bir katmanın yazdırılmaya başlanacağı bölgeye yakın konumun X koordinatı."

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "Bir katmandaki her kısmın yazdırılmaya başlanacağı yere yakın konumun X koordinatı."

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "Nozül yazdırma işlemini başlatmaya hazırlandığında konumun X koordinatı."

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "Her bir katmanın yazdırılmaya başlanacağı bölgeye yakın konumun Y koordinatı."

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "Bir katmandaki her kısmın yazdırılmaya başlanacağı yere yakın konumun Y koordinatı."

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "Nozül yazdırma işlemini başlatmaya hazırlandığında konumun Y koordinatı."

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "Nozül yazdırma işlemini başlatmaya hazırlandığında konumun Z koordinatı."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "İlk katmanın yazdırıldığı ivme."

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "İlk katman için belirlenen ivme."

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "İlk katmandaki hareket hamlelerinin ivmesi."

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "İlk katmandaki hareket hamlelerinin ivmesi."

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "İç duvarların yazdırıldığı ivme."

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "Dolgunun yazdırıldığı ivme."

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "Ütülemenin gerçekleştiği ivme."

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "Yazdırmanın gerçekleştiği ivme."

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "Taban radye katmanının yazdırıldığı ivme."

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "Destek zemininin yazdırıldığı ivme. Daha düşük ivmelerle yazdırma, desteğin modelin üzerine yapışmasını iyileştirebilir."

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "Destek dolgusunun yazdırıldığı ivme."

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "Orta radye katmanının yazdırıldığı ivme."

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "En dış duvarların yazdırıldığı ivme."

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "İlk direğin yazdırıldığı ivme."

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "Radyenin yazdırıldığı ivme."

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Destek çatıları ve zeminlerinin yazdırıldığı ivme. Daha düşük ivmelerle yazdırma, askıda kalan kısımların kalitesini iyileştirebilir."

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "Destek çatısının yazdırıldığı ivme. Daha düşük ivmelerle yazdırma, askıda kalan kısımların kalitesini iyileştirebilir."

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "Etek ve kenarın yazdırıldığı ivme. Bu işlem normalde ilk katman ivmesi ile yapılır, ama etek ve kenarı farklı bir ivmede yazdırmak isteyebilirsiniz."

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "Destek yapısının yazdırıldığı ivme."

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "Üst radye katmanların yazdırıldığı ivme."

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "Üst yüzey iç duvarlarının hangi hızla basıldığı."

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "Üst Yüzeyin En Dış Duvarlarının Hangi Hızda Basıldığı."

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "Duvarların yazdırıldığı ivme."

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "Üst yüzey katmanların yazdırıldığı ivme."

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "Üst/alt katmanların yazdırıldığı ivme."

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "Hareket hamlelerinin ivmesi."

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "Ütüleme sırasında normal yüzey hattına göre ekstrude edilecek malzeme miktarı. Nozülü dolu tutmak üst yüzeyde oluşan çatlakların bir kısmının doldurulmasını sağlar fakat nozülün fazla dolu olması aşırı ekstrüzyona ve yüzey yanlarında noktalar oluşmasına neden olur."

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu hattı genişliğinin yüzdesi olarak dolgu ve duvarların arasındaki çakışma miktarı. Ufak bir çakışma duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu ve duvarlar arasındaki çakışma miktarı. Hafif bir çakışma duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "Ekstrüderler değiştirilirken oluşan geri çekme miktarı. Geri çekme yoksa 0 olarak ayarlayın. Bu genellikle ısı bölgesinin uzunluğuna eşittir."

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "Yatay düzlem ve nozül ucunun sağ üzerinde bulunan konik parça arasındaki açı."

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "Direk tavanı açısı Yüksek bir değer, direk tavanını sivrileştirirken, daha düşük bir değer direk tavanlarını düzleştirir."

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "Kalıp için oluşturulan dış duvarların çıkıntı açısı. 0° kalıbın dış kovanını dikey hale getirirken, 90° ise modelin dış kısmının model konturunu takip etmesini sağlayacaktır."

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "Alta doğru gidildikçe kademeli olarak kalınlaşan dalların açısı. 0 derecelik bir açı dalların uzunluklarını gözetmeksizin tekdüze bir kalınlığa sahip olmalarını sağlayacaktır. Birazcık açı ağaç desteğin sabitliğini artırabilir."

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "Konik desteğin eğim açısı. Dikey 0 derece ve yatay 90 derece. Daha küçük açılar desteğin daha sağlam olmasını sağlar, ancak çok fazla malzeme içerir. Negatif açılar destek tabanının üst kısımdan daha geniş olmasına yol açar."

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "Bir katmandaki her bir poligona tanınan noktaların ortalama yoğunluğu. Poligonların asıl noktalarının çıkarıldığını dikkate alın; bunun sonucunda düşük yoğunluk sonuçları çözünürlük azalmasıyla sonuçlanabilir."

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "Her bir hat dilimine tanıtılan rastgele noktalar arasındaki ortalama mesafe. Poligonların asıl noktalarının çıkarıldığını dikkate alın; bunun sonucunda yüksek pürüzsüzlük sonuçları çözünürlük azalmasıyla sonuçlanabilir. Bu değer, Belirsiz Dış Katman Kalınlığından yüksek olmalıdır."

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "Kullanılan malzemenin markası."

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "Yazıcı başlığı hareketinin varsayılan ivmesi."

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "Yazdırma için kullanılan varsayılan sıcaklık. Bu sıcaklık malzemenin “temel” sıcaklığı olmalıdır. Diğer tüm yazıcı sıcaklıkları bu değere dayanan ofsetler kullanmalıdır"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "Isınan yapı levhası için kullanılan varsayılan sıcaklık. Bu sıcaklık yapı levhasının “temel” sıcaklığı olmalıdır. Diğer tüm yazıcı sıcaklıkları bu değere dayanan ofsetler kullanmalıdır"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Köprü yüzey alanı katmanının yoğunluğu. 100’den az değerler, yüzey alanı çizgileri arasındaki boşlukları artıracaktır."

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "Destek yapısı zeminlerinin yoğunluğu. Daha yüksek bir değer, desteğin modelin üzerine daha iyi yapışmasını sağlar."

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "Destek yapısı çatılarının yoğunluğu. Daha yüksek bir değer daha iyi çıkıntılar ortaya çıkarırken, desteklerin kaldırılmasını zorlaştırır."

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "İkinci köprü yüzey alanı katmanının yoğunluğu. 100’den az değerler, yüzey alanı çizgileri arasındaki boşlukları artıracaktır."

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "Üçüncü köprü yüzey alanı katmanının yoğunluğu. 100’den az değerler, yüzey alanı çizgileri arasındaki boşlukları artıracaktır."

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "Yazdırılabilir alan derinliği (Y yönü)."

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "Özel bir direğin çapı."

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "Ağaç desteğin en ince dallarının çapı. Daha kalın dallar daha dayanıklı olur. Tabana doğru uzanan dallar bundan daha kalın olacaktır."

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "Ağaç desteğinin dallarının ucunun en üstteki çapı."

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "Besleyiciye malzeme veren çarkın çapı."

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "Ağaç desteğinin en geniş dallarının çapı. Daha kalın bir gövde daha sağlamdır; daha ince bir gövde, yapı plakasında daha az yer kaplar."

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "Bir önceki ve bir sonraki katman yüksekliği arasındaki yükseklik farkı."

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "Ütüleme hatları arasında bulunan mesafe."

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "Hareket esnasında atlama yaparken nozül ve daha önce yazdırılmış olan bölümler arasındaki mesafe."

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Radyenin taban katmanı için radye hatları arasındaki mesafe. Geniş aralık bırakılması radyenin yapı levhasından kolayca kaldırılmasını sağlar."

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "Radyenin orta katmanı için radye hatları arasındaki mesafe. Ortadaki aralığın oldukça geniş olması gerekirken, üst radye katmanlarını desteklemek için de yeteri kadar yoğun olması gerekir."

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "Üst radye katmanları için radye hatları arasındaki mesafe. Yüzeyin katı olabilmesi için aralık hat genişliğine eşit olmalıdır."

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "Tek başlangıç kulesi sal katmanı için sal izleri arasındaki mesafe. Geniş aralıklar, salın yapıştırma tablasından kolay çıkarılmasını sağlar."

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "İç içe geçen yapı oluşturmak için modeller arası sınırdan hücre sayısı olarak ölçülen mesafe. Çok az hücre kullanmak zayıf yapışmaya neden olur."

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Modelin en dış kenar hattını olan mesafesi. Daha büyük kenar hattı yapı levhasına yapışmayı artırmanın yanı sıra etkin yazdırma alanını da azaltır."

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "İç içe geçen yapıların oluşturulmayacağı bir modelin dışından hücre cinsinden ölçülen mesafe."

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "Nozülden gelen ısının filamana aktarıldığı nozül ucuna olan mesafe."

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "Alt yüzeylerin dolgunun içine doğru genişleyeceği mesafedir. Daha yüksek değerler, yüzeyin dolgu şekline daha iyi tutunmasını sağladığı gibi yüzeyin aşağıdaki katmandaki duvara daha iyi yapışmasını sağlar. Daha düşük değerler, kullanılan malzemenin miktarından tasarruf yapılmasını sağlar."

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "Yüzeylerin dolgunun içine doğru genişleyeceği mesafedir. Daha yüksek değerler, yüzeyin dolgu şekline daha iyi tutunmasını sağladığı gibi komşu katmanlardaki duvarların yüzeye daha iyi yapışmasını sağlar. Daha düşük değerler, kullanılan malzemenin miktarından tasarruf yapılmasını sağlar."

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "Üst yüzeylerin dolgunun içine doğru genişleyeceği mesafedir. Daha yüksek değerler, yüzeyin dolgu şekline daha iyi tutunmasını sağladığı gibi yukarıdaki katmandaki duvarların yüzeye daha iyi yapışmasını sağlar. Daha düşük değerler, kullanılan malzemenin miktarından tasarruf yapılmasını sağlar."

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "Başlığı fırçada ileri ve geri hareket ettirme mesafesi."

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "Malzemeden tasarruf etmek için dolgu hatlarının uç noktaları kısaltılır. Bu ayar, bu hatların uç noktalarının çıkıntı açısıdır."

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "Ekstrüzyon sırasında nozülün soğuduğu ilave hız. Aynı değer, ekstrüzyon sırasında ısınırken kaybedilen ısınma hızını göstermek için de kullanılır."

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "Destek dolgusunun ilk katmanı için kullanılacak ekstruder Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "Radyenin ilk katmanının baskısında kullanılacak ekstrüderdir. Çoklu ekstrüzyonlarda kullanılır."

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "Desteğin zemininin yazdırılması için kullanılacak ekstrüder dizisi. Çoklu ekstrüzyon sırasında kullanılır."

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "Destek dolgusu için kullanılacak ekstruder Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "Radyenin orta katmanının baskısında kullanılacak ekstrüderdir. Çoklu ekstrüzyonlarda kullanılır."

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "Desteğin çatıları ve zeminlerinin yazdırılması için kullanılacak ekstrüder dizisi. Çoklu ekstrüzyon sırasında kullanılır."

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "Desteğin çatısının yazdırılması için kullanılacak ekstrüder dizisi. Çoklu ekstrüzyon sırasında kullanılır."

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "Etek veya kenar baskısı için kullanılacak ekstrüderdir. Çoklu ekstrüzyonlarda kullanılır."

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "Etek/kenar/radye yazdırmak için kullanılacak ekstruder Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "Destek için kullanılacak ekstruder Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "Radyenin üst katmanlarının baskısında kullanılacak ekstrüderdir. Çoklu ekstrüzyonlarda kullanılır."

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "Dolgu yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "İç duvarları yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "Dış Duvarı yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "Üst ve alt yüzeyi yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "En üstteki yüzeyi yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "Duvarları yazdırmak için kullanılan ekstruder dişli çarkı. Çoklu ekstrüzyon işlemi için kullanılır."

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "Radyenin taban katmanı için fan hızı."

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "Radyenin orta katmanı için fan hızı."

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Radye için fan hızı."

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "Üst radye katmanları için fan hızı."

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "Parlaklık değerlerinin, yazdırma dolgusunun ilgili konumundaki minimum yoğunluğu belirlediği görüntünün dosya konumu."

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "Parlaklık değerlerinin, desteğin ilgili konumundaki minimum yoğunluğu belirlediği görüntünün dosya konumu."

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "Yapı levhasına daha iyi yapışma sağlamak ve yazdırmanın genel başarı oranını artırmak için ilk birkaç katman modelin kalan kısmından daha yavaş yazdırılır. Bu hız katmanlar üzerinde giderek artar."

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "Son radye katmanı ve modelin ilk katmanı arasındaki boşluk. Radye katmanı ve model arasındaki yapışmayı azaltmak için sadece ilk katman yükseltilir. Radyeyi sıyırmayı kolaylaştırır."

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "Yazdırılabilir alan yüksekliği (Z yönü)."

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "Kalıp yazdıracak modelinizin yatay kısımlarının üzerindeki yükseklik."

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Fanların olağan fan hızında döndüğü yükseklik. Alttaki katmanlarda fan hızı, İlk Fan Hızından Olağan Fan Hızına kadar kademeli olarak artar."

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "Nozül ucu ve portal sistemi (X ve Y aksları) arasındaki yükseklik farkı."

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "Nozül ucu ve yazıcı başlığının en alt parçası arasındaki yükseklik farkı."

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "Ekstruder değişiminden sonra Z Sıçraması yapılırken oluşan yükseklik farkı."

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "Z Sıçraması yapılırken oluşan yükseklik farkı."

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "Z Sıçraması yapılırken oluşan yükseklik farkı."

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "Her katmanın milimetre cinsinden yüksekliği. Daha yüksek değerler düşük çözünürlükte hızlı baskılar üretir; daha düşük değerler ise yüksek çözünürlükte daha yavaş baskılar üretir."

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "Yoğunluğun yarısına inmeden önce verilen bir yoğunluktaki dolgunun yüksekliği."

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "Yoğunluğun yarısına inmeden önce belirli bir yoğunluktaki destek dolgusunun yüksekliği."

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "İç içe geçen yapı kirişlerinin katman sayısı olarak ölçülen yüksekliği. Daha az katman daha güçlüdür, ama kusurlara daha yatkındır."

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "İç içe geçen yapı kirişlerinin katman sayısı olarak ölçülen yüksekliği. Daha az katman daha güçlüdür, ama kusurlara daha yatkındır."

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "İlk katmanın milimetre cinsinden yüksekliği. Kalın ilk katmanlar yapı levhasına yapışmayı kolaylaştırır."

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "Prime tower tabanının yüksekliği. Bu değeri artırmak, taban daha geniş olacağı için daha sağlam bir prime tower oluşturur. Eğer bu ayar çok düşükse, prime tower sağlam bir tabana sahip olmayacaktır."

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "Modelin üzerinde sabit duran desteğin merdiven benzeri alt kısmının basamak yüksekliği. Daha düşük bir değer desteğin hareket ettirilmesini zorlaştırırken, daha yüksek bir değer kararsız destek yapılarına yol açabilir. Merdiven benzeri davranışı kapatmak için sıfır değerine ayarlayın."

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "Baskının ilk katmanının uçtaki ilk hattı ile ana hattı arasındaki yatay mesafe. Küçük bir boşluk baskının uç kısmının kolayca çıkarılmasını sağlamasının yanı sıra ısı bakımından da avantajlıdır."

msgctxt "skirt_gap description"
msgid "The horizontal distance between the skirt and the first layer of the print.\nThis is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr "Baskının eteği ve ilk katmanı arasındaki yatay mesafe."
"Minimum mesafedir. Bu mesafeden çok sayıda etek hattı dışarı doğru uzanır."

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "Dolgu hatları, baskı süresinden tasarruf etmek için düzleştirilir. Bu, dolgu hattının uzunluğu boyunca izin verilen maksimum çıkıntı açısıdır."

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "Dolgu şekli X ekseni boyunca bu mesafe kadar kaydırılır."

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "Dolgu şekli Y ekseni boyunca bu mesafe kadar kaydırılır."

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "Nozül iç çapı. Standart olmayan nozül boyutu kullanırken bu ayarı değiştirin."

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "Taban radye katmanının yazdırıldığı ivmesi değişimi."

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "Orta radye katmanının yazdırıldığı salınım."

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "Radyenin yazdırıldığı salınım."

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "Üst radye katmanların yazdırıldığı salınım."

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "Kaldırılacak olan alt yüzey alanlarının en büyük genişliğidir. Bu değerden daha küçük olan her yüzey alanı kaybolacaktır. Bu, modeldeki eğimli yüzeylerde alt yüzeyin yazdırılması için harcanan süreyi ve malzemeyi sınırlamaya yardımcı olabilir."

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "Kaldırılacak olan yüzey alanlarının en büyük genişliğidir. Bu değerden daha küçük olan her yüzey alanı kaybolacaktır. Bu, modeldeki eğimli yüzeylerde alt/üst yüzeyin yazdırılması için harcanan süreyi ve malzemeyi sınırlamaya yardımcı olabilir."

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "Kaldırılacak olan üst yüzey alanlarının en büyük genişliğidir. Bu değerden daha küçük olan her yüzey alanı kaybolacaktır. Bu, modeldeki eğimli yüzeylerde üst yüzeyin yazdırılması için harcanan süreyi ve malzemeyi sınırlamaya yardımcı olabilir."

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "Fanların olağan fan hızında döndüğü katman Yüksekteki olağan fan hızı ayarlanırsa bu değer hesaplanır ve tam sayıya yuvarlanır."

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "Sınır değerini olağan ve maksimum fan hızı arasında ayarlayan katman süresi. Bundan daha kısa sürede yazdıran katmanlar olağan fan hızı kullanır. Daha hızlı katmanlar için, fan hızı maksimum fan hızına doğru kademeli olarak artar."

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "Geri çekme hareketi sırasında geri çekilen malzemenin uzunluğu."

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "Prime tower tabanının eğiminde kullanılan büyüklük faktörü. Bu değeri artırırsanız, taban daha ince hale gelir. Azaltırsanız, taban daha kalın olur."

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "Yazıcıya takılı yapı levhasının malzemesi."

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "Taban katmanı yüksekliğine göre izin verilen azami yükseklik."

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "Sızdırma kalkanında bir bölümün sahip olacağı en büyük açı. Dikey 0 derece ve yatay 90 derece. Daha küçük bir açı sızdırma kalkanının daha sorunsuz olmasını sağlarken daha fazla malzeme kullanılmasına yol açar."

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "Yazdırılabilir yapıldıktan sonra çıkıntıların en büyük açısı. 0° değerindeyken tüm modeller yapı levhasına bağlı bir model parçasıyla değiştirilirken 90° modeli hiçbir şekilde değiştirmez."

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "Dalların modelin etrafında büyürken aldıkları maksimum açı. Daha dikey ve daha dengeli hale getirmek için daha düşük bir açı kullanın. Daha fazla erişim için daha yüksek bir açı kullanın."

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "Çıkıntıyı Yazdırılabilir Yap işlemiyle çıkarılmadan önce modelin tabanındaki deliğin maksimum alanı.  Bu değerden küçük delikler korunacaktır.  0 mm²'lik değer modellerin tabanındaki tüm delikleri dolduracaktır."

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "Maksimum Çözünürlük ayarı için çözünürlük azaltıldığında izin verilen maksimum sapma. Bu değeri artırırsanız baskının doğruluğu azalacak ancak g kodu daha küçük olacaktır. Maksimum Sapma, Maksimum Çözünürlük için sınırdır, dolayısıyla iki değer çelişirse Maksimum Sapma her zaman doğru kabul edilir."

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "X/Y yönlerinde destek yapıları arasındaki maksimum mesafedir. Ayrı yapılar birbirlerine bu değerden daha yakınsa yapılar birleşerek tek bir yapı haline gelir."

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "Akış hızındaki değişiklikleri telafi etmek için filamentin hareket ettirileceği mm cinsinden maksimum mesafe."

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "Ara noktaları düz bir hattan çıkarırken izin verilen maksimum ekstrüzyon alanı sapmasıdır. Bir ara nokta, uzun düz bir hatta genişlik değiştiren nokta olarak hizmet edebilir. Bu nedenle, ara noktanın çıkarılması hattın tek boyutlu bir genişliğe sahip olmasına ve dolayısıyla bir miktar ekstrüzyon alanı kaybetmesine (veya kazanmasına) neden olur. Bu değeri artırırsanız daha fazla ara genişlik değiştiren noktaların kaldırılmasına izin verileceğinden, düz paralel duvarlar arasında az (veya çok) ekstrüzyon görebilirsiniz. Baskının doğruluğu azalacak fakat g kodu daha küçük olacaktır."

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "İlk katmanın yazdırıldığı maksimum anlık yazdırma hızı değişimi."

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "Yazıcı başlığının maksimum anlık hız değişimi."

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "Ütüleme sırasında oluşan maksimum anlık hız değişimi."

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "Tüm iç duvarların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "Dolgunun yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "Desteğin zeminlerinin yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "Desteğin dolgusunun yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "En dıştaki duvarların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "İlk direğin yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "Desteğin çatıları ve zeminlerinin yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "Desteğin çatılarının yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "Etek ve kenarların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "Destek yapısının yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "Üst Yüzeyin En Dış Duvarlarının Basıldığı Anki Maksimum Hız Değişikliği."

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "Üst Yüzeyin İç Duvarlarının Basıldığı Anki Maksimum Hız Değişikliği."

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "Duvarların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "Üst yüzey katmanların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "Üst/alt katmanların yazdırıldığı maksimum anlık hız değişimi."

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "Hareket hamlelerinin yapıldığı maksimum anlık hız değişimi."

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "Havadan basılabilecek dalların maksimum uzunluğu."

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "X yönü motoru için maksimum hız."

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Y yönü motoru için maksimum hız."

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Z yönü motoru için maksimum hız."

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "Filamanın maksimum hızı."

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "Modelin üzerinde sabit duran desteğin merdiven benzeri alt kısmının maksimum basamak genişliği. Daha düşük bir değer desteğin hareket ettirilmesini zorlaştırırken, daha yüksek bir değer kararsız destek yapılarına yol açabilir."

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "Kalıbın dış tarafı ile modelin dış tarafı arasındaki minimum mesafedir."

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "Yazıcı başlığının minimum hareket hızı."

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "Yazdırmanın başlayacağı Yazdırma Sıcaklığına ulaşırken görülen minimum sıcaklık."

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "Nozül soğumadan önce ekstruderin etkin olmaması gerektiği minimum süre. Ekstruder sadece bu süreden daha uzun bir süre kullanılmadığında bekleme sıcaklığına inebilecektir."

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "Dolum eklenen dahili çıkıntıların minimum açısı. 0° değerde nesneler tamamen doldurulur, 90°’de dolgu yapılmaz."

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "Desteğin eklendiği çıkıntıların minimum açısı. 0°’de tüm çıkıntılar desteklenirken 90°‘de destek sağlanmaz."

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "Geri çekme işleminin yapılması için gerekli olan minimum hareket mesafesi. Küçük bir alanda daha az geri çekme işlemi yapılmasına yardımcı olur."

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "Etek veya kenarın minimum uzunluğu. Tüm etek veya kenar hatları birlikte bu uzunluğa ulaşmazsa minimum uzunluğa ulaşılana kadar daha fazla etek veya kenar hattı eklenecektir. Not: Hat sayısı 0’a ayarlanırsa, bu yok sayılır."

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "Orta hat boşluğunu dolduran çok hatlı duvarlar için minimum hat genişliğidir. Bu ayar, iki duvar hattı baskısının hangi model kalınlığında iki dış duvar ve tek bir merkezi orta duvar baskısına geçirileceğini belirler. Daha yüksek Minimum Tek Duvar Hattı Genişliği değeri belirlenmesi daha yüksek maksimum çift duvar hattı genişliği oluşturur. Maksimum tek duvar hattı genişliği, 2 * Minimum Çift Duvar Hattı Genişliği formülüyle hesaplanır."

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "Normal çokgen duvarlar için minimum hat genişliğidir. Bu ayar, tek bir ince duvar hattının basılmasından iki duvar hattına hangi model kalınlığında geçileceğini belirler. Daha yüksek Minimum Çift Duvar Hattı Genişliği değeri belirlenmesi daha yüksek maksimum tek duvar hattı genişliği oluşmasına yol açar. Maksimum çift duvar hattı genişliği, Dış Duvar Hattı Genişliği + 0,5 * Minimum Tek Duvar Hattı Genişliği formülüyle hesaplanır."

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "Düşük katman süresi nedeniyle yavaşlamaya karşın minimum yazdırma hızı. Yazıcı çok yavaşladığında nozüldeki basınç çok düşük olacak ve kötü yazdırma kalitesiyle sonuçlanacaktır."

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "Bir çizginin dilimlemeden sonraki minimum boyutu. Bu değer artırıldıktan sonra örgünün çözünürlüğü düşer. Bu, yazıcının g-kodunu işlemek için gereken hıza yetişmesine olanak tanır ve örtünün zaten işlenemeyecek ayrıntılarını kaldırarak dilimleme hızını artırır."

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "Bir hareket çizgisinin dilimlemeden sonraki minimum boyutu. Bunu artırmanız durumunda, hareketlerde köşelerin yumuşaklığı azalır. Bu seçenek, yazıcının g-code işlemek için gereken hızı yakalamasına olanak tanıyabilir ancak model kaçınmasının doğruluğunu azaltabilir."

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "Basamaklı alanın etkili olması için gereken minimum eğimdir. Düşük değerler, derinliği daha düşük olan eğimlerde desteğin kaldırılmasını kolaylaştırırken, gerçekten düşük değerler ise modelin diğer parçalarında tersine sonuçlar doğurabilir."

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Bir katmanda harcanan minimum süre. Bu süre yazıcıyı yavaşlamaya ve burada en azından bir katmanda ayarlanan süreyi kullanmaya zorlar. Bir sonraki katman yazdırılmadan önce yazdırılan materyalin düzgün bir şekilde soğumasını sağlar. Kaldırma Başlığı devre dışı bırakılır ve Minimum Hız değeri başka bir şekilde ihlal edilmezse katmanlar yine de minimal katman süresinden daha kısa sürebilir."

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "Yeterince malzeme temizlemek için ilk direğin her bir katmanı için minimum hacim."

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "Modele bağlanması gereken dalın çapının, yapı levhasına erişebilecek dallarla birleşerek en fazla ne kadar artabileceği. Bunu artırmak baskı süresini azaltır, ancak modele dayanan destek alanını artırır"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "3B yazıcı modelinin adı."

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "Ekstruder dişli çarkı için nozül kimliği, “AA 0.4” ve “BB 0.8” gibi."

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "Nozül hareket esnasında daha önce yazdırılmış bölümleri atlar. Bu seçenek sadece tarama etkinleştirildiğinde kullanılabilir."

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "Nozül hareket ederken önceden yazdırılmış destekleri atlar. Bu seçenek yalnızca tarama etkin olduğunda kullanılabilir."

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Alt katman sayısı. Bu değer, alt kalınlığıyla hesaplandığında tam sayıya yuvarlanır."

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "Radyenin taban katmanındaki doğrusal desen etrafına basılacak kontur sayısıdır."

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "Raftın orta katmanlarındaki doğrusal desenin etrafına yazdırılacak kontur sayısı."

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "Raftın üst katmanlarındaki doğrusal desenin etrafına yazdırılacak kontur sayısı."

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "Raft doğrusal modelinin etrafına yazdırılacak konturların sayısı."

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "Kaplamanın kenarlarını destekleyen dolgu katmanının kalınlığı."

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "Yapı plakasından itibaren ilk alt katman sayısı Bu değer, alt kalınlığıyla hesaplandığında tam sayıya yuvarlanır."

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Radyenin tabanı ve yüzeyi arasındaki katman sayısıdır. Bunlar radyenin temel kalınlığını oluşturur. Bu değerin artırılması daha kalın ve sağlam bir radye oluşturur."

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "Bir kenar için kullanılan hatların sayısı Daha fazla kenar hattı yapı levhasına yapışmayı artırmanın yanı sıra etkin yazdırma alanını da azaltır."

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "Bir destek kenarı için kullanılan hatların sayısı. Daha fazla kenar hattı, ekstra malzeme karşılığında baskı tablasına daha fazla alanın yapışacağı anlamına gelir."

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "İkinci radye katmanındaki üst katmanların sayısı. Bunlar modelin üstünde durduğu tamamı dolgulu katmanlardır. İki katman bir katmandan daha pürüzsüz bir üst yüzey oluşturur."

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "Üst katman sayısı. Bu değer, üst kalınlığıyla hesaplandığında tam sayıya yuvarlanır."

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "En üstteki yüzey katmanlarının sayısı. Yüksek kalitede üst yüzeyler oluşturmak için genellikle tek bir üst yüzey katmanı yeterlidir."

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Destek dolgusunun çevreleneceği duvar sayısı. Bir duvarın eklenmesi destek yazdırmasını daha güvenilir kılabilir ve çıkıntıları daha iyi destekleyebilir. Ancak yazdırma süresini ve kullanılan malzemeyi artırır."

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Destek arayüz zeminini çevreleyecek duvar sayısı. Duvar eklemek, destek baskıyı daha güvenilir hale getirebilir ve çıkıntıları daha iyi destekleyebilir, ama baskı süresini ve kullanılan malzemeyi artırır."

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Destek arayüz çatısını çevreleyecek duvar sayısı. Duvar eklemek, destek baskıyı daha güvenilir hale getirebilir ve çıkıntıları daha iyi destekleyebilir, ama baskı süresini ve kullanılan malzemeyi artırır."

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "Destek arayüzünü çevreleyecek duvar sayısı. Duvar eklemek, destek baskıyı daha güvenilir hale getirebilir ve çıkıntıları daha iyi destekleyebilir, ama baskı süresini ve kullanılan malzemeyi artırır."

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "Varyasyonun yayılması gereken, merkezden itibaren sayılan duvar sayısı. Düşük değerler olması dış duvarların genişliğinin değişmeyeceğini gösterir."

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "Duvar sayısı. Bu değer, duvar kalınlığıyla hesaplandığında tam sayıya yuvarlanır."

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "Nozül ucunun dış çapı."

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "Baskının dolgu malzemesinin şeklidir. Hat ve zikzak dolgu, farklı katmanlar üzerinde yön değiştirerek malzeme maliyetini azaltır. Izgara, üçgen, üçlü altıgen, kübik, sekizlik, çeyrek kübik, çapraz ve eşmerkezli şekiller her katmana tam olarak basılır. Gyroid, kübik, çeyrek kübik ve sekizlik dolgu, her yönde daha eşit bir kuvvet dağılımı sağlamak için her katmanda değişir. Yıldırım dolgu, objenin yalnızca tavanını destekleyerek dolgu miktarını en aza indirmeye çalışır."

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "Yazdırma destek yapılarının şekli. Bulunan farklı seçenekler sağlam veya kolay çıkarılabilir destek oluşturabilir."

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "En üst yüzeyin şekli."

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "Üst/alt katmanların şekli."

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "Yazdırmanın altında ilk katmanda yer alacak şekil."

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "Üst yüzeyleri ütülemek için kullanılacak model."

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "Destek zeminlerinin yazdırıldığı desen."

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "Model ile birlikte destek arayüzünün yazdırıldığı şekil."

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "Destek çatısının yazdırıldığı desen."

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "Bir katmandaki her kısmın basılmaya başlanacağı yere yakın konum."

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "Modelden kaçınmak zorunda olmadıklarında dalların tercih edilen açısı. Daha dikey ve daha dengeli hale getirmek için daha düşük bir açı kullanın. Dalların daha hızlı birleşmesi için daha yüksek bir açı kullanın."

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "Destek yapılarının tercih edilen yerleşimi. Yapılar tercih edilen yere yerleştirilemiyorsa başka bir yere yerleştirilecektir. Bu, onları modelin üzerine yerleştirmek anlamına da gelebilir."

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "İlk katman için maksimum anlık yazdırma hızı değişimi."

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "Yazdırılamayan alanların haricinde yapı levhasının şekli."

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "Baskı kafasının şekli. Bunlar baskı kafasının konumuna göre koordinatlardır ve genellikle ilk ekstrüderin konumunu gösterir. Baskı kafasının sol ve önündeki boyutlar negatif koordinatlar olmalıdır."

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "Şeklin kendisine temas ettiği yüksekliklerde, çapraz 3D şekilde dört yönlü kesişme yerlerinde bulunan ceplerin boyutudur."

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "Taramaya izin verilmeden önce ekstrüzyon yolunda olması gereken en küçük hacim. Daha küçük ekstrüzyon yolları için bowden tüpünde daha az basınç geliştirilir ve bu nedenle taranan hacim doğrusal olarak ölçeklendirilir. Bu değer her zaman Tarama Değerinden daha büyüktür."

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Ortalama nozül soğumasının normal yazdırma sıcaklıkları ve bekleme sıcaklığı penceresinin üzerinde olduğu hız (°C/sn)."

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "Ortalama nozül ısınmasının normal yazdırma sıcaklıkları ve bekleme sıcaklığı penceresinin üzerinde olduğu hız (°C/sn)."

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "Tüm iç duvarların yazdırıldığı hız. İç duvarları dış duvarlardan daha hızlı yazdırmak yazdırma süresini azaltacaktır. Bu ayarı dış duvar hızı ve dolgu hızı arasında yapmak faydalı olacaktır."

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "Köprü yüzey alanı bölgelerinin yazdırıldığı hız."

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "Dolgunun gerçekleştiği hız."

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "Yazdırmanın gerçekleştiği hız."

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Radyenin taban katmanının yazdırıldığı hız. Nozülden gelen malzemenin hacmi çok büyük olduğu için bu kısım yavaş yazdırılmalıdır."

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "Köprü duvarlarının yazdırıldığı hız."

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "Fanların, yazdırma işleminin başındaki dönme hızı. Sonraki katmanlarda fan hızı, Yüksekteki Olağan Fan Hızına karşılık gelen katmana kadar kademeli olarak artar."

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "Katmanların sınıra ulaşmadan önceki dönüş hızı Katman sınır değerinden daha hızlı yazdırdığında fan hızı giderek maksimum fan hızına yönelir."

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "Katmanların minimum katman süresindeki dönüş hızı. Sınır değerine ulaşıldığında, fan hızı olağan ve maksimum fan hızı arasında kademeli artış gösterir."

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "Filamanın geri çekme hareketi sırasında astarlandığı hız."

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "Filamanın sürme geri çekme hareketi sırasında astarlandığı hız."

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "Nozül değişiminin çekmesi sonucunda filamanın geriye doğru itildiği hız."

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "Filamanın geri çekildiği ve geri çekme hareketi sırasında astarlandığı hız."

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "Filamanın geri çekildiği ve sürme geri çekme hareketi sırasında astarlandığı hız."

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "Nozül değişiminin çekmesi sırasında filamanın geri çekildiği hız."

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "Filamanın geri çekme hareketi sırasında geri çekildiği hız."

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "Filamanın sürme geri çekme hareketi sırasında geri çekildiği hız."

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "Filamanın geri çekildiği hız. Daha yüksek bir geri çekme hızı daha çok işe yarar, fakat çok yüksek geri çekme hızı filaman aşınmasına neden olabilir."

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "Destek zemininin yazdırılma hızı. Daha düşük hızlarda yazdırma, desteğin modelin üzerine yapışmasını iyileştirebilir."

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "Dolgu desteğinin yazdırıldığı hız. Dolguyu daha düşük hızlarda yazdırmak sağlamlığı artırır."

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "Orta radye katmanının yazdırıldığı hız. Nozülden gelen malzemenin hacmi çok büyük olduğu için bu kısım yavaş yazdırılmalıdır."

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "En dış duvarların yazdırıldığı hız. Dış duvarı düşük hızda yazdırmak son yüzey kalitesini artırır. Öte yandan, iç duvar hızı ve dış duvar hızı arasındaki farkın fazla olması kaliteyi olumsuz etkileyecektir."

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "İlk direğin yazdırıldığı hız. Farklı filamanlar arasındaki yapışma standardın altında olduğunda, ilk direği daha yavaş yazdırmak dayanıklılığı artırabilir."

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "Yazdırma soğutma fanlarının dönüş hızı."

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "Radyenin yazdırıldığı hız."

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Destek çatıları ve zeminlerinin yazdırılma hızı. Daha düşük hızlarda yazdırma, askıda kalan kısımların kalitesini iyileştirebilir."

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "Destek çatısının yazdırılma hızı. Daha düşük hızlarda yazdırma, askıda kalan kısımların kalitesini iyileştirebilir."

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "Etek ve kenarın yazdırıldığı hız. Bu işlem normalde ilk katman hızında yapılır, ama etek ve kenarı farklı hızlarda yazdırmak isteyebilirsiniz."

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "Destek yapısının yazdırıldığı hız. Yüksek hızlardaki yazdırma desteği yazdırma süresini büyük oranda azaltabilir. Destek yapısının yüzey kalitesi, yazdırma işleminden sonra çıkartıldığı için önemli değildir."

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "Radye katmanlarının yazdırıldığı hız. Nozülün bitişik yüzey hatlarını yavaşça düzeltebilmesi için, bu kısımlar biraz daha yavaş yazdırılmalıdır."

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "Üst Yüzey İç Duvarların Hangi Hızda Basıldığı."

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "Üst Yüzeyin En Dış Duvarlarının Hangi Hızda Basıldığı."

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Z Atlamaları için yapılan dikey Z hareketinin gerçekleştirileceği hızdır. Yapı plakasının veya makine tezgahının hareket etmesi daha zor olduğundan genelde baskı hızından daha düşüktür."

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "Duvarların yazdırıldığı hız."

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "Üst yüzeyi geçmek için gereken süre."

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "Sorunsuz kopması için filamentin geri çekilmesi gereken hızdır."

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "Üst yüzey katmanların yazdırıldığı hız."

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "Üst/alt katmanların yazdırıldığı hız."

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "Hareket hamlelerinin hızı."

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "Ekstrüzyon yolu hızına göre tarama sırasındaki hareket hızı. Tarama hareketi sırasında bowden tüpündeki basınç düştüğü için değerin %100’ün altında olması öneriliyor."

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "İlk katman için hız. Yapı plakasında yapışmayı iyileştirmek için düşük bir değer tavsiye edilir. Yapı plakasının kenar ve radye gibi yapışma yapılarını etkilemez."

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "İlk katmanın yazdırılması için belirlenen hız. Yapı tahtasına yapışmayı artırmak için daha düşük bir değer önerilmektedir."

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "İlk katmandaki hareket hamlelerinin hızı. Daha önce yazdırılan bölümlerin yapı levhasından ayrılmasını önlemek için daha düşük bir değer kullanılması önerilir. Bu ayar değeri, Hareket Hızı ve Yazdırma Hızı arasındaki orana göre otomatik olarak hesaplanabilir."

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "Sorunsuz kopması için filament koptuğundaki sıcaklık değeridir."

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "Baskı yapılacak ortamın sıcaklığı. Bu değer 0 ise yapı hacminin sıcaklığı ayarlanmaz."

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "Yazdırma için başka bir nozül kullanılırken nozülün sıcaklığı."

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "Yazdırma bitmeden hemen önce soğuma işleminin başladığı sıcaklık."

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "İlk katmanın basımında kullanılan sıcaklık."

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "Yazdırma için kullanılan sıcaklık."

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "İlk katmanda ısıtıcı yapı plakası için kullanılan sıcaklık. Bu değer 0 olduğunda yapı plakası ilk katman boyunca ısıtılmaz."

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "Isıtıcı yapı plakası için kullanılan sıcaklık. Bu değer 0 olduğunda yapı plakası ısıtılmaz."

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "Malzemeyi temizlemek için kullanılan sıcaklık; kabaca mümkün olan en yüksek baskı sıcaklığına eşit olmalıdır."

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "Yazdırmadaki alt katmanların kalınlığı. Katman yüksekliğiyle ayrılan bu değer alt katmanların sayısını belirtir."

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "Kaplamanın kenarlarını destekleyen ekstra dolgunun kalınlığı."

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "Alt veya üst kısımdaki modele değdiği yerde destek arayüzü kalınlığı."

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "Destek zeminlerinin kalınlığı. Desteğin üzerinde durduğu modelin üst kısımlarına yazdırılan yoğun katmanların sayısını kontrol eder."

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "Destek tavanlarının kalınlığı. Modelin bulunduğu desteğin üst kısmındaki yoğun katmanların sayısını kontrol eder."

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "Yazdırmadaki üst katmanların kalınlığı. Katman yüksekliğiyle ayrılan bu değer üst katmanların sayısını belirtir."

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "Yazdırmadaki üst/alt katmanların kalınlığı. Katman yüksekliğiyle ayrılan bu değer üst/alt katmanların sayısını belirtir."

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "Yatay yönde duvar kalınlığı. Bu değer duvar hattı genişliğiyle bölündüğünde duvar sayısını belirler."

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Dolgu malzemesinin her bir katmanının kalınlığı Bu değer her zaman katman yüksekliğinin katı olmalıdır, aksi takdirde yuvarlanır."

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "Her katmandaki destek dolgusu malzemesinin kalınlığı. Bu değer her zaman katman yüksekliğinin bir katı olmalıdır, aksi takdirde değer yuvarlanır."

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "Oluşturulacak g-code türü."

msgctxt "material_type description"
msgid "The type of material used."
msgstr "Kullanılan malzemenin türü."

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "Aksi takdirde hacim sızdırılır. Bu değer, genellikle nozül çapının küpüne yakındır."

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "Yazdırılabilir alan genişliği (X yönü)."

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "Desteğin altına yazdırılacak kenarın genişliği. Daha geniş kenar, ekstra malzeme karşılığında baskı tablasına daha fazla alanın yapışacağı anlamına gelir."

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "İç içe geçen yapı kirişlerinin genişliği."

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Prime tower brim/tabanının genişliği. Daha büyük bir taban yapışmayı artırır, ancak etkili baskı alanını da azaltır."

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "İlk Direk Genişliği."

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "Titremenin yapılacağı genişlik. İç duvarlar değiştirilmediği için, bunun dış duvar genişliğinin altında tutulması öneriliyor."

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "Maksimum geri çekme sayısının uygulandığı pencere. Bu değer, geri çekme mesafesi ile hemen hemen aynıdır, bu şekilde geri çekmenin aynı malzeme yolundan geçme sayısı etkin olarak sınırlandırılır."

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "İlk direk konumunun x koordinatı."

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "İlk direk konumunun y koordinatı."

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "Bunlar sahnedeki mevcut destek örgüleridir. Bu ayar Cura tarafından kontrol edilir."

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "Bu, ekstruderin bir köprü duvarı başlamadan hemen önce taraması gereken mesafeyi kontrol eder. Köprü başlamadan önce tarama, nozüldeki basıncı azaltabilir ve daha düz bir köprü üretebilir."

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Bu ayar, raft tabanı taslağındaki iç köşelerin ne kadarının yuvarlak olacağını kontrol eder. İç köşeler, yarıçapı burada verilen değere eşit olacak şekilde yarım daire şeklinde yuvarlanır. Bu ayar aynı zamanda raft dış hattındaki böyle bir daireden daha küçük olan delikleri de kaldırır."

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Bu ayar, raft orta kısmı taslağındaki iç köşelerin ne kadarının yuvarlak olacağını kontrol eder. İç köşeler, yarıçapı burada verilen değere eşit olacak şekilde yarım daire şeklinde yuvarlanır. Bu ayar aynı zamanda raft dış hattındaki böyle bir daireden daha küçük olan delikleri de kaldırır."

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Bu ayar, radye ana hattında yer alan iç köşelerin ne kadar yuvarlanacağını kontrol eder. İç köşeler, burada belirtilen değere eşit yarıçapa sahip yarım daire şeklinde yuvarlanır. Ayrıca bu ayar, söz konusu daireden daha küçük olan radye ana hattındaki delikleri ortadan kaldırır."

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "Bu ayar, raft üstü taslağındaki iç köşelerin ne kadarının yuvarlak olacağını kontrol eder. İç köşeler, yarıçapı burada verilen değere eşit olacak şekilde yarım daire şeklinde yuvarlanır. Bu ayar aynı zamanda raft dış hattındaki böyle bir daireden daha küçük olan delikleri de kaldırır."

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "Bu ayar, düşük ekstrüzyon mesafesi penceresinde oluşan ekstrüzyon sayısını sınırlandırır. Bu penceredeki geri çekmeler yok sayılacaktır. Filamanı düzleştirebildiği ve aşındırma sorunlarına yol açabileceği için aynı filaman parçası üzerinde tekrar tekrar geri çekme yapılmasını önler."

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "Modelin etrafında (sıcak) hava ve kalkanlara dışarıdaki hava akımına karşı set çeken bir duvar oluşturur. Özellikle kolayca eğrilebilen malzemeler için kullanışlıdır."

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "Uç Çapı"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "Malzemenin soğudukça büzülmesini telafi etmek için model bu faktöre göre XY yönünde (yatay olarak) ölçeklenecektir."

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "Malzemenin soğudukça büzülmesini telafi etmek için model bu faktöre göre Z yönünde (dikey olarak) ölçeklenecektir."

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "Malzemenin soğudukça büzülmesini telafi etmek için model bu faktöre göre ölçeklenecektir."

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "Üst Katmanlar"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "Üst Yüzey Genişleme Mesafesi"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "Üst Yüzey Kaldırma Genişliği"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "Üst Yüzey İç Duvar Hızlanması"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "Üst Yüzeyin En Dış Duvar Darbesi"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "Üst Yüzey İç Duvar Hızı"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "Üst Yüzey İç Duvar Akışı"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "Üst Yüzey Dış Duvar Hızlanması"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "Üst Yüzeyin En Dış Duvar Akışı"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "Üst Yüzeyin İç Duvar Darbesi"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "Üst Yüzeyin En Dış Duvar Hızı"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "Üst Yüzey İvmesi"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "Üst Yüzey Ekstruderi"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "Üst Yüzeyin Dış Katman Akışı"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "Üst Yüzey İvmesi Değişimi"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "Üst Yüzey Katmanları"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "Üst Yüzey Hat Yönleri"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "Üst Yüzey Hat Genişliği"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "Üst Yüzey Şekli"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "Üst Yüzey Hızı"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "Üst Kalınlık"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "Nesnenizin bu ayardan daha geniş açıya sahip üst ve/veya alt zeminlerinin yüzeyleri genişletilmez. Böylece model yüzeyinin neredeyse dik açıya sahip olduğu durumlarda ortaya çıkan dar yüzey alanlarının genişletilmesi önlenmiş olur. 0°’lik bir açı yataydır ve yüzey alanının genişlemesine neden olmaz; 90°’lik bir açı dikeydir ve tüm yüzey alanlarının genişlemesine neden olur."

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "Üst / Alt"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "Üst / Alt"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "Üst/Alt İvme"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "Üst/Alt Ekstruderi"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "Üst/Alt Akış"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "Üst/Alt Salınımı"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "Üst/Alt Çizgi Yönleri"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "Üst/Alt Hat Genişliği"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "Üst/Alt Şekil"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "Üst/Alt Hız"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "Üst/Alt Kalınlık"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "Yapı Levhasına Dokunma"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "Direk Çapı"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "Direk Tavanı Açısı"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "Modeli dosyadan indirirken modele uygulanacak olan dönüşüm matrisi."

msgctxt "travel label"
msgid "Travel"
msgstr "Hareket"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "Hareket İvmesi"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "Hareket Atlama Mesafesi"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "Hareket Salınımı"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "Hareket Hızı"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "Modeli sadece bir yüzey, gevşek yüzeyli hacim veya hacimler şeklinde işleyin. Normal yazdırma modu sadece kapalı hacimleri yazdırır. “Yüzey”, dolgusu ve üst/alt dış katmanı olmayan birleşim yüzeyini takip eden tek bir duvar yazdırır. “Her ikisi” kapalı hacimleri normal şekilde ve kalan poligonları yüzey şeklinde yazdırır."

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "Ağaç"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "Üçlü Altıgen"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "Üçgenler"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "Üçgenler"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "Üçgenler"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "Üçgenler"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "Üçgenler"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "Gövde Çapı"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "Bağlantı Çakışma Hacimleri"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "Bundan daha kısa desteklenmeyen duvarlar normal duvar ayarları kullanılarak yazdırılacaktır. Daha uzun desteklenmeyen duvarlar köprü duvarı ayarları kullanılarak yazdırılacaktır."

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "Uyarlanabilir Katmanların Kullanımı"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "Direkleri kullan"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "Hareket hamleleri için ayrı bir ivme oranı kullanın. Hareket hamleleri devre dışı bırakılırsa varış noktasında yazdırılan hattın ivme değerini kullanır."

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "Hareket hamleleri için ayrı bir salınım oranı kullanın. Hareket hamleleri devre dışı bırakılırsa varış noktasında yazdırılan hattın ivme değerini kullanır."

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "Mutlak ekstrüzyon yerine bağıl ekstrüzyon uygulayın. Bağıl E-adımlarının uygulanması, g-code’un sonradan işlenmesini kolaylaştırır. Ancak bu, tüm yazıcılar tarafından desteklenmemektedir ve mutlak E-adımları ile karşılaştırıldığında birikmiş malzemenin miktarında hafif farklılıklar yaratabilir. Bu ayara bakılmaksızın, herhangi bir g-code komut dosyası çıkartılmadan önce ekstrüzyon modu her zaman mutlak değere ayarlı olacaktır."

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "Küçük çıkıntı alanlarını desteklemek için özel direkler kullanın. Bu direkler desteklediğimiz bölgeden daha büyük çaptadır. Çıkıntıyı yaklaştırırsanız direklerin çapı azalır ve bir tavan oluşturur."

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "Çakıştığı diğer dolgu ağını düzeltmek için bu ağı kullanın. Bu birleşim için olan bölgelerle diğer birleşimlerin dolgu bölgelerini değiştirir. Bu birleşim için Üst/Alt Dış Katmanı değil sadece bir Duvarı yazdırmak önerilir."

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "Destek alanlarını belirlemek için bu örgüyü kullanın. Bu örgü, destek yapısını oluşturmak için kullanılabilir."

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "Bu bileşimi, modelin hiçbir parçasının çıkıntı olarak algılanmadığı durumları belirlemek için kullanın. Bu, istenmeyen destek yapısını kaldırmak için kullanılabilir."

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "Kullanıcı Tarafından Belirtilen"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "Dikey Ölçekleme Faktörü Büzülme Telafisi"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "Dilimlenmiş katmanlardaki dikey tolerans. Bir katmanın konturları her katmanın kalınlığının ortasından enine kesitler (Ortalayan) alınarak normal şekilde oluşturulur. Alternatif olarak, her katman, katmanın tüm kalınlığı boyunca hacmin iç kısmına düşen alanlara (Dışlayan) sahip olabilir; veya bir katman, katman içinde herhangi bir yere düşen alanlara (İçeren) sahip olabilir. İçeren seçeneğinde katmandaki çoğu ayrıntı korunur, Dışlayan seçeneği en iyi uyum içindir ve Ortalayan seçeneği ise katmanı orijinal yüzeyin en yakınında tutar."

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "Yapı Levhasının Isınmasını Bekle"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "Nozülün Isınmasını Bekle"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "Duvar İvmesi"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "Duvar Dağılım Sayısı"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "Duvar Ekstruderi"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "Duvar Akışı"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "Duvar Salınımı"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "Duvar Hattı Sayısı"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "Duvar Hattı Genişliği"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "Duvar Sıralaması"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "Duvar Hızı"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "Duvar Kalınlığı"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "Duvar Geçişi Uzunluğu"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "Duvar Geçişi Filtresi Mesafesi"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "Duvar Geçişi Filtresi Kenar Boşluğu"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "Duvar Geçişi Eşik Açısı"

msgctxt "shell label"
msgid "Walls"
msgstr "Duvarlar"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "Bu açıdan daha yüksek çıkıntıya sahip duvarlar çıkıntılı duvar ayarları kullanılarak basılacaktır. Değer 90 ise hiçbir duvarda çıkıntı olmadığı varsayılacaktır. Destek ile desteklenen çıkıntılar da çıkıntı olarak değerlendirilmeyecektir."

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "Bu ayar etkinleştirildiğinde, düzgün hareket planlayıcıları olan yazıcılar için takım yolları düzeltilir. Genel takım yolu yönünden sapan küçük hareketler, akışkan hareketlerini iyileştirmek için düzeltilir."

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "Aktifleştirildiğinde, dolgu hatlarının baskı düzeni, hareketi azaltmak için optimize edilir. Elde edilen hareket zamanındaki azalma dilimlenen modele, dolgu şekline ve yoğunluğuna vs. bağlıdır. Birçok ufak dolgu bölgesine sahip bazı modeller için modelin dilimlenme süresi önemli ölçüde artabilir."

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "Bu ayar etkinleştirildiğinde, yazıcı soğutma fanının hızı desteğin hemen üzerindeki yüzey bölgeleri için değiştirilir."

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "Etkin olduğunda, z dikişi koordinatları her parçanın merkezine göre hizalıdır. Devre dışı olduğunda, koordinatlar yapı levhası üzerinde mutlak bir pozisyonu belirtir."

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "Sıfırdan büyük olduğunda, bu mesafeden daha uzun tarama mesafelerinde geri çekme yapılır. Sıfıra ayarlandığında, bir maksimum belirlenmez ve tarama hareketlerinde geri çekme kullanılmaz."

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "Sıfırdan büyük olduğunda, Delik Yatay Büyüme küçük deliklere kademeli olarak uygulanır (küçük delikler daha fazla büyütülür). Sıfır olarak ayarlandığında Delik Yatay Büyüme tüm deliklere uygulanacaktır. Delik Yatay Büyüme Maksimum Çapı’ndan daha büyük delikler genişletilmez."

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "Delik Yatay Genişlemesi, sıfırdan büyük olmak koşuluyla, her katmandaki tüm deliklere uygulanan ofset miktarıdır. Pozitif değerlerde delikler büyür, negatif değerlerde ise küçülür. Bu ayar etkinleştirildiğinde, Delik Yatay Genişleme Maksimum Çapı ile daha detaylı bir ayarlama yapılabilir."

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "Köprü yüzey alanı bölgeleri yazdırılırken, ekstrude edilen malzeme miktarı bu değerle çarpılır."

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "Köprü duvarları yazdırılırken, ekstrude edilen malzeme miktarı bu değerle çarpılır."

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "İkinci köprü yüzey alanı katmanı yazdırılırken, ekstrude edilen malzeme miktarı bu değerle çarpılır."

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "Üçüncü köprü yüzey alanı katmanı yazdırılırken, ekstrude edilen malzeme miktarı bu değerle çarpılır."

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "Düşük katman süresi nedeniyle minimum hıza inildiğinde yazıcı başlığını yazıcıdan kaldırıp düşük katman süresine ulaşana kadar olan ek süreyi bekleyin."

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "Modelde yalnızca birkaç katmanda küçük dikey boşluklar varsa normal şartlarda dar alandaki bu katmanların etrafında dış bir katman olmalıdır. Dikey boşluğun çok küçük olduğu durumlarda dış katman oluşturulmaması için bu ayarı etkinleştirin. Böylece baskı ve dilimleme süresi kısalır ancak teknik olarak bakıldığında havayla temasa açık dolgular kalır."

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "Çift ve tek sayıdaki duvarlar arasında ne zaman geçiş oluşturulacağını gösterir. Bu ayardan daha geniş açıya sahip bir kama şekline geçiş eklenmez ve kalan alanının doldurulması sırasında merkez noktada duvar baskısı yapılmaz. Bu ayarın düşürülmesi bu merkez duvarların sayısını ve uzunluğunu azaltır fakat boşluklara ve aşırı ekstrüzyona neden olabilir."

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "Farkı sayıda duvar arasından geçerken parça daha ince hale geldiğinden duvar hatlarını bölmek veya birleştirmek için belirli bir alan ayrılır."

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Sürme sırasında yapı plakası nozül ve baskı arasında açıklık oluşturmak üzere alçaltılır. Bu işlem, hareket sırasında nozülün baskıya çarpmasını önler ve baskının devrilerek yapı plakasından düşme olasılığını azaltır."

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "Geri çekme her yapıldığında, nozül ve baskı arasında açıklık oluşturmak için yapı levhası indirilir. Yapı levhasından baskıya çarpma şansını azaltarak nozülün hareket sırasında baskıya değmesini önler."

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "Destek X/Y Mesafesinin Destek Z Mesafesinden veya tersi yönde fazla olup olmadığı. X/Y, Z’den fazla olursa, X/Y mesafesi çıkıntıya olan asıl Z mesafesini etkileyerek desteği modelden iter. Çıkıntıların etrafına X/Y mesafesi uygulayarak bunu engelleyebiliriz."

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "Yazıcı sıfır noktasının X/Y koordinatlarının yazdırılabilir alanın merkezinde olup olmadığı."

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "X ekseninin kapamasının pozitif yönde mi (yüksek X koordinatı) yoksa negatif yönde mi (düşük X koordinatı) olduğu."

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "Y ekseninin kapamasının pozitif yönde mi (yüksek Y koordinatı) yoksa negatif yönde mi (düşük Y koordinatı) olduğu."

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "Z ekseninin kapamasının pozitif yönde mi (yüksek Z koordinatı) yoksa negatif yönde mi (düşük Z koordinatı) olduğu."

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "Ekstrüderlerin tek bir ısıtıcıyı mı paylaşacağı yoksa her bir ekstrüderin kendi ısıtıcısı mı olacağı."

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "Ekstrüderlerin tek bir nozülü mü paylaşacağı yoksa her bir ekstrüderin kendi nozülü mü olacağıdır. True olarak ayarlandığında printer-start gcode betiğinin tüm ekstrüderleri bilinen ve karşılıklı olarak uyumlu olan bir ilk geri çekme durumunda (sıfır veya geri çekilmemiş bir filament) düzgün bir şekilde ayarlaması beklenir. Bu durumda ilk geri çekme, ekstrüder başına \"machine_extruders_shared_nozzle_initial_retraction\" parametresi ile açıklanır."

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "Makinenin mevcut yapı levhasını ısıtıp ısıtmadığı."

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "Makinenin yapı hacmi sıcaklığını dengeleyip dengelemediği."

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "Nesnenin kaydedildiği koordinat sistemini kullanmak yerine nesnenin yapı platformunun (0,0) ortasına yerleştirilmesi."

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "Cura üzerinden sıcaklığın kontrol edilip edilmeme ayarı. Nozül sıcaklığını Cura dışından kontrol etmek için bu ayarı kapalı konuma getirin."

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "Gcode başlangıcında yapı levhası sıcaklık komutlarını ekleyip eklememe. start_gcode zaten yapı levhası sıcaklığı içeriyorsa Cura ön ucu otomatik olarak bu ayarı devre dışı bırakır."

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "Gcode başlangıcında nozül sıcaklık komutlarını ekleyip eklememe. start_gcode zaten nozül sıcaklığı içeriyorsa Cura ön ucu otomatik olarak bu ayarı devre dışı bırakır."

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "Katmanlar arasına nozül sürme G-Code'u eklenip eklenmeyeceği (katman başına maksimum 1). Bu ayarın etkinleştirilmesi katman değişiminde geri çekme davranışını etkileyebilir. Sürme komutunun çalıştığı katmanlarda geri çekmeyi kontrol etmek için lütfen Sürme Geri Çekme ayarlarını kullanın."

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "Yapı levhası sıcaklığı başlangıca ulaşana kadar bekleme komutu ekleyip eklememe."

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "Yazdırma öncesinde bir damla ile filamanın astarlanıp astarlanmayacağı. Bu ayar açık olarak ayarlandığında, yazdırma öncesinde ekstrüder nozülünde malzeme hazır olacaktır. Kenar veya Etek Yazdırma da astarlama etkisi yapabilir; bu durumda bu ayarın kapatılmasıyla biraz zaman kazanılabilir."

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "Sıradakine geçmeden önce, tüm modellerin tek seferde bir katmanla mı yazdırılacağı yoksa bir modelin bitmesinin mi bekleneceği. Teker teker modu a) yalnızca bir ekstrüder etkinleştirildiğinde b) tüm modeller baskı kafası aralarında hareket edecek veya nozül ile X/Y eksenleri arasındaki mesafeden az olacak şekilde ayrıldığında kullanılabilir."

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "Ayrı json dosyalarında belirtilen bu makinenin farklı varyantlarının gösterilip gösterilmemesi."

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "Malzemeyi geri çekmek için G1 komutlarında E özelliği yerine aygıt yazılımı çekme komutlarının (G10/G11) kullanılıp kullanılmayacağı."

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "Yapı levhası sıcaklığı başlangıca ulaşana kadar bekleyip beklememe."

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "Tek bir dolgu hattının genişliği."

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "Destek çatısı veya zemininin tek çizgi genişliği."

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "Baskının üst tarafında bulunan alanlardaki tek bir hattın genişliği."

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "Tek bir hattın genişliği Genellikle her hattın genişliği nozül genişliğine eşit olmalıdır. Ancak, bu değeri biraz azaltmak daha iyi baskılar üretilmesini sağlayabilir."

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "Tek bir ilk direk hattının genişliği."

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "Tek bir etek veya kenar hattının genişliği."

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "Tek bir destek zemininin çizgi genişliği."

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "Tek bir destek çatısının çizgi genişliği."

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "Tek bir destek yapısı hattının genişliği."

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "Tek bir üst/alt hattın genişliği."

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "En dış duvar haricindeki tüm duvar hatları için tek bir duvar hattı genişliği."

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "Tek bir duvar hattının genişliği."

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "Radyenin taban katmanındaki hatların genişliği. Bunlar, yapı levhasına yapışma işlemine yardımcı olan kalın hatlar olmalıdır."

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "Radyenin orta katmanındaki hatların genişliği. İkinci katmanın daha fazla sıkılması hatların yapı levhasına yapışmasına neden olur."

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Radyenin üst yüzeyindeki hatların genişliği. Radyenin üstünün pürüzsüz olması için bunlar ince hat olabilir."

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "En dıştaki duvar hattının genişliği. Bu değeri azaltarak daha yüksek seviyede ayrıntılar yazdırılabilir."

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "Modelin ince yüz hatlarının yerini alacak duvarın genişliğidir (Minimum Yüz Hattı Boyutuna göre). Minimum Duvar Hattı Genişliği, yüz hattının kalınlığından daha inceyse duvar da yüz hattının kendisi kadar kalınlaştırılacaktır."

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "Sürme Fırçası X Konumu"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "Sürme Sıçrama Hızı"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "İlk Direkteki Sürme İnaktif Nozülü"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "Sürme Hareket Mesafesi"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "Katmanlar Arasındaki Sürme Nozülü"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "Sürmeyi Durdurma"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "Sürme Tekrar Sayısı"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "Sürme Geri Çekme Mesafesi"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "Sürme Geri Çekmenin Etkinleştirilmesi"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "Sürme Geri Çekme Sırasındaki İlave Astar Miktarı"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "Sürme Geri Çekme Sırasındaki Çalışmaya Hazırlama Hızı"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "Sürme Geri Çekme Sırasındaki Çekim Hızı"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "Sürme Geri Çekme Hızı"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "Sürme Z Sıçraması"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "Sürme Z Sıçraması Yüksekliği"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "Dolgu İçinde"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "Aktif olmayan araca geçici komut gönderildikten sonra aktif aracı yazın. Smoothie veya modal araç komutlarına sahip diğer donanım yazılımları ile Çift Ekstrüderli baskı için gereklidir."

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "X Kapaması Pozitif Yönde"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "Sürme komutunun başlatılacağı X konumu."

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y, Z’den fazla"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "Y Kapaması Pozitif Yönde"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "Z Kapaması Pozitif Yönde"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "Ekstruder Değişimi Sonrasındaki Z Sıçraması"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "Ekstruder Yüksekliği Değişimi Sonrasındaki Z Sıçraması"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z Sıçraması Yüksekliği"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "Sadece Yazdırılan Parçalar Üzerindeki Z Sıçraması"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Z Atlama Hızı"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "Geri Çekildiğinde Z Sıçraması"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z Dikiş Hizalama"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z Dikişi Konumu"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Z Dikişi Göreliliği"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z Dikişi X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z Dikişi Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z, X/Y’den fazla"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zik Zak"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zik Zak"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zik Zak"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zik Zak"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"

msgctxt "travel description"
msgid "travel"
msgstr "hareket"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>Bir nozül değişimi sırasında soğutma fanlarının etkinleştirilip etkinleştirilmeyeceği. Bu, nozülü daha hızlı soğutarak sızıntıyı azaltmaya yardımcı olabilir:<ul><li><b>Değişmemiş:</b> Fanları daha önce olduğu gibi tutun</li><li><b>Sadece son ekstrüder:</b> Son kullanılan ekstrüderin fanını açın ama diğerlerini (varsa) kapatın. Bu, tamamen ayrı ekstrüderleriniz varsa kullanışlıdır.</li><li><b>Tüm fanlar:</b> Nozül değişimi sırasında tüm fanları açın. Bu, tek bir soğutma fanınız veya birbirine yakın duran birden fazla fanınız varsa kullanışlıdır.</li></ul></html>"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "Tüm fanlar"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "Ekstruder değişimi sırasında soğutma"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "Destek yapısının z dikiş izi ile gerçek 3D model arasındaki uzamsal ilişkiyi yönetin. Bu kontrol, kullanıcıların baskı sonrası destek yapılarının, basılan modelde hasara yol açmadan veya iz bırakmadan hatasız bir şekilde çıkarılmasını sağlaması nedeniyle çok önemlidir."

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "Modelden Min Z Dikiş İzi Mesafesi"

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "Desteğin ilk katmanlarındaki dolgu çarpanı. Bunu artırmak, yatak yapışmasına yardımcı olabilir."

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "Sadece son ekstrüder"

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "Z dikiş izini bir çokgen tepe noktasına yerleştirin. Bunu kapatmak, dikiş izini köşeler arasına da yerleştirebilir. (Bunun, dikiş izinin desteklenmeyen bir çıkıntıya yerleştirilmesiyle ilgili kısıtlamaları geçersiz kılmayacağını unutmayın.)"

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "Ana Kule Minimum Kabuk Kalınlığı"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "Raft Tabanı Akış"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "Raft Tabanı Dolgu Örtüşmesi"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "Raft Tabanı Dolgu Örtüşmesi Yüzdesi"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "Raft Akış"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "Raft Arayüzü Akış"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "Raft Arayüzü Dolgu Örtüşmesi"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "Raft Arayüzü Dolgu Örtüşme Yüzdesi"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "Raft Arayüzü Z Ofseti"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "Raft Yüzey Akışı"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "Raft Yüzey Dolgusu Örtüşmesi"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "Raft Yüzey Dolgusu Örtüşme Yüzdesi"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "Raft Yüzeyi Z Ofseti"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "Dikiş İzi Çıkıntılı Duvar Açısı"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "Destek Dolgu Yoğunluğu Çarpanı İlk Katman"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "Destek Z Dikiş İzi Modelden Mesafe"

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "Raft tabanı baskısı sırasında normal bir ekstrüzyon hattına göre ekstrüzyona tabi tutulacak malzeme miktarı. Artan bir akışa sahip olmak yapışmayı ve raft yapısal mukavemetini artırabilir."

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "Raft arayüzü baskısı sırasında normal bir ekstrüzyon hattına göre ekstrüzyona tabi tutulacak malzeme miktarı. Artan bir akışa sahip olmak yapışmayı ve raft yapısal mukavemetini artırabilir."

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "Raft baskısı sırasında normal bir ekstrüzyon hattına göre ekstrüzyona tabi tutulacak malzeme miktarı. Artan bir akışa sahip olmak yapışmayı ve raft yapısal mukavemetini artırabilir."

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "Raft yüzey baskısı sırasında normal bir ekstrüzyon hattına göre ekstrüzyona tabi tutulacak malzeme miktarı. Artan bir akışa sahip olmak yapışmayı ve raft yapısal mukavemetini artırabilir."

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu hattı genişliğinin yüzdesi olarak dolgu ile raft tabanının duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu ile raft tabanının duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu hattı genişliğinin yüzdesi olarak dolgu ile raft arayüzünün duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu ile raft arayüzünün duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu hattı genişliğinin yüzdesi olarak dolgu ile raft yüzeyinin duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "Dolgu ile raft yüzeyinin duvarları arasındaki örtüşme miktarı. Hafif bir örtüşme, duvarların dolguya sıkıca bağlanmasını sağlar."

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "Model ile z ekseni dikiş izindeki destek yapısı arasındaki mesafe."

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "Ana kule kabuğunun minimum kalınlığı. Ana kuleyi güçlendirmek için artırabilirsiniz."

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "Bu açıdan daha fazla çıkıntı yapan duvarlardaki dikiş izlerini önlemeye çalışın. Değer 90 olduğunda hiçbir duvar çıkıntı olarak kabul edilmeyecektir."

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "Değişmemiş"

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "Raft arayüzünün ilk katmanını yazdırırken taban ile arayüz arasındaki yapışmayı özelleştirmek için bu ofset ile öteleyin. Negatif bir ofset yapışmayı iyileştirmelidir."

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "Raft yüzeyinin ilk katmanını yazdırırken arayüz ve yüzey arasındaki yapışmayı özelleştirmek için bu ofset ile öteleyin. Negatif bir ofset yapışmayı iyileştirmelidir."

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "Tepe Noktasında Z Dikiş İzi"

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "Yukarıdaki kaplamaları desteklemek için dolgu desenine ekstra hatlar ekleyin. Bu seçenek, karmaşık şekilli kaplamalarda alttaki dolgunun üstte basılan kaplama katmanını doğru şekilde desteklememesi nedeniyle bazen ortaya çıkan delikleri veya plastik lekeleri önler. \"Duvarlar\" sadece kaplamanın ana hatlarını desteklerken, \"Duvarlar ve Hatlar\" kaplamayı oluşturan hatların uçlarını da destekler."

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "Yükseklikteki Yapı Fan Hızı"

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "Katmandaki Yapı Fan Hızı"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "Yapı hacmi fan sayısı"

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "Atkı dikişi boyunca kalıptan geçirirken akış değişimindeki her adımın uzunluğunu belirler. Daha küçük bir mesafe, daha hassas ama aynı zamanda daha karmaşık bir G koduna sebep olacaktır."

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "Z dikişini daha az görünür hâle getirmesi gereken bir dikiş türü olan atkı dikişinin uzunluğunu belirler. Etkili olması için 0'dan büyük olmalıdır."

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "Kademeli akış değişimindeki her adımın süresi"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "Kademeli akış değişikliklerini etkinleştirin. Etkinleştirildiğinde akış, hedef akışa doğru kademeli olarak artırılır/azaltılır. Bu, akışın ekstrüder motoru çalıştığında/durduğunda hemen değişmediği bowden tüplü yazıcılar için kullanışlıdır."

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "Kaplamaları Desteklemek İçin Ekstra Dolgu Hatları"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "Bu değerden daha uzun herhangi bir seyahat hareketi için malzeme akışı, hedef akış yollarına sıfırlanır"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "Kademeli akış ayrıklaştırma adım boyutu"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "Kademeli akış etkinleştirildi"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "Kademeli akış maksimum ivme"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "İlk katman maksimum akış ivmesi"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "Kademeli akış değişiklikleri için maksimum ivme"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "İlk katman için kademeli akış değişiklikleri için minimum hız"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "Hiçbiri"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr "Dış Duvar Hızlanması"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr "Dış Duvar Yavaşlaması"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "Dış Duvar Bitiş Hız Oranı"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "Dış Duvar Hızı Bölünme Mesafesi"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "Dış Duvar Başlangıç Hız Oranı"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "Akış süresini sıfırla"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "Atkı Dikişi Uzunluğu"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "Atkı Dikişi Başlangıç Yüksekliği"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "Atkı Dikişi Adım Uzunluğu"

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "Fanların normal fan hızında döndüğü yükseklik. Aşağıdaki katmanlarda fan hızı, Başlangıç Fan Hızından Normal Fan Hızına doğru kademeli olarak artar."

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "Yapı fanlarının tam fan hızında döndüğü katman. Bu değer hesaplanır ve bir tam sayıya yuvarlanır."

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "Yapı hacmini soğutan fan sayısı. Bu, 0 olarak ayarlanırsa hiç yapı hacmi fanı olmadığı anlamına gelir"

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "Atkı dikişinin başlatılacağı, seçilen katman yüksekliğinin oranı. Daha düşük bir sayı, daha büyük bir dikiş yüksekliği ile sonuçlanacaktır. Etkili olması için 100'den düşük olmalıdır."

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "Bu değer, bir dış duvar yazdırılırken en yüksek hıza ulaşmak için gereken hızlanmayı ifade eder."

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "Bu değer, bir dış duvar yazdırma işleminin sonlandırılacağı yavaşlamayı ifade eder."

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "Bu değer, dış duvar hızlanmasını/yavaşlamasını uygulamak için daha uzun bir yolu bölerken bir kalıptan basma yolunun maksimum uzunluğudur. Daha küçük bir mesafe daha hassas ama aynı zamanda daha karmaşık bir G Kodu oluşturacaktır."

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "Bu değer, bir dış duvar yazdırılırken işlemin sonlandırılacağı en yüksek hızın oranını ifade eder."

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "Bu değer, bir dış duvar yazdırılırken işlemin başlayacağı en yüksek hızın oranını ifade eder."

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "Sadece Duvarlar"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "Duvarlar ve Hatlar"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "Bu açıdan daha fazla çıkıntı yapan duvarlar, çıkıntılı duvar ayarları kullanılarak yazdırılacaktır. Değer 90 olduğunda, hiçbir duvar çıkıntı olarak değerlendirilmeyecektir. Dayanak tarafından desteklenen çıkıntılar da çıkıntı olarak değerlendirilmeyecektir. Ayrıca, çıkıntının yarısından az olan herhangi bir hat da çıkıntı olarak değerlendirilmeyecektir."

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "Alt yüzey kabuk katmanları çizgi veya zikzak desenini kullandığında kullanılacak tam sayı çizgi yönlerinin listesi. Katmanlar ilerledikçe listedeki elemanlar sırayla kullanılır ve listenin sonuna ulaşıldığında tekrar baştan başlanır. Liste ögeleri virgülle ayrılır ve listenin tamamı köşeli parantez içinde yer alır. Liste ögeleri virgülle ayrılır ve listenin tamamı köşeli parantez içinde yer alır. Varsayılan, geleneksel varsayılan açıları (45 ve 135 derece) kullanmak anlamına gelen boş bir listedir."

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "Alt Yüzey İç Duvar İvmesi"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "Alt Yüzey İç Duvar Sarsıntısı"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "Alt Yüzey İç Duvar Hızı"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "Alt Yüzey İç Duvar(lar) Akışı"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "Alt Yüzey Dış Duvar İvmesi"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "Alt Yüzey Dış Duvar Akışı"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "Alt Yüzey Dış Duvar Sarsıntısı"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "Alt Yüzey Dış Duvar Hızı"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "Alt Yüzey Kabuk İvmesi"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "Alt Yüzey Kabuk Ekstruderi"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "Alt Yüzey Kabuk Akışı"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "Alt Yüzey Kabuk Sarsıntısı"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "Alt Yüzey Kabuk Katmanları"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "Alt Yüzey Kabuk Çizgisi Yönleri"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "Alt Yüzey Kabuk Çizgisi Genişliği"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "Alt Yüzey Kabuk Deseni"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "Alt Yüzey Kabuk Hızı"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "Eş merkezli"

msgctxt "variant_name"
msgid "Extruder"
msgstr "Ekstruder"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "En dıştaki hariç tüm duvar hatları için alt yüzey duvar hatlarında akış dengelemesi."

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "Baskının alt kısmındaki alanların çizgilerinde akış dengelemesi."

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "Alt yüzey dış duvar hattında akış dengelemesi."

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "Griffin+Çita"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "İç Seyahat Mesafeden Kaçın"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "Hatlar"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "Çıkıntı ile Minimum Katman Süresi"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "Minimum Çıkıntı Segment Uzunluğu"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "Monotonik Alt Yüzey Düzeni"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "Dış Duvar Sonu Yavaşlaması"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "Dış Duvar Başlangıç ​​İvmesi"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "Sarkık Duvar Hızları"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "Duvarların sarkması durumunda baskı normal baskı hızının belirli bir yüzdesi oranında yapılacaktır. Örneğin [75, 50, 25] ayarlayarak birden fazla değer belirtebilirsiniz, böylece daha fazla sarkan duvar daha da yavaş yazdırılır."

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "Basınç ilerleme faktörü"

msgctxt "variant_name"
msgid "Print Core"
msgstr "Baskı Çekirdeği"

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "Alt yüzey çizgilerini, her zaman aynı yöndeki bitişik çizgilerle örtüşecek şekilde sıralayın. Bu, baskı için biraz daha fazla zaman alır ama düz yüzeylerin daha tutarlı görünmesini sağlar."

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "GCode'u Başlat, ilk sırada olmalıdır"

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "Alt yüzey kabuk katmanlarının basıldığı ivme."

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "Alt yüzey iç duvarlarının basıldığı ivme."

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "Alt yüzeyin en dış duvarlarının basıldığı ivme."

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "'Birer Birer' yazdırırken 'Güvenli Model Mesafesi'ni belirlemek için kullanılan baskı başlığının boyutları. Bu sayılar, ilk ekstruder nozülünün merkez hattına aittir. Nozülün solu 'X Min'dir ve negatif olmalı. Nozülün arkası 'Y Min'dir ve negatif olmalı. X Max (sağ) ve Y Max (ön) pozitif sayılardır. Vinç yüksekliği, yapı plakasından X vinci kirişine kadar olan boyuttur."

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "Bir modelin içinde hareket ederken nozül ile önceden basılmış dış duvarlar arasındaki mesafe."

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "En alttaki kabuğu basmak için kullanılan ekstruder treni. Çoklu ekstrüzyonda kullanılır."

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "Alt yüzey kabuk katmanlarının basıldığı maksimum anlık hız değişimi."

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "Alt yüzey iç duvarlarının basıldığı maksimum anlık hız değişimi."

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "Alt yüzeyin en dış duvarlarının basıldığı maksimum anlık hız değişimi."

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "Sarkan ekstrüzyonlar içeren bir katmanda harcanan minimum süre. Bu, yazıcının yavaşlamasına, en azından burada ayarlanan süreyi tek katmanda geçirmesine neden olur. Bu, bir sonraki katmanın basılmasından önce basılı malzemenin düzgün bir şekilde soğumasını sağlar. Kaldırma Başlığı devre dışı bırakılmışsa ve aksi takdirde Minimum Hız ihlal edilecekse, katmanlar yine de minimum katman süresinden daha kısa sürebilir."

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "Kabuğun en alt katmanlarının sayısı. Genellikle daha kaliteli alt yüzeyler elde etmek için sadece en alttaki tek bir katman yeterlidir."

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "En alttaki katmanların deseni."

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr "Alt yüzey kabuk katmanlarının basılma hızı."

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "Alt yüzey iç duvarlarının basılma hızı."

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "Alt yüzeyin en dış duvarının basılma hızı."

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "Bu ayar, başlangıç ​​g kodunun her zaman ilk g kodu olmaya zorlanıp zorlanmayacağını kontrol eder. Bu seçenek olmadan başlangıç ​​g-kodundan önce T0 gibi başka bir g kodu eklenebilir."

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "Ekstrüzyonu hareketle senkronize etmeyi amaçlayan basınç avansı için ayar faktörü"

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "Sarkan katmanlara özgü minimum katman süresi uygulanmaya çalışıldığı zaman yalnızca en az bir ardışık sarkan ekstrüzyon hareketi bu değerden daha uzunsa uygulanacaktır."

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "Baskının alt kısmındaki alanların tek bir çizgisinin genişliği."

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "Zikzak"
