msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2024-03-13 09:02+0100\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=1; plural=0;\n"

msgctxt "prime_tower_mode description"
msgid "<html>How to generate the prime tower:<ul><li><b>Normal:</b> create a bucket in which secondary materials are primed</li><li><b>Interleaved:</b> create a prime tower as sparse as possible. This will save time and filament, but is only possible if the used materials adhere to each other</li></ul></html>"
msgstr "<html>如何空心主塔:<ul><li><b>通常的:</b>创建一个桶状结构,在其中填充辅助材料</li><li><b>交错的:</b> 创建一个尽可能稀疏的主塔。这将节省时间和丝材,但只有当所用材料粘附在每个部件上时才有可能</li></ul></html>"

msgctxt "brim_inside_margin description"
msgid "A brim around a model may touch an other model where you don't want it. This removes all brim within this distance from brimless models."
msgstr "模型周围的裙边可能会触及您不希望接触到的其他模型。此操作会将与其他无裙边的模型小于特定距离的裙边打印区域删除。"

msgctxt "ironing_inset description"
msgid "A distance to keep from the edges of the model. Ironing all the way to the edge of the mesh may result in a jagged edge on your print."
msgstr "与模型边缘保持的距离。 一直熨平至网格的边缘可能导致打印品出现锯齿状边缘。"

msgctxt "material_no_load_move_factor description"
msgid "A factor indicating how much the filament gets compressed between the feeder and the nozzle chamber, used to determine how far to move the material for a filament switch."
msgstr "表示长丝在进料器和喷嘴室之间被压缩多少的系数，用于确定针对长丝开关将材料移动的距离。"

msgctxt "roofing_angles description"
msgid "A list of integer line directions to use when the top surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "当顶部表面皮肤层采用线条或锯齿状图案时使用的整数走线方向的列表。 列表中的元素随层的进度依次使用，当达到列表末尾时，它将从头开始。 列表项以逗号分隔，整个列表包含在方括号中。 默认是一个空列表，即意味着使用传统的默认角度（45 和 135 度）。"

msgctxt "skin_angles description"
msgid "A list of integer line directions to use when the top/bottom layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "当顶层/底层采用线条或锯齿状图案时使用的整数走线方向的列表。 列表中的元素随层的进度依次使用，当达到列表末尾时，它将从头开始。 列表项以逗号分隔，整个列表包含在方括号中。 默认是一个空列表，即意味着使用传统的默认角度（45 和 135 度）。"

msgctxt "support_infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angle 0 degrees."
msgstr "要使用的整数走线方向列表。列表中的元素随层的进度依次使用，当达到列表末尾时将从头开始。列表项以逗号分隔，整个列表包含在方括号中。“默认“是一个空列表，即意味着使用默认角度 0 度。"

msgctxt "support_bottom_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的整数走线方向列表。列表中的元素随层的进度依次使用，当达到列表末尾时将从头开始。列表项以逗号分隔，整个列表包含在方括号中。“默认“为一个空列表，即意味着使用默认角度（如果接触面很厚或为 90 度，则在 45 度和 135 度之间交替）。"

msgctxt "support_interface_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的整数走线方向列表。列表中的元素随层的进度依次使用，当达到列表末尾时将从头开始。列表项以逗号分隔，整个列表包含在方括号中。“默认“为一个空列表，即意味着使用默认角度（如果接触面很厚或为 90 度，则在 45 度和 135 度之间交替）。"

msgctxt "support_roof_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the default angles (alternates between 45 and 135 degrees if interfaces are quite thick or 90 degrees)."
msgstr "要使用的整数走线方向列表。列表中的元素随层的进度依次使用，当达到列表末尾时将从头开始。列表项以逗号分隔，整个列表包含在方括号中。“默认“为一个空列表，即意味着使用默认角度（如果接触面很厚或为 90 度，则在 45 度和 135 度之间交替）。"

msgctxt "infill_angles description"
msgid "A list of integer line directions to use. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees for the lines and zig zag patterns and 45 degrees for all other patterns)."
msgstr "要使用的整数走线方向列表。 列表中的元素随层的进度依次使用，当达到列表末尾时，它将从头开始。 列表项以逗号分隔，整个列表包含在方括号中。 默认是一个空列表，即意味着使用传统的默认角度（线条和锯齿形图案为 45 和 135 度，其他所有图案为 45 度）。"

msgctxt "nozzle_disallowed_areas description"
msgid "A list of polygons with areas the nozzle is not allowed to enter."
msgstr "包含不允许喷嘴进入区域的多边形列表。"

msgctxt "machine_disallowed_areas description"
msgid "A list of polygons with areas the print head is not allowed to enter."
msgstr "包含不允许打印头进入区域的多边形列表。"

msgctxt "support_tree_branch_reach_limit description"
msgid "A recomendation to how far branches can move from the points they support. Branches can violate this value to reach their destination (buildplate or a flat part of the model). Lowering this value will make the support more sturdy, but increase the amount of branches (and because of that material usage/print time) "
msgstr "建议分支离从其支撑点移动的距离。分支可以违反此值以到达其目的地（打印平台或模型的平面部分）。降低此值可以使支撑更坚固，但会增加分支数量（进而增加材料的使用/打印时间）"

msgctxt "extruder_prime_pos_abs label"
msgid "Absolute Extruder Prime Position"
msgstr "绝对挤出机主要位置"

msgctxt "adaptive_layer_height_variation label"
msgid "Adaptive Layers Maximum Variation"
msgstr "自适应图层最大变化"

msgctxt "adaptive_layer_height_threshold label"
msgid "Adaptive Layers Topography Size"
msgstr "自适应图层地形尺寸"

msgctxt "adaptive_layer_height_variation_step label"
msgid "Adaptive Layers Variation Step Size"
msgstr "自适应图层变化步长"

msgctxt "adaptive_layer_height_enabled description"
msgid "Adaptive layers computes the layer heights depending on the shape of the model."
msgstr "自适应图层根据模型形状计算图层高度。"

msgctxt "infill_wall_line_count description"
msgid "Add extra walls around the infill area. Such walls can make top/bottom skin lines sag down less which means you need less top/bottom skin layers for the same quality at the cost of some extra material.\nThis feature can combine with the Connect Infill Polygons to connect all the infill into a single extrusion path without the need for travels or retractions if configured right."
msgstr "在填充区域周围添加额外壁。此类壁可减少顶部/底部皮肤走线，这意味着只要付出一些额外的材料就可以使用更少的顶部/底部皮肤层达到相同的质量。"
"在适当配置的情况下，此功能可结合连接填充多边形以将所有填充物连接到单一挤出路径而无需空驶或回抽。"

msgctxt "platform_adhesion description"
msgid "Adhesion"
msgstr "附着"

msgctxt "material_adhesion_tendency label"
msgid "Adhesion Tendency"
msgstr "附着倾向"

msgctxt "skin_overlap description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines, as a percentage of the line widths of the skin lines and the innermost wall. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any percentage over 50% may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "调整壁和皮肤中心线的（端点）之间的重叠量，以皮肤线走线和最内壁的线宽度的百分比表示。稍微重叠可让各个壁与皮肤牢固连接。请注意，对于相等的皮肤和壁线宽度，任何超过 50% 的百分比可能已经导致任何皮肤越过壁，因为在该点，皮肤挤出机的喷嘴位置可能已经达到越过壁中间的位置。"

msgctxt "skin_overlap_mm description"
msgid "Adjust the amount of overlap between the walls and (the endpoints of) the skin-centerlines. A slight overlap allows the walls to connect firmly to the skin. Note that, given an equal skin and wall line-width, any value over half the width of the wall may already cause any skin to go past the wall, because at that point the position of the nozzle of the skin-extruder may already reach past the middle of the wall."
msgstr "调整壁和皮肤中心线的（端点）之间的重叠量。稍微重叠可让各个壁与皮肤牢固连接。请注意，对于相等的皮肤和壁线宽度，任何超过壁宽度一半的值可能已经导致任何皮肤越过壁，因为在该点，皮肤挤出机的喷嘴位置可能已经达到越过壁中间的位置。"

msgctxt "infill_sparse_density description"
msgid "Adjusts the density of infill of the print."
msgstr "调整打印填充的密度。"

msgctxt "support_interface_density description"
msgid "Adjusts the density of the roofs and floors of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "调整支撑结构顶板和底板的密度。 较高的值会实现更好的悬垂，但支撑将更加难以移除。"

msgctxt "support_tree_top_rate description"
msgid "Adjusts the density of the support structure used to generate the tips of the branches. A higher value results in better overhangs, but the supports are harder to remove. Use Support Roof for very high values or ensure support density is similarly high at the top."
msgstr "调整用于生成分支顶端的支撑结构的密度。高数值可以确保悬垂质量更好，但支撑结构会更难去除。用支撑顶板获取极高数值，或者确保顶层的支撑密度同样高。"

msgctxt "support_infill_rate description"
msgid "Adjusts the density of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "调整支撑结构的密度。 较高的值会实现更好的悬垂，但支撑将更加难以移除。"

msgctxt "material_diameter description"
msgid "Adjusts the diameter of the filament used. Match this value with the diameter of the used filament."
msgstr "调整所用耗材的直径。 将此值与所用耗材的直径匹配。"

msgctxt "support_type description"
msgid "Adjusts the placement of the support structures. The placement can be set to touching build plate or everywhere. When set to everywhere the support structures will also be printed on the model."
msgstr "调整支撑结构的放置。 放置可以设置为支撑打印平台或全部支撑。 当设置为全部支撑时，支撑结构也将在模型上打印。"

msgctxt "prime_tower_wipe_enabled description"
msgid "After printing the prime tower with one nozzle, wipe the oozed material from the other nozzle off on the prime tower."
msgstr "在用一个喷嘴打印装填塔后，从装填塔上的另一个喷嘴擦去渗出的材料。"

msgctxt "retraction_hop_after_extruder_switch description"
msgid "After the machine switched from one extruder to the other, the build plate is lowered to create clearance between the nozzle and the print. This prevents the nozzle from leaving oozed material on the outside of a print."
msgstr "当机器从一个挤出机切换到另一个时，打印平台会降低以便在喷嘴和打印品之间形成空隙。 这将防止喷嘴在打印品外部留下渗出物。"

msgctxt "retraction_combing option all"
msgid "All"
msgstr "所有"

msgctxt "print_sequence option all_at_once"
msgid "All at Once"
msgstr "同时打印"

msgctxt "resolution description"
msgid "All settings that influence the resolution of the print. These settings have a large impact on the quality (and print time)"
msgstr "影响打印分辨率的所有设置。 这些设置会对质量（和打印时间）产生显著影响"

msgctxt "user_defined_print_order_enabled description"
msgid "Allows you to order the object list to manually set the print sequence. First object from the list will be printed first."
msgstr "允许您对对象列表进行排序以手动设置打印顺序。列表中的第一个对象将首先打印。"

msgctxt "alternate_extra_perimeter label"
msgid "Alternate Extra Wall"
msgstr "交替备用壁"

msgctxt "alternate_carve_order label"
msgid "Alternate Mesh Removal"
msgstr "交替网格移除"

msgctxt "material_alternate_walls label"
msgid "Alternate Wall Directions"
msgstr "交替壁方向"

msgctxt "material_alternate_walls description"
msgid "Alternate wall directions every other layer and inset. Useful for materials that can build up stress, like for metal printing."
msgstr "在每一层或嵌入上交替壁方向。这适用于会产生应力的材料，例如在金属打印中。"

msgctxt "machine_buildplate_type option aluminum"
msgid "Aluminum"
msgstr "铝"

msgctxt "machine_always_write_active_tool label"
msgid "Always Write Active Tool"
msgstr "始终写入活动工具"

msgctxt "travel_retract_before_outer_wall description"
msgid "Always retract when moving to start an outer wall."
msgstr "在移动开始打印外壁时始终回抽。"

msgctxt "xy_offset description"
msgid "Amount of offset applied to all polygons in each layer. Positive values can compensate for too big holes; negative values can compensate for too small holes."
msgstr "应用到每一层所有多边形的偏移量。 正数值可以补偿过大的孔洞；负数值可以补偿过小的孔洞。"

msgctxt "xy_offset_layer_0 description"
msgid "Amount of offset applied to all polygons in the first layer. A negative value can compensate for squishing of the first layer known as \"elephant's foot\"."
msgstr "应用到第一层所有多边形的偏移量。 负数值可以补偿第一层的压扁量（被称为“象脚”）。"

msgctxt "support_offset description"
msgid "Amount of offset applied to all support polygons in each layer. Positive values can smooth out the support areas and result in more sturdy support."
msgstr "应用到每一层所有支撑多边形的偏移量。 正值可以让支撑区域更平滑，并产生更为牢固的支撑。"

msgctxt "support_bottom_offset description"
msgid "Amount of offset applied to the floors of the support."
msgstr "应用到支撑底板的偏移量。"

msgctxt "support_roof_offset description"
msgid "Amount of offset applied to the roofs of the support."
msgstr "应用到支撑顶板的偏移量。"

msgctxt "support_interface_offset description"
msgid "Amount of offset applied to the support interface polygons."
msgstr "应用到支撑接触面多边形的偏移量。"

msgctxt "wipe_retraction_amount description"
msgid "Amount to retract the filament so it does not ooze during the wipe sequence."
msgstr "耗材回抽量，可避免耗材在擦拭期间渗出。"

msgctxt "sub_div_rad_add description"
msgid "An addition to the radius from the center of each cube to check for the boundary of the model, as to decide whether this cube should be subdivided. Larger values lead to a thicker shell of small cubes near the boundary of the model."
msgstr "从每个立方体的中心对半径进行添加，以检查模型的边界，进而决定是否应对此立方体进行分区。 值越大则模型边界附近的小型立方体外壳越厚。"

msgctxt "anti_overhang_mesh label"
msgid "Anti Overhang Mesh"
msgstr "防悬网格"

msgctxt "material_anti_ooze_retracted_position label"
msgid "Anti-ooze Retracted Position"
msgstr "防渗出回抽位置"

msgctxt "material_anti_ooze_retraction_speed label"
msgid "Anti-ooze Retraction Speed"
msgstr "防渗出回抽速度"

msgctxt "machine_use_extruder_offset_to_offset_coords description"
msgid "Apply the extruder offset to the coordinate system. Affects all extruders."
msgstr "将挤出器偏移量应用到坐标轴系统。影响所有挤出器。"

msgctxt "interlocking_enable description"
msgid "At the locations where models touch, generate an interlocking beam structure. This improves the adhesion between models, especially models printed in different materials."
msgstr "在模型接触的位置，生成互锁梁结构。这改善了模型之间的粘合力，特别是用不同材料打印的模型。"

msgctxt "travel_avoid_other_parts label"
msgid "Avoid Printed Parts When Traveling"
msgstr "空驶时避开已打印部分"

msgctxt "travel_avoid_supports label"
msgid "Avoid Supports When Traveling"
msgstr "避免移动时支撑"

msgctxt "z_seam_position option back"
msgid "Back"
msgstr "返回"

msgctxt "z_seam_position option backleft"
msgid "Back Left"
msgstr "左后方"

msgctxt "z_seam_position option backright"
msgid "Back Right"
msgstr "右后方"

msgctxt "machine_gcode_flavor option BFB"
msgid "Bits from Bytes"
msgstr "Bits from Bytes"

msgctxt "magic_mesh_surface_mode option both"
msgid "Both"
msgstr "两者都"

msgctxt "support_interface_priority option nothing"
msgid "Both overlap"
msgstr "两者重叠"

msgctxt "bottom_layers label"
msgid "Bottom Layers"
msgstr "底部层数"

msgctxt "top_bottom_pattern_0 label"
msgid "Bottom Pattern Initial Layer"
msgstr "底层图案起始层"

msgctxt "bottom_skin_expand_distance label"
msgid "Bottom Skin Expand Distance"
msgstr "底部皮肤扩展距离"

msgctxt "bottom_skin_preshrink label"
msgid "Bottom Skin Removal Width"
msgstr "底部皮肤移除宽度"

msgctxt "bottom_thickness label"
msgid "Bottom Thickness"
msgstr "底层厚度"

msgctxt "support_tree_top_rate label"
msgid "Branch Density"
msgstr "分支密度"

msgctxt "support_tree_branch_diameter label"
msgid "Branch Diameter"
msgstr "分支直径"

msgctxt "support_tree_branch_diameter_angle label"
msgid "Branch Diameter Angle"
msgstr "分支直径角度"

msgctxt "material_break_preparation_retracted_position label"
msgid "Break Preparation Retracted Position"
msgstr "断裂缓冲期回抽位置"

msgctxt "material_break_preparation_speed label"
msgid "Break Preparation Retraction Speed"
msgstr "断裂缓冲期回抽速度"

msgctxt "material_break_preparation_temperature label"
msgid "Break Preparation Temperature"
msgstr "断裂缓冲期温度"

msgctxt "material_break_retracted_position label"
msgid "Break Retracted Position"
msgstr "断裂回抽位置"

msgctxt "material_break_speed label"
msgid "Break Retraction Speed"
msgstr "断裂回抽速度"

msgctxt "material_break_temperature label"
msgid "Break Temperature"
msgstr "折断温度"

msgctxt "support_skip_some_zags label"
msgid "Break Up Support In Chunks"
msgstr "将支撑结构分拆成块状"

msgctxt "bridge_fan_speed label"
msgid "Bridge Fan Speed"
msgstr "连桥风扇速度"

msgctxt "bridge_enable_more_layers label"
msgid "Bridge Has Multiple Layers"
msgstr "连桥有多层"

msgctxt "bridge_skin_density_2 label"
msgid "Bridge Second Skin Density"
msgstr "连桥第二层表面密度"

msgctxt "bridge_fan_speed_2 label"
msgid "Bridge Second Skin Fan Speed"
msgstr "连桥第二层表面风扇速度"

msgctxt "bridge_skin_material_flow_2 label"
msgid "Bridge Second Skin Flow"
msgstr "连桥第二层表面流量"

msgctxt "bridge_skin_speed_2 label"
msgid "Bridge Second Skin Speed"
msgstr "连桥第二层表面速度"

msgctxt "bridge_skin_density label"
msgid "Bridge Skin Density"
msgstr "连桥表面密度"

msgctxt "bridge_skin_material_flow label"
msgid "Bridge Skin Flow"
msgstr "连桥表面流量"

msgctxt "bridge_skin_speed label"
msgid "Bridge Skin Speed"
msgstr "连桥表面速度"

msgctxt "bridge_skin_support_threshold label"
msgid "Bridge Skin Support Threshold"
msgstr "连桥表面支撑阈值"

msgctxt "bridge_sparse_infill_max_density label"
msgid "Bridge Sparse Infill Max Density"
msgstr "连桥稀疏填充物最大密度"

msgctxt "bridge_skin_density_3 label"
msgid "Bridge Third Skin Density"
msgstr "连桥第三层表面密度"

msgctxt "bridge_fan_speed_3 label"
msgid "Bridge Third Skin Fan Speed"
msgstr "连桥第三层表面风扇速度"

msgctxt "bridge_skin_material_flow_3 label"
msgid "Bridge Third Skin Flow"
msgstr "连桥第三层表面流量"

msgctxt "bridge_skin_speed_3 label"
msgid "Bridge Third Skin Speed"
msgstr "连桥第三层表面速度"

msgctxt "bridge_wall_coast label"
msgid "Bridge Wall Coasting"
msgstr "桥壁滑行"

msgctxt "bridge_wall_material_flow label"
msgid "Bridge Wall Flow"
msgstr "桥壁流量"

msgctxt "bridge_wall_speed label"
msgid "Bridge Wall Speed"
msgstr "桥壁速度"

msgctxt "adhesion_type option brim"
msgid "Brim"
msgstr "Brim"

msgctxt "brim_inside_margin label"
msgid "Brim Avoid Margin"
msgstr "裙边邻避边距"

msgctxt "brim_gap label"
msgid "Brim Distance"
msgstr "边沿距离"

msgctxt "brim_line_count label"
msgid "Brim Line Count"
msgstr "Brim 走线计数"

msgctxt "brim_location label"
msgid "Brim Location"
msgstr "裙边位置"

msgctxt "brim_replaces_support label"
msgid "Brim Replaces Support"
msgstr "Brim 替换支撑"

msgctxt "brim_width label"
msgid "Brim Width"
msgstr "Brim 宽度"

msgctxt "platform_adhesion label"
msgid "Build Plate Adhesion"
msgstr "打印平台附着"

msgctxt "adhesion_extruder_nr label"
msgid "Build Plate Adhesion Extruder"
msgstr "打印平台附着挤出机"

msgctxt "adhesion_type label"
msgid "Build Plate Adhesion Type"
msgstr "打印平台附着类型"

msgctxt "machine_buildplate_type label"
msgid "Build Plate Material"
msgstr "打印平台材料"

msgctxt "machine_shape label"
msgid "Build Plate Shape"
msgstr "打印平台形状"

msgctxt "material_bed_temperature label"
msgid "Build Plate Temperature"
msgstr "打印平台温度"

msgctxt "material_bed_temperature_layer_0 label"
msgid "Build Plate Temperature Initial Layer"
msgstr "打印平台温度起始层"

msgctxt "build_volume_temperature label"
msgid "Build Volume Temperature"
msgstr "打印体积温度"

msgctxt "bv_temp_anomaly_limit label"
msgid "Build Volume temperature Limit"
msgstr "打印体积温度限制"

msgctxt "bv_temp_warn_limit label"
msgid "Build Volume temperature Warning"
msgstr "打印体积温度警告"

msgctxt "prime_tower_brim_enable description"
msgid "By enabling this setting, your prime-tower will get a brim, even if the model doesn't. If you want a sturdier base for a high tower, you can increase the base height."
msgstr "启用此设置将为您的 prime tower 添加一个边缘，即使您的模型中原本没有。如果您希望高塔有更坚固的基座，可以增加底座高度。"

msgctxt "center_object label"
msgid "Center Object"
msgstr "中心点"

msgctxt "conical_overhang_enabled description"
msgid "Change the geometry of the printed model such that minimal support is required. Steep overhangs will become shallow overhangs. Overhanging areas will drop down to become more vertical."
msgstr "更改打印模型的几何，以最大程度减少需要的支撑。 陡峭的悬垂物将变浅。 悬垂区域将下降变得更垂直。"

msgctxt "support_structure description"
msgid "Chooses between the techniques available to generate support. \"Normal\" support creates a support structure directly below the overhanging parts and drops those areas straight down. \"Tree\" support creates branches towards the overhanging areas that support the model on the tips of those branches, and allows the branches to crawl around the model to support it from the build plate as much as possible."
msgstr "在可用于产生支撑的方法之间进行选择。“普通”支撑在悬垂部分正下方形成一个支撑结构，并直接垂下这些区域。“树形”支撑形成一些分支，它们朝向在这些分支的尖端上支撑模型的悬垂区域，并使这些分支可缠绕在模型周围以尽可能多地从构建板上支撑它。"

msgctxt "coasting_speed label"
msgid "Coasting Speed"
msgstr "滑行速度"

msgctxt "coasting_volume label"
msgid "Coasting Volume"
msgstr "滑行体积"

msgctxt "coasting_enable description"
msgid "Coasting replaces the last part of an extrusion path with a travel path. The oozed material is used to print the last piece of the extrusion path in order to reduce stringing."
msgstr "滑行会用一个空驶路径替代挤出路径的最后部分。 渗出材料用于打印挤出路径的最后部分，以便减少串接。"

msgctxt "retraction_combing label"
msgid "Combing Mode"
msgstr "梳理模式"

msgctxt "retraction_combing description"
msgid "Combing keeps the nozzle within already printed areas when traveling. This results in slightly longer travel moves but reduces the need for retractions. If combing is off, the material will retract and the nozzle moves in a straight line to the next point. It is also possible to avoid combing over top/bottom skin areas or to only comb within the infill."
msgstr "梳理可在空驶时让喷嘴保持在已打印区域内。这会使空驶距离稍微延长，但可减少回抽需求。如果关闭梳理，则材料将回抽，且喷嘴沿着直线移动到下一个点。也可以避免顶部/底部皮肤区域的梳理或仅在填充物内进行梳理。"

msgctxt "command_line_settings label"
msgid "Command Line Settings"
msgstr "命令行设置"

msgctxt "infill_pattern option concentric"
msgid "Concentric"
msgstr "同心圆"

msgctxt "ironing_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "roofing_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_bottom_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_interface_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "support_roof_pattern option concentric"
msgid "Concentric"
msgstr "同心圆"

msgctxt "top_bottom_pattern option concentric"
msgid "Concentric"
msgstr "同心圆"

msgctxt "top_bottom_pattern_0 option concentric"
msgid "Concentric"
msgstr "同心圆"

msgctxt "support_conical_angle label"
msgid "Conical Support Angle"
msgstr "锥形支撑角度"

msgctxt "support_conical_min_width label"
msgid "Conical Support Minimum Width"
msgstr "锥形支撑最小宽度"

msgctxt "zig_zaggify_infill label"
msgid "Connect Infill Lines"
msgstr "连接填充走线"

msgctxt "connect_infill_polygons label"
msgid "Connect Infill Polygons"
msgstr "连接填充多边形"

msgctxt "zig_zaggify_support label"
msgid "Connect Support Lines"
msgstr "连接支撑线"

msgctxt "support_connect_zigzags label"
msgid "Connect Support ZigZags"
msgstr "连接支撑锯齿形"

msgctxt "connect_skin_polygons label"
msgid "Connect Top/Bottom Polygons"
msgstr "连接顶部/底部多边形"

msgctxt "connect_infill_polygons description"
msgid "Connect infill paths where they run next to each other. For infill patterns which consist of several closed polygons, enabling this setting greatly reduces the travel time."
msgstr "在填充路径互相紧靠运行的地方连接它们。对于包含若干闭合多边形的填充图案，启用此设置可大大减少空驶时间。"

msgctxt "support_connect_zigzags description"
msgid "Connect the ZigZags. This will increase the strength of the zig zag support structure."
msgstr "连接锯齿形。 这将增加锯齿形支撑结构的强度。"

msgctxt "zig_zaggify_support description"
msgid "Connect the ends of the support lines together. Enabling this setting can make your support more sturdy and reduce underextrusion, but it will cost more material."
msgstr "将支撑线尾端连接在一起。启用此设置会让支撑更为牢固并减少挤出不足，但需要更多材料。"

msgctxt "zig_zaggify_infill description"
msgid "Connect the ends where the infill pattern meets the inner wall using a line which follows the shape of the inner wall. Enabling this setting can make the infill adhere to the walls better and reduce the effects of infill on the quality of vertical surfaces. Disabling this setting reduces the amount of material used."
msgstr "使用沿内壁形状的走线连接填充图案与内壁相接的各端。启用此设置会使填充更好地粘着在壁上，减少填充物效果对垂直表面质量的影响。禁用此设置可减少使用的材料量。"

msgctxt "connect_skin_polygons description"
msgid "Connect top/bottom skin paths where they run next to each other. For the concentric pattern enabling this setting greatly reduces the travel time, but because the connections can happen midway over infill this feature can reduce the top surface quality."
msgstr "在顶部/底部皮肤路径互相紧靠运行的地方连接它们。对于同心图案，启用此设置可大大减少空驶时间，但由于连接可在填充中途发生，此功能可能会降低顶部表面质量。"

msgctxt "z_seam_corner description"
msgid "Control whether corners on the model outline influence the position of the seam. None means that corners have no influence on the seam position. Hide Seam makes the seam more likely to occur on an inside corner. Expose Seam makes the seam more likely to occur on an outside corner. Hide or Expose Seam makes the seam more likely to occur at an inside or outside corner. Smart Hiding allows both inside and outside corners, but chooses inside corners more frequently, if appropriate."
msgstr "控制模型轮廓上的角是否影响缝隙的位置。“无”表示各个角不影响缝隙位置。“隐藏缝隙”会使缝隙更可能出现在内侧角上。“外露缝隙”会使缝隙更可能出现在外侧角上。“隐藏或外露缝隙”会使缝隙更可能出现在内侧或外侧角上。“智能隐藏”允许缝隙出现在内侧和外侧角上，如适当，会更多地出现在内侧角上。"

msgctxt "infill_multiplier description"
msgid "Convert each infill line to this many lines. The extra lines do not cross over each other, but avoid each other. This makes the infill stiffer, but increases print time and material usage."
msgstr "将每个填充走线转换成这种多重走线。额外走线互相不交叉，而是互相避开。这使得填充更严格，但会增加打印时间和材料使用。"

msgctxt "machine_nozzle_cool_down_speed label"
msgid "Cool Down Speed"
msgstr "冷却速度"

msgctxt "cooling description"
msgid "Cooling"
msgstr "冷却"

msgctxt "cooling label"
msgid "Cooling"
msgstr "冷却"

msgctxt "infill_pattern option cross"
msgid "Cross"
msgstr "交叉"

msgctxt "support_pattern option cross"
msgid "Cross"
msgstr "交叉"

msgctxt "infill_pattern option cross_3d"
msgid "Cross 3D"
msgstr "交叉 3D"

msgctxt "cross_infill_pocket_size label"
msgid "Cross 3D Pocket Size"
msgstr "交叉 3D 气槽大小"

msgctxt "cross_support_density_image label"
msgid "Cross Fill Density Image for Support"
msgstr "交叉填充密度图象"

msgctxt "cross_infill_density_image label"
msgid "Cross Infill Density Image"
msgstr "交叉加密图像密度"

msgctxt "material_crystallinity label"
msgid "Crystalline Material"
msgstr "晶体材料"

msgctxt "infill_pattern option cubic"
msgid "Cubic"
msgstr "立方体"

msgctxt "infill_pattern option cubicsubdiv"
msgid "Cubic Subdivision"
msgstr "立方体分区"

msgctxt "sub_div_rad_add label"
msgid "Cubic Subdivision Shell"
msgstr "立方体分区外壳"

msgctxt "cutting_mesh label"
msgid "Cutting Mesh"
msgstr "切割网格"

msgctxt "material_flow_temp_graph description"
msgid "Data linking material flow (in mm3 per second) to temperature (degrees Celsius)."
msgstr "数据连接材料流量（mm3/s）到温度（摄氏度）。"

msgctxt "machine_acceleration label"
msgid "Default Acceleration"
msgstr "默认加速度"

msgctxt "default_material_bed_temperature label"
msgid "Default Build Plate Temperature"
msgstr "默认打印平台温度"

msgctxt "machine_max_jerk_e label"
msgid "Default Filament Jerk"
msgstr "默认挤出电机 Jerk"

msgctxt "default_material_print_temperature label"
msgid "Default Printing Temperature"
msgstr "默认打印温度"

msgctxt "machine_max_jerk_xy label"
msgid "Default X-Y Jerk"
msgstr "默认 X-Y 平面抖动速度（Jerk）"

msgctxt "machine_max_jerk_z label"
msgid "Default Z Jerk"
msgstr "默认 Z 轴抖动速度（Jerk）"

msgctxt "machine_max_jerk_xy description"
msgid "Default jerk for movement in the horizontal plane."
msgstr "水平面移动的默认抖动速度。"

msgctxt "machine_max_jerk_z description"
msgid "Default jerk for the motor of the Z-direction."
msgstr "Z 轴方向电机的默认抖动速度。"

msgctxt "machine_max_jerk_e description"
msgid "Default jerk for the motor of the filament."
msgstr "耗材电机的默认抖动速度。"

msgctxt "bridge_settings_enabled description"
msgid "Detect bridges and modify print speed, flow and fan settings while bridges are printed."
msgstr "在打印连桥时，检测连桥并修改打印速度、流量和风扇设置。"

msgctxt "inset_direction description"
msgid "Determines the order in which walls are printed. Printing outer walls earlier helps with dimensional accuracy, as faults from inner walls cannot propagate to the outside. However printing them later allows them to stack better when overhangs are printed. When there is an uneven amount of total innner walls, the 'center last line' is always printed last."
msgstr "确定打印壁的顺序。先打印外壁有助于提高尺寸精度，因为内壁的误差不会传播到外壁。不过，在打印悬垂对象时，后打印外壁可以实现更好的堆叠。当总内壁数量不均匀时，“中心最后线”总是最后打印。"

msgctxt "infill_mesh_order description"
msgid "Determines the priority of this mesh when considering multiple overlapping infill meshes. Areas where multiple infill meshes overlap will take on the settings of the mesh with the highest rank. An infill mesh with a higher rank will modify the infill of infill meshes with lower rank and normal meshes."
msgstr "在考虑多个重叠的填充网格时确定此网格的优先级。其中有多个填充网格重叠的区域将采用等级最高的网格的设置。具有较高等级的填充网格将修改具有较低等级的填充网格和普通网格的填充。"

msgctxt "lightning_infill_support_angle description"
msgid "Determines when a lightning infill layer has to support anything above it. Measured in the angle given the thickness of a layer."
msgstr "决定闪电形填充层何时必须支撑其上方的任何物体。在给定的层厚度下测得的角度。"

msgctxt "lightning_infill_overhang_angle description"
msgid "Determines when a lightning infill layer has to support the model above it. Measured in the angle given the thickness."
msgstr "决定闪电形填充层何时必须支撑其上方的模型。在给定的厚度下测得的角度。"

msgctxt "material_diameter label"
msgid "Diameter"
msgstr "直径"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model label"
msgid "Diameter Increase To Model"
msgstr "扩大直径以匹配模型"

msgctxt "support_tree_bp_diameter description"
msgid "Diameter every branch tries to achieve when reaching the buildplate. Improves bed adhesion."
msgstr "接触打印平台时，每个分支可能达到的直径。提高床附着力。"

msgctxt "adhesion_type description"
msgid "Different options that help to improve both priming your extrusion and adhesion to the build plate. Brim adds a single layer flat area around the base of your model to prevent warping. Raft adds a thick grid with a roof below the model. Skirt is a line printed around the model, but not connected to the model."
msgstr "帮助改善挤出装填以及与打印平台附着的不同选项。 Brim 会在模型基座周围添加单层平面区域，以防止卷翘。 Raft 会在模型下添加一个有顶板的厚网格。 Skirt 是在模型四周打印的一条线，但并不与模型连接。"

msgctxt "machine_disallowed_areas label"
msgid "Disallowed Areas"
msgstr "不允许区域"

msgctxt "infill_line_distance description"
msgid "Distance between the printed infill lines. This setting is calculated by the infill density and the infill line width."
msgstr "打印填充走线之间的距离。 该设置是通过填充密度和填充线宽度计算。"

msgctxt "support_initial_layer_line_distance description"
msgid "Distance between the printed initial layer support structure lines. This setting is calculated by the support density."
msgstr "已打印起始层支撑结构走线之间的距离。该设置通过支撑密度计算。"

msgctxt "support_bottom_line_distance description"
msgid "Distance between the printed support floor lines. This setting is calculated by the Support Floor Density, but can be adjusted separately."
msgstr "已打印支撑底板走线之间的距离。 该设置是通过支撑底板密度计算，但可以单独调整。"

msgctxt "support_roof_line_distance description"
msgid "Distance between the printed support roof lines. This setting is calculated by the Support Roof Density, but can be adjusted separately."
msgstr "已打印支撑顶板走线之间的距离。 该设置是通过支撑顶板密度计算，但可以单独调整。"

msgctxt "support_line_distance description"
msgid "Distance between the printed support structure lines. This setting is calculated by the support density."
msgstr "已打印支撑结构走线之间的距离。 该设置通过支撑密度计算。"

msgctxt "support_bottom_distance description"
msgid "Distance from the print to the bottom of the support. Note that this is rounded up to the next layer height."
msgstr "从打印物到支撑底部的距离。注意这个会上调到下一个层高。"

msgctxt "support_top_distance description"
msgid "Distance from the top of the support to the print."
msgstr "从支撑顶部到打印品的距离。"

msgctxt "support_z_distance description"
msgid "Distance from the top/bottom of the support structure to the print. This gap provides clearance to remove the supports after the model is printed. The topmost support layer below the model might be a fraction of regular layers."
msgstr "支撑结构顶部/底部到打印物的距离。这个间隙提供了清除模型打印后支撑的空间。模型下方的最顶层支撑层可能是常规层的一小部分。"

msgctxt "infill_wipe_dist description"
msgid "Distance of a travel move inserted after every infill line, to make the infill stick to the walls better. This option is similar to infill overlap, but without extrusion and only on one end of the infill line."
msgstr "每条填充走线后插入的空驶距离，让填充物更好地粘着到壁上。 此选项与填充重叠类似，但没有挤出，且仅位于填充走线的一端。"

msgctxt "wall_0_wipe_dist description"
msgid "Distance of a travel move inserted after the outer wall, to hide the Z seam better."
msgstr "插入外壁后的空驶距离，旨在更好地隐藏 Z 缝。"

msgctxt "draft_shield_dist description"
msgid "Distance of the draft shield from the print, in the X/Y directions."
msgstr "防风罩在 X/Y 方向与打印品的距离。"

msgctxt "ooze_shield_dist description"
msgid "Distance of the ooze shield from the print, in the X/Y directions."
msgstr "渗出罩在 X/Y 方向距打印品的距离。"

msgctxt "support_xy_distance_overhang description"
msgid "Distance of the support structure from the overhang in the X/Y directions."
msgstr "支撑结构在 X/Y 方向距悬垂的距离。"

msgctxt "support_xy_distance description"
msgid "Distance of the support structure from the print in the X/Y directions."
msgstr "支撑结构在 X/Y 方向距打印品的距离。"

msgctxt "meshfix_fluid_motion_shift_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "移动距离点，使路径平滑"

msgctxt "meshfix_fluid_motion_small_distance description"
msgid "Distance points are shifted to smooth the path"
msgstr "移动距离点，使路径平滑"

msgctxt "min_infill_area description"
msgid "Don't generate areas of infill smaller than this (use skin instead)."
msgstr "不要生成小于此面积的填充区域（使用皮肤取代）。"

msgctxt "draft_shield_height label"
msgid "Draft Shield Height"
msgstr "防风罩高度"

msgctxt "draft_shield_height_limitation label"
msgid "Draft Shield Limitation"
msgstr "防风罩限制"

msgctxt "draft_shield_dist label"
msgid "Draft Shield X/Y Distance"
msgstr "防风罩 X/Y 距离"

msgctxt "support_mesh_drop_down label"
msgid "Drop Down Support Mesh"
msgstr "下拉式支撑网格"

msgctxt "dual label"
msgid "Dual Extrusion"
msgstr "双重挤出"

msgctxt "machine_shape option elliptic"
msgid "Elliptic"
msgstr "类圆形"

msgctxt "acceleration_enabled label"
msgid "Enable Acceleration Control"
msgstr "启用加速度控制"

msgctxt "bridge_settings_enabled label"
msgid "Enable Bridge Settings"
msgstr "启用连桥设置"

msgctxt "coasting_enable label"
msgid "Enable Coasting"
msgstr "启用滑行"

msgctxt "support_conical_enabled label"
msgid "Enable Conical Support"
msgstr "启用锥形支撑"

msgctxt "draft_shield_enabled label"
msgid "Enable Draft Shield"
msgstr "启用防风罩"

msgctxt "meshfix_fluid_motion_enabled label"
msgid "Enable Fluid Motion"
msgstr "启用流体运动"

msgctxt "ironing_enabled label"
msgid "Enable Ironing"
msgstr "启用熨平"

msgctxt "jerk_enabled label"
msgid "Enable Jerk Control"
msgstr "启用抖动速度控制"

msgctxt "machine_nozzle_temp_enabled label"
msgid "Enable Nozzle Temperature Control"
msgstr "启用喷嘴温度控制"

msgctxt "ooze_shield_enabled label"
msgid "Enable Ooze Shield"
msgstr "启用渗出罩"

msgctxt "prime_blob_enable label"
msgid "Enable Prime Blob"
msgstr "启用装填光点"

msgctxt "prime_tower_enable label"
msgid "Enable Prime Tower"
msgstr "启用装填塔"

msgctxt "cool_fan_enabled label"
msgid "Enable Print Cooling"
msgstr "开启打印冷却"

msgctxt "ppr_enable label"
msgid "Enable Print Process Reporting"
msgstr "启用打印过程报告"

msgctxt "retraction_enable label"
msgid "Enable Retraction"
msgstr "启用回抽"

msgctxt "support_brim_enable label"
msgid "Enable Support Brim"
msgstr "启用支撑 Brim"

msgctxt "support_bottom_enable label"
msgid "Enable Support Floor"
msgstr "启用支撑底板"

msgctxt "support_interface_enable label"
msgid "Enable Support Interface"
msgstr "启用支撑接触面"

msgctxt "support_roof_enable label"
msgid "Enable Support Roof"
msgstr "启用支撑顶板"

msgctxt "acceleration_travel_enabled label"
msgid "Enable Travel Acceleration"
msgstr "启用空驶加速度"

msgctxt "jerk_travel_enabled label"
msgid "Enable Travel Jerk"
msgstr "启用空驶抖动速度"

msgctxt "ooze_shield_enabled description"
msgid "Enable exterior ooze shield. This will create a shell around the model which is likely to wipe a second nozzle if it's at the same height as the first nozzle."
msgstr "启用外部渗出罩。 这将在模型周围创建一个外壳，如果与第一个喷嘴处于相同的高度，则可能会擦拭第二个喷嘴。"

msgctxt "ppr_enable description"
msgid "Enable print process reporting for setting threshold values for possible fault detection."
msgstr "启用打印过程报告以设置可能的故障检测的阈值。"

msgctxt "small_skin_on_surface description"
msgid "Enable small (up to 'Small Top/Bottom Width') regions on the topmost skinned layer (exposed to air) to be filled with walls instead of the default pattern."
msgstr "使最顶层蒙皮图层（暴露在空气中）的小区域（最多“小顶部/底部宽度”）用墙壁填充，而不是默认图案。"

msgctxt "jerk_enabled description"
msgid "Enables adjusting the jerk of print head when the velocity in the X or Y axis changes. Increasing the jerk can reduce printing time at the cost of print quality."
msgstr "启用当 X 或 Y 轴的速度变化时调整打印头的抖动速度。 提高抖动速度可以通过以打印质量为代价来缩短打印时间。"

msgctxt "acceleration_enabled description"
msgid "Enables adjusting the print head acceleration. Increasing the accelerations can reduce printing time at the cost of print quality."
msgstr "启用调整打印头加速度。 提高加速度可以通过以打印质量为代价来缩短打印时间。"

msgctxt "cool_fan_enabled description"
msgid "Enables the print cooling fans while printing. The fans improve print quality on layers with short layer times and bridging / overhangs."
msgstr "打印时启用打印冷却风扇。 风扇可以在层时间较短和有桥接/悬垂的层上提高打印质量。"

msgctxt "machine_end_gcode label"
msgid "End G-code"
msgstr "结束 G-code"

msgctxt "material_end_of_filament_purge_length label"
msgid "End of Filament Purge Length"
msgstr "耗材末端清除长度"

msgctxt "material_end_of_filament_purge_speed label"
msgid "End of Filament Purge Speed"
msgstr "耗材末端清除速度"

msgctxt "brim_replaces_support description"
msgid "Enforce brim to be printed around the model even if that space would otherwise be occupied by support. This replaces some regions of the first layer of support by brim regions."
msgstr "强制围绕模型打印 Brim，即使该空间本该由支撑占据。此操作会将第一层的某些支撑区域替换为 Brim 区域。"

msgctxt "brim_location option everywhere"
msgid "Everywhere"
msgstr "各处"

msgctxt "support_type option everywhere"
msgid "Everywhere"
msgstr "全部支撑"

msgctxt "slicing_tolerance option exclusive"
msgid "Exclusive"
msgstr "不包含"

msgctxt "experimental label"
msgid "Experimental"
msgstr "实验性"

msgctxt "z_seam_corner option z_seam_corner_outer"
msgid "Expose Seam"
msgstr "外露缝隙"

msgctxt "meshfix_extensive_stitching label"
msgid "Extensive Stitching"
msgstr "广泛缝合"

msgctxt "meshfix_extensive_stitching description"
msgid "Extensive stitching tries to stitch up open holes in the mesh by closing the hole with touching polygons. This option can introduce a lot of processing time."
msgstr "广泛缝合尝试通过接触多边形来闭合孔洞，以此缝合网格中的开孔。 此选项可能会产生大量的处理时间。"

msgctxt "infill_wall_line_count label"
msgid "Extra Infill Wall Count"
msgstr "额外填充壁计数"

msgctxt "skin_outline_count label"
msgid "Extra Skin Wall Count"
msgstr "额外皮肤壁计数"

msgctxt "switch_extruder_extra_prime_amount description"
msgid "Extra material to prime after nozzle switching."
msgstr "喷嘴切换后的额外装填材料。"

msgctxt "extruder_prime_pos_x label"
msgid "Extruder Prime X Position"
msgstr "挤出机 X 轴坐标"

msgctxt "extruder_prime_pos_y label"
msgid "Extruder Prime Y Position"
msgstr "挤出机 Y 轴起始位置"

msgctxt "extruder_prime_pos_z label"
msgid "Extruder Prime Z Position"
msgstr "挤出机初始 Z 轴位置"

msgctxt "machine_extruders_share_heater label"
msgid "Extruders Share Heater"
msgstr "挤出器共用加热器"

msgctxt "machine_extruders_share_nozzle label"
msgid "Extruders Share Nozzle"
msgstr "挤出器共用喷嘴"

msgctxt "material_extrusion_cool_down_speed label"
msgid "Extrusion Cool Down Speed Modifier"
msgstr "挤出冷却速度调节器"

msgctxt "speed_equalize_flow_width_factor description"
msgid "Extrusion width based correction factor on the speed. At 0% the movement speed is kept constant at the Print Speed. At 100% the movement speed is adjusted so that the flow (in mm³/s) is kept constant, i.e. lines half the normal Line Width are printed twice as fast and lines twice as wide are printed half as fast. A value larger than 100% can help to compensate for the higher pressure required to extrude wide lines."
msgstr "基于速度校正系数的挤出宽度。在 0% 时，移动速度保持在打印速度不变。在 100% 时，将调整移动速度以使流量（以 mm³/s 为单位）保持恒定，即以两倍的速度打印正常线宽一半的线条，以一半的速度打印两倍宽的线条。大于 100% 的值有助于为挤出宽线所需的更高压力提供补偿。"

msgctxt "cool_fan_speed label"
msgid "Fan Speed"
msgstr "风扇速度"

msgctxt "support_fan_enable label"
msgid "Fan Speed Override"
msgstr "风扇速度覆盖"

msgctxt "small_feature_max_length description"
msgid "Feature outlines that are shorter than this length will be printed using Small Feature Speed."
msgstr "将使用微小特征速度打印小于此长度的特征轮廓。"

msgctxt "experimental description"
msgid "Features that haven't completely been fleshed out yet."
msgstr "尚未完全充实的功能。"

msgctxt "machine_feeder_wheel_diameter label"
msgid "Feeder Wheel Diameter"
msgstr "进料装置驱动轮的直径"

msgctxt "material_final_print_temperature label"
msgid "Final Printing Temperature"
msgstr "最终打印温度"

msgctxt "machine_firmware_retract label"
msgid "Firmware Retraction"
msgstr "固件收回"

msgctxt "support_extruder_nr_layer_0 label"
msgid "First Layer Support Extruder"
msgstr "第一层支撑挤出机"

msgctxt "material_flow label"
msgid "Flow"
msgstr "流量"

msgctxt "speed_equalize_flow_width_factor label"
msgid "Flow Equalization Ratio"
msgstr "流量均衡比"

msgctxt "flow_anomaly_limit label"
msgid "Flow Limit"
msgstr "挤出量限制"

msgctxt "flow_rate_extrusion_offset_factor label"
msgid "Flow Rate Compensation Factor"
msgstr "流量补偿因子"

msgctxt "flow_rate_max_extrusion_offset label"
msgid "Flow Rate Compensation Max Extrusion Offset"
msgstr "流量补偿最大挤出偏移值"

msgctxt "material_flow_temp_graph label"
msgid "Flow Temperature Graph"
msgstr "流量温度图"

msgctxt "flow_warn_limit label"
msgid "Flow Warning"
msgstr "挤出量警告"

msgctxt "material_flow_layer_0 description"
msgid "Flow compensation for the first layer: the amount of material extruded on the initial layer is multiplied by this value."
msgstr "第一层的流量补偿：起始层挤出的材料量乘以此值。"

msgctxt "skin_material_flow_layer_0 description"
msgid "Flow compensation on bottom lines of the first layer"
msgstr "第一层底部走线的流量补偿"

msgctxt "infill_material_flow description"
msgid "Flow compensation on infill lines."
msgstr "填充走线的流量补偿。"

msgctxt "support_interface_material_flow description"
msgid "Flow compensation on lines of support roof or floor."
msgstr "支撑顶板或底板走线的流量补偿。"

msgctxt "roofing_material_flow description"
msgid "Flow compensation on lines of the areas at the top of the print."
msgstr "打印顶部区域走线的流量补偿。"

msgctxt "prime_tower_flow description"
msgid "Flow compensation on prime tower lines."
msgstr "装填塔走线的流量补偿。"

msgctxt "skirt_brim_material_flow description"
msgid "Flow compensation on skirt or brim lines."
msgstr "裙边或边缘走线的流量补偿。"

msgctxt "support_bottom_material_flow description"
msgid "Flow compensation on support floor lines."
msgstr "支撑底板走线的流量补偿。"

msgctxt "support_roof_material_flow description"
msgid "Flow compensation on support roof lines."
msgstr "支撑顶板走线的流量补偿。"

msgctxt "support_material_flow description"
msgid "Flow compensation on support structure lines."
msgstr "支撑结构走线的流量补偿。"

msgctxt "wall_0_material_flow_layer_0 description"
msgid "Flow compensation on the outermost wall line of the first layer."
msgstr "第一层最外壁走线的流量补偿。"

msgctxt "wall_0_material_flow description"
msgid "Flow compensation on the outermost wall line."
msgstr "最外壁走线的流量补偿。"

msgctxt "wall_0_material_flow_roofing description"
msgid "Flow compensation on the top surface outermost wall line."
msgstr "在顶面最外墙线挤出量补偿。"

msgctxt "wall_x_material_flow_roofing description"
msgid "Flow compensation on top surface wall lines for all wall lines except the outermost one."
msgstr "除最外层墙线之外,在所有墙线的顶面墙线挤出量补偿。"

msgctxt "skin_material_flow description"
msgid "Flow compensation on top/bottom lines."
msgstr "顶部/底部走线的流量补偿。"

msgctxt "wall_x_material_flow_layer_0 description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one, but only for the first layer"
msgstr "适用于所有壁走线（最外壁走线除外）的流量补偿，但仅适用于第一层。"

msgctxt "wall_x_material_flow description"
msgid "Flow compensation on wall lines for all wall lines except the outermost one."
msgstr "适用于所有壁走线（最外壁走线除外）的流量补偿。"

msgctxt "wall_material_flow description"
msgid "Flow compensation on wall lines."
msgstr "壁走线的流量补偿。"

msgctxt "material_flow description"
msgid "Flow compensation: the amount of material extruded is multiplied by this value."
msgstr "流量补偿：挤出的材料量乘以此值。"

msgctxt "meshfix_fluid_motion_angle label"
msgid "Fluid Motion Angle"
msgstr "流体运动角"

msgctxt "meshfix_fluid_motion_shift_distance label"
msgid "Fluid Motion Shift Distance"
msgstr "流体运动移动距离"

msgctxt "meshfix_fluid_motion_small_distance label"
msgid "Fluid Motion Small Distance"
msgstr "流体运动小距离"

msgctxt "material_flush_purge_length label"
msgid "Flush Purge Length"
msgstr "冲洗清除长度"

msgctxt "material_flush_purge_speed label"
msgid "Flush Purge Speed"
msgstr "冲洗清除速度"

msgctxt "min_wall_line_width description"
msgid "For thin structures around once or twice the nozzle size, the line widths need to be altered to adhere to the thickness of the model. This setting controls the minimum line width allowed for the walls. The minimum line widths inherently also determine the maximum line widths, since we transition from N to N+1 walls at some geometry thickness where the N walls are wide and the N+1 walls are narrow. The widest possible wall line is twice the Minimum Wall Line Width."
msgstr "对于一倍或两倍于喷嘴孔径的薄结构，需要更改走线宽度以遵循模型的厚度。此设置控制壁允许的最小走线宽度。同样，最小走线宽度内在地决定了最大走线宽度，因为我们在某些几何厚度中从 N 壁过渡到 N+1 壁时，N 壁宽而 N+1 壁窄。允许的最大壁走线宽度是最小壁走线宽度的两倍。"

msgctxt "z_seam_position option front"
msgid "Front"
msgstr "前方"

msgctxt "z_seam_position option frontleft"
msgid "Front Left"
msgstr "左前方"

msgctxt "z_seam_position option frontright"
msgid "Front Right"
msgstr "右前方"

msgctxt "draft_shield_height_limitation option full"
msgid "Full"
msgstr "完整"

msgctxt "magic_fuzzy_skin_enabled label"
msgid "Fuzzy Skin"
msgstr "模糊皮肤"

msgctxt "magic_fuzzy_skin_point_density label"
msgid "Fuzzy Skin Density"
msgstr "模糊皮肤密度"

msgctxt "magic_fuzzy_skin_outside_only label"
msgid "Fuzzy Skin Outside Only"
msgstr "仅外部模糊皮肤"

msgctxt "magic_fuzzy_skin_point_dist label"
msgid "Fuzzy Skin Point Distance"
msgstr "模糊皮肤点距离"

msgctxt "magic_fuzzy_skin_thickness label"
msgid "Fuzzy Skin Thickness"
msgstr "模糊皮肤厚度"

msgctxt "machine_gcode_flavor label"
msgid "G-code Flavor"
msgstr "G-code 风格"

msgctxt "machine_end_gcode description"
msgid "G-code commands to be executed at the very end - separated by \n."
msgstr "在结束前执行的 G-code 命令 - 以 "
" 分行。"

msgctxt "machine_start_gcode description"
msgid "G-code commands to be executed at the very start - separated by \n."
msgstr "在开始时执行的 G-code 命令 - 以 "
" 分行。"

msgctxt "material_guid description"
msgid "GUID of the material. This is set automatically."
msgstr "材料 GUID，此项为自动设置。"

msgctxt "gantry_height label"
msgid "Gantry Height"
msgstr "十字轴高度"

msgctxt "interlocking_enable label"
msgid "Generate Interlocking Structure"
msgstr "生成互锁结构"

msgctxt "support_enable label"
msgid "Generate Support"
msgstr "生成支撑"

msgctxt "support_brim_enable description"
msgid "Generate a brim within the support infill regions of the first layer. This brim is printed underneath the support, not around it. Enabling this setting increases the adhesion of support to the build plate."
msgstr "在第一层的支撑填充区域内生成一个 Brim。此 Brim 在支撑下方打印，而非周围。启用此设置会增强支撑与打印平台的附着。"

msgctxt "support_interface_enable description"
msgid "Generate a dense interface between the model and the support. This will create a skin at the top of the support on which the model is printed and at the bottom of the support, where it rests on the model."
msgstr "在模型和支撑之间生成一个密集的接触面。 这会在打印模型所在的支撑顶部和模型停放的支撑底部创建一个皮肤。"

msgctxt "support_bottom_enable description"
msgid "Generate a dense slab of material between the bottom of the support and the model. This will create a skin between the model and support."
msgstr "在支撑底部和模型之间生成一个密集的材料板。 这会在模型和支撑之间形成一个皮肤。"

msgctxt "support_roof_enable description"
msgid "Generate a dense slab of material between the top of support and the model. This will create a skin between the model and support."
msgstr "在支撑顶部和模型之间生成一个密集的材料板。 这会在模型和支撑之间形成一个皮肤。"

msgctxt "support_enable description"
msgid "Generate structures to support parts of the model which have overhangs. Without these structures, such parts would collapse during printing."
msgstr "在模型的悬垂（Overhangs）部分生成支撑结构。若不这样做，这些部分在打印时将倒塌。"

msgctxt "machine_buildplate_type option glass"
msgid "Glass"
msgstr "玻璃"

msgctxt "ironing_enabled description"
msgid "Go over the top surface one additional time, but this time extruding very little material. This is meant to melt the plastic on top further, creating a smoother surface. The pressure in the nozzle chamber is kept high so that the creases in the surface are filled with material."
msgstr "再次经过顶部表面，但这次挤出的材料非常少。这意味着将进一步熔化顶部的塑料，形成更平滑的表面。喷嘴室中的压力保持很高，确保表面折痕中也能填充材料，以保证细节。"

msgctxt "gradual_infill_step_height label"
msgid "Gradual Infill Step Height"
msgstr "渐进填充步阶高度"

msgctxt "gradual_infill_steps label"
msgid "Gradual Infill Steps"
msgstr "渐进填充步阶"

msgctxt "gradual_support_infill_step_height label"
msgid "Gradual Support Infill Step Height"
msgstr "渐进支撑填充步阶高度"

msgctxt "gradual_support_infill_steps label"
msgid "Gradual Support Infill Steps"
msgstr "渐进支撑填充步阶"

msgctxt "cool_min_temperature description"
msgid "Gradually reduce to this temperature when printing at reduced speeds because of minimum layer time."
msgstr "当由于最短印层时间而导致打印速度降低时，温度将逐渐降低至该温度。"

msgctxt "infill_pattern option grid"
msgid "Grid"
msgstr "网格"

msgctxt "support_bottom_pattern option grid"
msgid "Grid"
msgstr "网格"

msgctxt "support_interface_pattern option grid"
msgid "Grid"
msgstr "网格"

msgctxt "support_pattern option grid"
msgid "Grid"
msgstr "网格"

msgctxt "support_roof_pattern option grid"
msgid "Grid"
msgstr "网格"

msgctxt "machine_gcode_flavor option Griffin"
msgid "Griffin"
msgstr "Griffin"

msgctxt "group_outer_walls label"
msgid "Group Outer Walls"
msgstr "外墙编组"

msgctxt "infill_pattern option gyroid"
msgid "Gyroid"
msgstr "螺旋二十四面体"

msgctxt "support_pattern option gyroid"
msgid "Gyroid"
msgstr "螺旋二十四面体"

msgctxt "machine_heated_build_volume label"
msgid "Has Build Volume Temperature Stabilization"
msgstr "具有构建体积温度稳定性"

msgctxt "machine_heated_bed label"
msgid "Has Heated Build Plate"
msgstr "有加热打印平台"

msgctxt "machine_nozzle_heat_up_speed label"
msgid "Heat Up Speed"
msgstr "升温速度"

msgctxt "machine_heat_zone_length label"
msgid "Heat Zone Length"
msgstr "加热区长度"

msgctxt "draft_shield_height description"
msgid "Height limitation of the draft shield. Above this height no draft shield will be printed."
msgstr "防风罩的高度限制。 在此高度以上不会打印任何防风罩。"

msgctxt "z_seam_corner option z_seam_corner_inner"
msgid "Hide Seam"
msgstr "隐藏缝隙"

msgctxt "z_seam_corner option z_seam_corner_any"
msgid "Hide or Expose Seam"
msgstr "隐藏或外露缝隙"

msgctxt "hole_xy_offset label"
msgid "Hole Horizontal Expansion"
msgstr "孔洞水平扩展"

msgctxt "hole_xy_offset_max_diameter label"
msgid "Hole Horizontal Expansion Max Diameter"
msgstr "孔洞水平扩展最大直径"

msgctxt "small_hole_max_size description"
msgid "Holes and part outlines with a diameter smaller than this will be printed using Small Feature Speed."
msgstr "将使用微小特征速度打印直径小于此尺寸的孔和零件轮廓。"

msgctxt "xy_offset label"
msgid "Horizontal Expansion"
msgstr "水平扩展"

msgctxt "material_shrinkage_percentage_xy label"
msgid "Horizontal Scaling Factor Shrinkage Compensation"
msgstr "水平缩放因子收缩补偿"

msgctxt "material_break_preparation_retracted_position description"
msgid "How far the filament can be stretched before it breaks, while heated."
msgstr "耗材受热拉伸但不断裂的极限长度。"

msgctxt "material_anti_ooze_retracted_position description"
msgid "How far the material needs to be retracted before it stops oozing."
msgstr "材料在停止渗出前所需的回抽长度。"

msgctxt "flow_rate_extrusion_offset_factor description"
msgid "How far to move the filament in order to compensate for changes in flow rate, as a percentage of how far the filament would move in one second of extrusion."
msgstr "为补偿流量变化而将线材移动的距离，在挤出一秒钟的情况下占线材移动距离的百分比。"

msgctxt "material_break_retracted_position description"
msgid "How far to retract the filament in order to break it cleanly."
msgstr "为完全脱落耗材而抽回耗材的长度。"

msgctxt "material_break_preparation_speed description"
msgid "How fast the filament needs to be retracted just before breaking it off in a retraction."
msgstr "耗材在回抽过程中恰好折断的回抽速率。"

msgctxt "material_anti_ooze_retraction_speed description"
msgid "How fast the material needs to be retracted during a filament switch to prevent oozing."
msgstr "在耗材用于防渗出过程中材料所需的回抽速率。"

msgctxt "material_end_of_filament_purge_speed description"
msgid "How fast to prime the material after replacing an empty spool with a fresh spool of the same material."
msgstr "将空线轴替换为使用相同材料的新线轴后，装填材料的速度如何。"

msgctxt "material_flush_purge_speed description"
msgid "How fast to prime the material after switching to a different material."
msgstr "切换到其他材料后，装填材料的速度如何。"

msgctxt "material_maximum_park_duration description"
msgid "How long the material can be kept out of dry storage safely."
msgstr "材料能在干燥存储区之外安全存放多长时间。"

msgctxt "machine_steps_per_mm_x description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the X direction."
msgstr "步进电机前进多少步将导致在 X 方向移动一毫米。"

msgctxt "machine_steps_per_mm_y description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Y direction."
msgstr "步进电机前进多少步将导致在 Y 方向移动一毫米。"

msgctxt "machine_steps_per_mm_z description"
msgid "How many steps of the stepper motor will result in one millimeter of movement in the Z direction."
msgstr "步进电机前进多少步将导致在 Z 方向移动一毫米。"

msgctxt "machine_steps_per_mm_e description"
msgid "How many steps of the stepper motors will result in moving the feeder wheel by one millimeter around its circumference."
msgstr "步进电机前进多少步将导致进料器轮绕其周长移动一毫米。"

msgctxt "material_end_of_filament_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when replacing an empty spool with a fresh spool of the same material."
msgstr "将空线轴替换为使用相同材料的新线轴后，需要使用多少材料从喷嘴中清除之前的材料（以长丝长度计）。"

msgctxt "material_flush_purge_length description"
msgid "How much material to use to purge the previous material out of the nozzle (in length of filament) when switching to a different material."
msgstr "切换到其他材料时，需要使用多少材料从喷嘴中清除之前的材料（以长丝长度计）。"

msgctxt "machine_extruders_shared_nozzle_initial_retraction description"
msgid "How much the filament of each extruder is assumed to have been retracted from the shared nozzle tip at the completion of the printer-start gcode script; the value should be equal to or greater than the length of the common part of the nozzle's ducts."
msgstr "假定在打印机启动 gcode 脚本完成后，每个挤出器的细丝从共用喷嘴头缩回多少；该值应等于或大于喷嘴导管公共部分的长度。"

msgctxt "support_interface_priority description"
msgid "How support interface and support will interact when they overlap. Currently only implemented for support roof."
msgstr "重叠时支撑接触面和支撑的交互方式。目前仅对支撑顶板实施。"

msgctxt "support_tree_min_height_to_model description"
msgid "How tall a branch has to be if it is placed on the model. Prevents small blobs of support. This setting is ignored when a branch is supporting a support roof."
msgstr "将分支放在模型上时，分支的必要高度。防止出现小的支撑光点。分支支撑着支撑顶板时，此设置将被忽略。"

msgctxt "bridge_skin_support_threshold description"
msgid "If a skin region is supported for less than this percentage of its area, print it using the bridge settings. Otherwise it is printed using the normal skin settings."
msgstr "如果受支撑的表面区域小于整个区域的这一百分比，则使用连桥设置打印。否则，使用正常表面设置打印。"

msgctxt "meshfix_fluid_motion_angle description"
msgid "If a toolpath-segment deviates more than this angle from the general motion it is smoothed."
msgstr "如果刀具路径段偏离一般运动的角度大于这个角度，使路径平滑。"

msgctxt "bridge_enable_more_layers description"
msgid "If enabled, the second and third layers above the air are printed using the following settings. Otherwise, those layers are printed using the normal settings."
msgstr "如果启用此选项，则使用以下设置打印净空区域上方第二层和第三层。否则，将使用正常设置打印这些层。"

msgctxt "wall_transition_filter_distance description"
msgid "If it would be transitioning back and forth between different numbers of walls in quick succession, don't transition at all. Remove transitions if they are closer together than this distance."
msgstr "如果要在不同数量的壁之间快速连续地来回过渡，那么根本不要过渡。如果这些过渡的距离之和小于此距离，则移除过渡。"

msgctxt "raft_base_margin description"
msgid "If the raft base is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "如果设置了筏层基段扩展,这将会是模型周围扩展出的打印区域,并同时打印筏层。增加此边距会使筏层更为坚固,但也会使用更多材料并占用更多的打印空间。"

msgctxt "raft_margin description"
msgid "If the raft is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "如果启用了 raft，则这是指也被提供了 raft 的模型周围的额外 raft 区域。 增加此留白将创建强度更大的 raft，但会使用更多材料，为打印品留下的空间更少。"

msgctxt "raft_interface_margin description"
msgid "If the raft middle is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "如果设置了筏层中段扩展,这将会是模型周围扩展出的打印区域,并同时打印筏层。增加此边距会使筏层更为坚固,但也会使用更多材料并占用更多的打印空间。"

msgctxt "raft_surface_margin description"
msgid "If the raft top is enabled, this is the extra raft area around the model which is also given a raft. Increasing this margin will create a stronger raft while using more material and leaving less area for your print."
msgstr "如果设置了筏层顶段扩展,这将会是模型周围扩展出的打印区域,并同时打印筏层。增加此边距会使筏层更为坚固,但也会使用更多材料并占用更多的打印空间。"

msgctxt "meshfix_union_all description"
msgid "Ignore the internal geometry arising from overlapping volumes within a mesh and print the volumes as one. This may cause unintended internal cavities to disappear."
msgstr "忽略由网格内的重叠体积产生的内部几何，并将多个部分作为一个打印。 这可能会导致意外的内部孔洞消失。"

msgctxt "material_bed_temp_prepend label"
msgid "Include Build Plate Temperature"
msgstr "包含打印平台温度"

msgctxt "material_print_temp_prepend label"
msgid "Include Material Temperatures"
msgstr "包含材料温度"

msgctxt "slicing_tolerance option inclusive"
msgid "Inclusive"
msgstr "包含"

msgctxt "infill description"
msgid "Infill"
msgstr "填充"

msgctxt "infill label"
msgid "Infill"
msgstr "填充"

msgctxt "acceleration_infill label"
msgid "Infill Acceleration"
msgstr "填充加速度"

msgctxt "infill_before_walls label"
msgid "Infill Before Walls"
msgstr "先填充物后壁"

msgctxt "infill_sparse_density label"
msgid "Infill Density"
msgstr "填充密度"

msgctxt "infill_extruder_nr label"
msgid "Infill Extruder"
msgstr "填充挤出机"

msgctxt "infill_material_flow label"
msgid "Infill Flow"
msgstr "填充流量"

msgctxt "jerk_infill label"
msgid "Infill Jerk"
msgstr "填充抖动速度"

msgctxt "infill_sparse_thickness label"
msgid "Infill Layer Thickness"
msgstr "填充层厚度"

msgctxt "infill_angles label"
msgid "Infill Line Directions"
msgstr "填充走线方向"

msgctxt "infill_line_distance label"
msgid "Infill Line Distance"
msgstr "填充走线距离"

msgctxt "infill_multiplier label"
msgid "Infill Line Multiplier"
msgstr "填充走线乘数"

msgctxt "infill_line_width label"
msgid "Infill Line Width"
msgstr "走线宽度（填充）"

msgctxt "infill_mesh label"
msgid "Infill Mesh"
msgstr "填充网格"

msgctxt "infill_support_angle label"
msgid "Infill Overhang Angle"
msgstr "填充悬垂角"

msgctxt "infill_overlap_mm label"
msgid "Infill Overlap"
msgstr "填充重叠"

msgctxt "infill_overlap label"
msgid "Infill Overlap Percentage"
msgstr "填充重叠百分比"

msgctxt "infill_pattern label"
msgid "Infill Pattern"
msgstr "填充图案"

msgctxt "speed_infill label"
msgid "Infill Speed"
msgstr "填充速度"

msgctxt "infill_support_enabled label"
msgid "Infill Support"
msgstr "填充支撑"

msgctxt "infill_enable_travel_optimization label"
msgid "Infill Travel Optimization"
msgstr "填充物空驶优化"

msgctxt "infill_wipe_dist label"
msgid "Infill Wipe Distance"
msgstr "填充物擦拭距离"

msgctxt "infill_offset_x label"
msgid "Infill X Offset"
msgstr "填充 X 轴偏移量"

msgctxt "infill_offset_y label"
msgid "Infill Y Offset"
msgstr "填充 Y 轴偏移量"

msgctxt "initial_bottom_layers label"
msgid "Initial Bottom Layers"
msgstr "初始底层数"

msgctxt "cool_fan_speed_0 label"
msgid "Initial Fan Speed"
msgstr "起始风扇速度"

msgctxt "acceleration_layer_0 label"
msgid "Initial Layer Acceleration"
msgstr "起始层加速度"

msgctxt "skin_material_flow_layer_0 label"
msgid "Initial Layer Bottom Flow"
msgstr "起始层底部流量"

msgctxt "support_tree_bp_diameter label"
msgid "Initial Layer Diameter"
msgstr "起始层直径"

msgctxt "material_flow_layer_0 label"
msgid "Initial Layer Flow"
msgstr "起始层流量"

msgctxt "layer_height_0 label"
msgid "Initial Layer Height"
msgstr "起始层高"

msgctxt "xy_offset_layer_0 label"
msgid "Initial Layer Horizontal Expansion"
msgstr "起始层水平扩展"

msgctxt "wall_x_material_flow_layer_0 label"
msgid "Initial Layer Inner Wall Flow"
msgstr "起始层内壁流量"

msgctxt "jerk_layer_0 label"
msgid "Initial Layer Jerk"
msgstr "起始层抖动速度"

msgctxt "initial_layer_line_width_factor label"
msgid "Initial Layer Line Width"
msgstr "起始层走线宽度"

msgctxt "wall_0_material_flow_layer_0 label"
msgid "Initial Layer Outer Wall Flow"
msgstr "起始层外壁流量"

msgctxt "acceleration_print_layer_0 label"
msgid "Initial Layer Print Acceleration"
msgstr "起始层打印加速度"

msgctxt "jerk_print_layer_0 label"
msgid "Initial Layer Print Jerk"
msgstr "起始层打印抖动速度"

msgctxt "speed_print_layer_0 label"
msgid "Initial Layer Print Speed"
msgstr "起始层打印速度"

msgctxt "speed_layer_0 label"
msgid "Initial Layer Speed"
msgstr "起始层速度"

msgctxt "support_initial_layer_line_distance label"
msgid "Initial Layer Support Line Distance"
msgstr "起始层支撑走线距离"

msgctxt "acceleration_travel_layer_0 label"
msgid "Initial Layer Travel Acceleration"
msgstr "起始层空驶加速度"

msgctxt "jerk_travel_layer_0 label"
msgid "Initial Layer Travel Jerk"
msgstr "起始层空驶抖动速度"

msgctxt "speed_travel_layer_0 label"
msgid "Initial Layer Travel Speed"
msgstr "起始层空驶速度"

msgctxt "layer_0_z_overlap label"
msgid "Initial Layer Z Overlap"
msgstr "起始层 Z 重叠"

msgctxt "material_initial_print_temperature label"
msgid "Initial Printing Temperature"
msgstr "起始打印温度"

msgctxt "acceleration_wall_x label"
msgid "Inner Wall Acceleration"
msgstr "内壁加速度"

msgctxt "wall_x_extruder_nr label"
msgid "Inner Wall Extruder"
msgstr "内壁挤出机"

msgctxt "jerk_wall_x label"
msgid "Inner Wall Jerk"
msgstr "内壁抖动速度"

msgctxt "speed_wall_x label"
msgid "Inner Wall Speed"
msgstr "速度（内壁）"

msgctxt "wall_x_material_flow label"
msgid "Inner Wall(s) Flow"
msgstr "内壁流量"

msgctxt "wall_line_width_x label"
msgid "Inner Wall(s) Line Width"
msgstr "走线宽度（内壁）"

msgctxt "wall_0_inset description"
msgid "Inset applied to the path of the outer wall. If the outer wall is smaller than the nozzle, and printed after the inner walls, use this offset to get the hole in the nozzle to overlap with the inner walls instead of the outside of the model."
msgstr "应用在外壁路径上的嵌入。 如果外壁小于喷嘴，并且在内壁之后打印，则使用该偏移量来使喷嘴中的孔与内壁而不是模型外部重叠。"

msgctxt "brim_location option inside"
msgid "Inside Only"
msgstr "仅内侧"

msgctxt "inset_direction option inside_out"
msgid "Inside To Outside"
msgstr "从内到外"

msgctxt "support_interface_priority option interface_lines_overwrite_support_area"
msgid "Interface lines preferred"
msgstr "偏好接触面走线"

msgctxt "support_interface_priority option interface_area_overwrite_support_area"
msgid "Interface preferred"
msgstr "偏好接触面"

msgctxt "prime_tower_mode option interleaved"
msgid "Interleaved"
msgstr "交错的"

msgctxt "interlocking_beam_layer_count label"
msgid "Interlocking Beam Layer Count"
msgstr "互锁梁层数"

msgctxt "interlocking_beam_width label"
msgid "Interlocking Beam Width"
msgstr "互锁梁宽度"

msgctxt "interlocking_boundary_avoidance label"
msgid "Interlocking Boundary Avoidance"
msgstr "互锁边界回避"

msgctxt "interlocking_depth label"
msgid "Interlocking Depth"
msgstr "互锁深度"

msgctxt "interlocking_orientation label"
msgid "Interlocking Structure Orientation"
msgstr "互锁结构方向"

msgctxt "ironing_only_highest_layer label"
msgid "Iron Only Highest Layer"
msgstr "仅熨平最高层"

msgctxt "acceleration_ironing label"
msgid "Ironing Acceleration"
msgstr "熨平加速度"

msgctxt "ironing_flow label"
msgid "Ironing Flow"
msgstr "熨平流量"

msgctxt "ironing_inset label"
msgid "Ironing Inset"
msgstr "熨平嵌入"

msgctxt "jerk_ironing label"
msgid "Ironing Jerk"
msgstr "熨平抖动速度"

msgctxt "ironing_line_spacing label"
msgid "Ironing Line Spacing"
msgstr "熨平走线间距"

msgctxt "ironing_pattern label"
msgid "Ironing Pattern"
msgstr "熨平图案"

msgctxt "speed_ironing label"
msgid "Ironing Speed"
msgstr "熨平速度"

msgctxt "machine_center_is_zero label"
msgid "Is Center Origin"
msgstr "位于中心"

msgctxt "material_is_support_material label"
msgid "Is support material"
msgstr "支撑材料"

msgctxt "material_crystallinity description"
msgid "Is this material the type that breaks off cleanly when heated (crystalline), or is it the type that produces long intertwined polymer chains (non-crystalline)?"
msgstr "该材料为受热后脱落干净的类型（晶体），还是会产生长交织状聚合物链的类型（非晶体）？"

msgctxt "material_is_support_material description"
msgid "Is this material typically used as a support material during printing."
msgstr "这种材料通常被用作打印的支撑材料吗"

msgctxt "magic_fuzzy_skin_outside_only description"
msgid "Jitter only the parts' outlines and not the parts' holes."
msgstr "仅抖动部件的轮廓，而不抖动部件的孔。"

msgctxt "meshfix_keep_open_polygons label"
msgid "Keep Disconnected Faces"
msgstr "保留断开连接的面"

msgctxt "layer_height label"
msgid "Layer Height"
msgstr "层高"

msgctxt "layer_start_x label"
msgid "Layer Start X"
msgstr "层开始 X"

msgctxt "layer_start_y label"
msgid "Layer Start Y"
msgstr "层开始 Y"

msgctxt "raft_base_thickness description"
msgid "Layer thickness of the base raft layer. This should be a thick layer which sticks firmly to the printer build plate."
msgstr "基础 Raft 层的层厚度。 该层应为与打印机打印平台牢固粘着的厚层。"

msgctxt "raft_interface_thickness description"
msgid "Layer thickness of the middle raft layer."
msgstr "中间 Raft 层的层厚度。"

msgctxt "raft_surface_thickness description"
msgid "Layer thickness of the top raft layers."
msgstr "顶部 Raft 层的层厚度。"

msgctxt "support_skip_zag_per_mm description"
msgid "Leave out a connection between support lines once every N millimeter to make the support structure easier to break away."
msgstr "每隔 N 毫米在支撑线之间略去一个连接,让支撑结构更容易脱离。"

msgctxt "z_seam_position option left"
msgid "Left"
msgstr "左侧"

msgctxt "cool_lift_head label"
msgid "Lift Head"
msgstr "打印头提升"

msgctxt "infill_pattern option lightning"
msgid "Lightning"
msgstr "闪电形"

msgctxt "lightning_infill_overhang_angle label"
msgid "Lightning Infill Overhang Angle"
msgstr "闪电形填充悬垂角"

msgctxt "lightning_infill_prune_angle label"
msgid "Lightning Infill Prune Angle"
msgstr "闪电形填充修剪角"

msgctxt "lightning_infill_straightening_angle label"
msgid "Lightning Infill Straightening Angle"
msgstr "闪电形填充矫直角"

msgctxt "lightning_infill_support_angle label"
msgid "Lightning Infill Support Angle"
msgstr "闪电形填充支撑角"

msgctxt "support_tree_limit_branch_reach label"
msgid "Limit Branch Reach"
msgstr "限制分支长度"

msgctxt "support_tree_limit_branch_reach description"
msgid "Limit how far each branch should travel from the point it supports. This can make the support more sturdy, but will increase the amount of branches (and because of that material usage/print time)"
msgstr "限制每个分支从其支撑点移动的距离。这样可以使支撑更坚固，但会增加分支的数量（进而增加材料的使用/打印时间）"

msgctxt "bv_temp_warn_limit description"
msgid "Limit on Build Volume Temperature warning for detection."
msgstr "检测的打印体积温度警告限制。"

msgctxt "bv_temp_anomaly_limit description"
msgid "Limit on Build Volume temperature Anomaly for detection."
msgstr "检测的打印体积温度异常限制。"

msgctxt "print_temp_anomaly_limit description"
msgid "Limit on Print Temperature anomaly for detection."
msgstr "检测的打印温度异常限制。"

msgctxt "print_temp_warn_limit description"
msgid "Limit on Print temperature warning for detection."
msgstr "检测的打印温度警告限制。"

msgctxt "flow_anomaly_limit description"
msgid "Limit on flow anomaly for detection."
msgstr "检测的挤出量异常限制。"

msgctxt "flow_warn_limit description"
msgid "Limit on the flow warning for detection."
msgstr "检测的挤出量警告限制。"

msgctxt "cutting_mesh description"
msgid "Limit the volume of this mesh to within other meshes. You can use this to make certain areas of one mesh print with different settings and with a whole different extruder."
msgstr "将此网格的体积限制在其他网格内。 您可以使用它来制作采用不同的设置以及完全不同的挤出机的网格打印的特定区域。"

msgctxt "draft_shield_height_limitation option limited"
msgid "Limited"
msgstr "有限"

msgctxt "line_width label"
msgid "Line Width"
msgstr "走线宽度"

msgctxt "infill_pattern option lines"
msgid "Lines"
msgstr "直线"

msgctxt "roofing_pattern option lines"
msgid "Lines"
msgstr "走线"

msgctxt "support_bottom_pattern option lines"
msgid "Lines"
msgstr "走线"

msgctxt "support_interface_pattern option lines"
msgid "Lines"
msgstr "走线"

msgctxt "support_pattern option lines"
msgid "Lines"
msgstr "走线"

msgctxt "support_roof_pattern option lines"
msgid "Lines"
msgstr "直线"

msgctxt "top_bottom_pattern option lines"
msgid "Lines"
msgstr "直线"

msgctxt "top_bottom_pattern_0 option lines"
msgid "Lines"
msgstr "直线"

msgctxt "machine_gcode_flavor option MACH3"
msgid "Mach3"
msgstr "Mach3"

msgctxt "machine_settings label"
msgid "Machine"
msgstr "﻿机器"

msgctxt "machine_depth label"
msgid "Machine Depth"
msgstr "机器深度"

msgctxt "machine_head_with_fans_polygon label"
msgid "Machine Head & Fan Polygon"
msgstr "机器头和风扇多边形"

msgctxt "machine_height label"
msgid "Machine Height"
msgstr "机器高度"

msgctxt "machine_name label"
msgid "Machine Type"
msgstr "﻿机器类型"

msgctxt "machine_width label"
msgid "Machine Width"
msgstr "机器宽度"

msgctxt "machine_settings description"
msgid "Machine specific settings"
msgstr "﻿机器详细设置"

msgctxt "conical_overhang_enabled label"
msgid "Make Overhang Printable"
msgstr "使悬垂可打印"

msgctxt "multiple_mesh_overlap description"
msgid "Make meshes which are touching each other overlap a bit. This makes them bond together better."
msgstr "让彼此接触的网格略微重叠。 这会让它们更好地粘合在一起。"

msgctxt "support_conical_enabled description"
msgid "Make support areas smaller at the bottom than at the overhang."
msgstr "使底部的支撑区域小于悬垂处的支撑区域。"

msgctxt "support_mesh_drop_down description"
msgid "Make support everywhere below the support mesh, so that there's no overhang in the support mesh."
msgstr "在支撑网格下方的所有位置进行支撑，让支撑网格中没有悬垂。"

msgctxt "extruder_prime_pos_abs description"
msgid "Make the extruder prime position absolute rather than relative to the last-known location of the head."
msgstr "使挤出机主要位置为绝对值，而不是与上一已知打印头位置的相对值。"

msgctxt "layer_0_z_overlap description"
msgid "Make the first and second layer of the model overlap in the Z direction to compensate for the filament lost in the airgap. All models above the first model layer will be shifted down by this amount.\nIt may be noted that sometimes the second layer is printed below initial layer because of this setting. This is intended behavior"
msgstr "使模型打印的第一层和第二层在 Z 方向上重叠,以补偿气隙中损失的丝材。模型的第一层上方的所有部分都将向下移动此量。"
"您可能会发现,进行此设置后,有时第二层会打印在初始层下方。这是正常的"

msgctxt "meshfix description"
msgid "Make the meshes more suited for 3D printing."
msgstr "使网格更适合 3D 打印。"

msgctxt "machine_gcode_flavor option Makerbot"
msgid "Makerbot"
msgstr "Makerbot"

msgctxt "machine_gcode_flavor option RepRap (Marlin/Sprinter)"
msgid "Marlin"
msgstr "Marlin"

msgctxt "machine_gcode_flavor option RepRap (Volumetric)"
msgid "Marlin (Volumetric)"
msgstr "Marlin（容积）"

msgctxt "material description"
msgid "Material"
msgstr "材料"

msgctxt "material label"
msgid "Material"
msgstr "材料"

msgctxt "material_brand label"
msgid "Material Brand"
msgstr "材料品牌"

msgctxt "material_guid label"
msgid "Material GUID"
msgstr "材料 GUID"

msgctxt "material_type label"
msgid "Material Type"
msgstr "材料种类"

msgctxt "max_extrusion_before_wipe label"
msgid "Material Volume Between Wipes"
msgstr "擦拭之间的材料量"

msgctxt "retraction_combing_max_distance label"
msgid "Max Comb Distance With No Retract"
msgstr "最大梳距，无收缩"

msgctxt "machine_max_acceleration_x label"
msgid "Maximum Acceleration X"
msgstr "X 轴最大加速度"

msgctxt "machine_max_acceleration_y label"
msgid "Maximum Acceleration Y"
msgstr "轴最大加速度"

msgctxt "machine_max_acceleration_z label"
msgid "Maximum Acceleration Z"
msgstr "Z 轴最大加速度"

msgctxt "support_tree_angle label"
msgid "Maximum Branch Angle"
msgstr "最大分支角度"

msgctxt "meshfix_maximum_deviation label"
msgid "Maximum Deviation"
msgstr "最大偏移量"

msgctxt "meshfix_maximum_extrusion_area_deviation label"
msgid "Maximum Extrusion Area Deviation"
msgstr "最大挤出面积偏移量"

msgctxt "cool_fan_speed_max label"
msgid "Maximum Fan Speed"
msgstr "最大风扇速度"

msgctxt "machine_max_acceleration_e label"
msgid "Maximum Filament Acceleration"
msgstr "挤出电机最大加速度"

msgctxt "conical_overhang_angle label"
msgid "Maximum Model Angle"
msgstr "最大模型角度"

msgctxt "conical_overhang_hole_size label"
msgid "Maximum Overhang Hole Area"
msgstr "最大悬垂孔面积"

msgctxt "material_maximum_park_duration label"
msgid "Maximum Park Duration"
msgstr "最长停放持续时间"

msgctxt "meshfix_maximum_resolution label"
msgid "Maximum Resolution"
msgstr "最大分辨率"

msgctxt "retraction_count_max label"
msgid "Maximum Retraction Count"
msgstr "最大回抽计数"

msgctxt "max_skin_angle_for_expansion label"
msgid "Maximum Skin Angle for Expansion"
msgstr "最大扩展皮肤角度"

msgctxt "machine_max_feedrate_e label"
msgid "Maximum Speed E"
msgstr "E 轴最大速度"

msgctxt "machine_max_feedrate_x label"
msgid "Maximum Speed X"
msgstr "X 轴最大速度"

msgctxt "machine_max_feedrate_y label"
msgid "Maximum Speed Y"
msgstr "Y 轴最大速度"

msgctxt "machine_max_feedrate_z label"
msgid "Maximum Speed Z"
msgstr "Z 轴最大速度"

msgctxt "support_tower_maximum_supported_diameter label"
msgid "Maximum Tower-Supported Diameter"
msgstr "最大塔支撑直径"

msgctxt "meshfix_maximum_travel_resolution label"
msgid "Maximum Travel Resolution"
msgstr "空走的最大分辨率"

msgctxt "machine_max_acceleration_x description"
msgid "Maximum acceleration for the motor of the X-direction"
msgstr "X 轴方向电机的最大加速度"

msgctxt "machine_max_acceleration_y description"
msgid "Maximum acceleration for the motor of the Y-direction."
msgstr "Y 轴方向电机的最大加速度。"

msgctxt "machine_max_acceleration_z description"
msgid "Maximum acceleration for the motor of the Z-direction."
msgstr "Z 轴方向电机的最大加速度。"

msgctxt "machine_max_acceleration_e description"
msgid "Maximum acceleration for the motor of the filament."
msgstr "耗材电机的最大加速度。"

msgctxt "bridge_sparse_infill_max_density description"
msgid "Maximum density of infill considered to be sparse. Skin over sparse infill is considered to be unsupported and so may be treated as a bridge skin."
msgstr "填充物的最大密度被视为稀疏。稀疏填充物表面被视为不受支持，因此可被视为连桥表面。"

msgctxt "support_tower_maximum_supported_diameter description"
msgid "Maximum diameter in the X/Y directions of a small area which is to be supported by a specialized support tower."
msgstr "将由专门的支撑塔支撑的小区域 X/Y 轴方向的最大直径。"

msgctxt "max_extrusion_before_wipe description"
msgid "Maximum material that can be extruded before another nozzle wipe is initiated. If this value is less than the volume of material required in a layer, the setting has no effect in this layer, i.e. it is limited to one wipe per layer."
msgstr "在开始下一轮喷嘴擦拭之前可挤出的最大材料量。如果此值小于层中所需的材料量，则该设置在此层中无效，即每层仅限擦拭一次。"

msgctxt "multiple_mesh_overlap label"
msgid "Merged Meshes Overlap"
msgstr "合并网格重叠"

msgctxt "meshfix label"
msgid "Mesh Fixes"
msgstr "网格修复"

msgctxt "mesh_position_x label"
msgid "Mesh Position X"
msgstr "网格X位置"

msgctxt "mesh_position_y label"
msgid "Mesh Position Y"
msgstr "网格Y位置"

msgctxt "mesh_position_z label"
msgid "Mesh Position Z"
msgstr "网格Z位置"

msgctxt "infill_mesh_order label"
msgid "Mesh Processing Rank"
msgstr "网格处理等级"

msgctxt "mesh_rotation_matrix label"
msgid "Mesh Rotation Matrix"
msgstr "网格旋转矩阵"

msgctxt "slicing_tolerance option middle"
msgid "Middle"
msgstr "Middle"

msgctxt "mold_width label"
msgid "Minimal Mold Width"
msgstr "最小模具宽度"

msgctxt "machine_min_cool_heat_time_window label"
msgid "Minimal Time Standby Temperature"
msgstr "最短时间待机温度"

msgctxt "bridge_wall_min_length label"
msgid "Minimum Bridge Wall Length"
msgstr "最小桥壁长度"

msgctxt "min_even_wall_line_width label"
msgid "Minimum Even Wall Line Width"
msgstr "最小偶数壁走线宽度"

msgctxt "retraction_extrusion_window label"
msgid "Minimum Extrusion Distance Window"
msgstr "最小挤出距离范围"

msgctxt "min_feature_size label"
msgid "Minimum Feature Size"
msgstr "最小特征尺寸"

msgctxt "machine_minimum_feedrate label"
msgid "Minimum Feedrate"
msgstr "最小进料速率"

msgctxt "support_tree_min_height_to_model label"
msgid "Minimum Height To Model"
msgstr "模型的最小高度"

msgctxt "min_infill_area label"
msgid "Minimum Infill Area"
msgstr "最小填充区域"

msgctxt "cool_min_layer_time label"
msgid "Minimum Layer Time"
msgstr "最短单层冷却时间"

msgctxt "min_odd_wall_line_width label"
msgid "Minimum Odd Wall Line Width"
msgstr "最小奇数壁走线宽度"

msgctxt "minimum_polygon_circumference label"
msgid "Minimum Polygon Circumference"
msgstr "最小多边形周长"

msgctxt "min_skin_width_for_expansion label"
msgid "Minimum Skin Width for Expansion"
msgstr "最小扩展皮肤宽度"

msgctxt "cool_min_speed label"
msgid "Minimum Speed"
msgstr "最小风扇速度"

msgctxt "minimum_support_area label"
msgid "Minimum Support Area"
msgstr "最小支撑面积"

msgctxt "minimum_bottom_area label"
msgid "Minimum Support Floor Area"
msgstr "最小支撑底板面积"

msgctxt "minimum_interface_area label"
msgid "Minimum Support Interface Area"
msgstr "最小支撑接触面面积"

msgctxt "minimum_roof_area label"
msgid "Minimum Support Roof Area"
msgstr "最小支撑顶板面积"

msgctxt "support_xy_distance_overhang label"
msgid "Minimum Support X/Y Distance"
msgstr "最小支撑 X/Y 距离"

msgctxt "min_bead_width label"
msgid "Minimum Thin Wall Line Width"
msgstr "最小薄壁走线宽度"

msgctxt "coasting_min_volume label"
msgid "Minimum Volume Before Coasting"
msgstr "滑行前最小体积"

msgctxt "min_wall_line_width label"
msgid "Minimum Wall Line Width"
msgstr "最小壁走线宽度"

msgctxt "minimum_interface_area description"
msgid "Minimum area size for support interface polygons. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撑接触面多边形的最小面积。面积小于此值的多边形将打印为一般支撑。"

msgctxt "minimum_support_area description"
msgid "Minimum area size for support polygons. Polygons which have an area smaller than this value will not be generated."
msgstr "支撑多边形的最小面积。将不会生成面积小于此值的多边形。"

msgctxt "minimum_bottom_area description"
msgid "Minimum area size for the floors of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撑底板的最小面积。面积小于此值的多边形将打印为一般支撑。"

msgctxt "minimum_roof_area description"
msgid "Minimum area size for the roofs of the support. Polygons which have an area smaller than this value will be printed as normal support."
msgstr "支撑顶板的最小面积。面积小于此值的多边形将打印为一般支撑。"

msgctxt "min_feature_size description"
msgid "Minimum thickness of thin features. Model features that are thinner than this value will not be printed, while features thicker than the Minimum Feature Size will be widened to the Minimum Wall Line Width."
msgstr "薄特征的最小厚度。将不打印比此值更薄的模型特征，而比最小特征尺寸更厚的特征将加宽到最小壁走线宽度。"

msgctxt "support_conical_min_width description"
msgid "Minimum width to which the base of the conical support area is reduced. Small widths can lead to unstable support structures."
msgstr "锥形支撑区域底部被缩小至的最小宽度。 宽度较小可导致不稳定的支撑结构。"

msgctxt "mold_enabled label"
msgid "Mold"
msgstr "模具"

msgctxt "mold_angle label"
msgid "Mold Angle"
msgstr "模具角度"

msgctxt "mold_roof_height label"
msgid "Mold Roof Height"
msgstr "模具顶板高度"

msgctxt "ironing_monotonic label"
msgid "Monotonic Ironing Order"
msgstr "单调熨平顺序"

msgctxt "raft_surface_monotonic label"
msgid "Monotonic Raft Top Surface Order"
msgstr "单调递增的筏顶面顺序"

msgctxt "roofing_monotonic label"
msgid "Monotonic Top Surface Order"
msgstr "单调顶部表面顺序"

msgctxt "skin_monotonic label"
msgid "Monotonic Top/Bottom Order"
msgstr "单调顶部/底部顺序"

msgctxt "skirt_line_count description"
msgid "Multiple skirt lines help to prime your extrusion better for small models. Setting this to 0 will disable the skirt."
msgstr "多个 Skirt 走线帮助为小型模型更好地装填您的挤出部分。 将其设为 0 将禁用 skirt。"

msgctxt "initial_layer_line_width_factor description"
msgid "Multiplier of the line width on the first layer. Increasing this could improve bed adhesion."
msgstr "第一层走线宽度乘数。 增大此乘数可改善热床粘着。"

msgctxt "material_no_load_move_factor label"
msgid "No Load Move Factor"
msgstr "空载移动系数"

msgctxt "skin_no_small_gaps_heuristic label"
msgid "No Skin in Z Gaps"
msgstr "Z 间隙内无表层"

msgctxt "blackmagic description"
msgid "Non-traditional ways to print your models."
msgstr "打印模型的非传统方式。"

msgctxt "adhesion_type option none"
msgid "None"
msgstr "无"

msgctxt "z_seam_corner option z_seam_corner_none"
msgid "None"
msgstr "无"

msgctxt "magic_mesh_surface_mode option normal"
msgid "Normal"
msgstr "正常"

msgctxt "prime_tower_mode option normal"
msgid "Normal"
msgstr "通常的"

msgctxt "support_structure option normal"
msgid "Normal"
msgstr "正常"

msgctxt "meshfix_keep_open_polygons description"
msgid "Normally Cura tries to stitch up small holes in the mesh and remove parts of a layer with big holes. Enabling this option keeps those parts which cannot be stitched. This option should be used as a last resort option when everything else fails to produce proper g-code."
msgstr "一般情况下，Cura 会尝试缝合网格中的小孔，并移除层中有大孔的部分。启用此选项将保留那些无法缝合的部分。当其他所有方法都无法产生正确的 G-code 时，最后才应考虑该选项。"

msgctxt "retraction_combing option noskin"
msgid "Not in Skin"
msgstr "除了皮肤"

msgctxt "retraction_combing option no_outer_surfaces"
msgid "Not on Outer Surface"
msgstr "不在外表面上"

msgctxt "machine_nozzle_expansion_angle label"
msgid "Nozzle Angle"
msgstr "喷嘴角度"

msgctxt "machine_nozzle_size label"
msgid "Nozzle Diameter"
msgstr "喷嘴直径"

msgctxt "nozzle_disallowed_areas label"
msgid "Nozzle Disallowed Areas"
msgstr "喷嘴不允许区域"

msgctxt "machine_nozzle_id label"
msgid "Nozzle ID"
msgstr "喷嘴 ID"

msgctxt "machine_nozzle_head_distance label"
msgid "Nozzle Length"
msgstr "喷嘴长度"

msgctxt "switch_extruder_extra_prime_amount label"
msgid "Nozzle Switch Extra Prime Amount"
msgstr "喷嘴切换额外装填量"

msgctxt "switch_extruder_prime_speed label"
msgid "Nozzle Switch Prime Speed"
msgstr "喷嘴切换装填速度"

msgctxt "switch_extruder_retraction_speed label"
msgid "Nozzle Switch Retract Speed"
msgstr "喷嘴切换回抽速度"

msgctxt "switch_extruder_retraction_amount label"
msgid "Nozzle Switch Retraction Distance"
msgstr "喷嘴切换回抽距离"

msgctxt "switch_extruder_retraction_speeds label"
msgid "Nozzle Switch Retraction Speed"
msgstr "喷嘴切换回抽速度"

msgctxt "machine_extruder_count label"
msgid "Number of Extruders"
msgstr "挤出机数目"

msgctxt "extruders_enabled_count label"
msgid "Number of Extruders That Are Enabled"
msgstr "已启用的挤出机数目"

msgctxt "speed_slowdown_layers label"
msgid "Number of Slower Layers"
msgstr "较慢层的数量"

msgctxt "extruders_enabled_count description"
msgid "Number of extruder trains that are enabled; automatically set in software"
msgstr "已启用的挤出机组数目；软件自动设置"

msgctxt "machine_extruder_count description"
msgid "Number of extruder trains. An extruder train is the combination of a feeder, bowden tube, and nozzle."
msgstr "挤出机组数目。 挤出机组是指进料装置、鲍登管和喷嘴的组合。"

msgctxt "wipe_repeat_count description"
msgid "Number of times to move the nozzle across the brush."
msgstr "在擦拭刷上移动喷嘴的次数。"

msgctxt "gradual_infill_steps description"
msgid "Number of times to reduce the infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Infill Density."
msgstr "在进入顶部表面以下时，将填充密度减少一半的次数。 越靠近顶面的区域密度越高，最高达到填充密度。"

msgctxt "gradual_support_infill_steps description"
msgid "Number of times to reduce the support infill density by half when getting further below top surfaces. Areas which are closer to top surfaces get a higher density, up to the Support Infill Density."
msgstr "在进入顶层以下时，将支撑填充密度减少一半的次数。 越靠近顶面的区域密度越高，最高达到支撑填充密度。"

msgctxt "infill_pattern option tetrahedral"
msgid "Octet"
msgstr "八角形"

msgctxt "retraction_combing option off"
msgid "Off"
msgstr "关"

msgctxt "mesh_position_x description"
msgid "Offset applied to the object in the x direction."
msgstr "应用在模型 x 方向上的偏移量。"

msgctxt "mesh_position_y description"
msgid "Offset applied to the object in the y direction."
msgstr "应用在模型 y 方向上的偏移量。"

msgctxt "mesh_position_z description"
msgid "Offset applied to the object in the z direction. With this you can perform what was used to be called 'Object Sink'."
msgstr "应用在模型 z 方向上的偏移量。 利用此选项，您可以执行过去被称为“模型沉降”的操作。"

msgctxt "machine_use_extruder_offset_to_offset_coords label"
msgid "Offset with Extruder"
msgstr "挤出机偏移量"

msgctxt "support_tree_rest_preference option buildplate"
msgid "On buildplate when possible"
msgstr "在打印平台上（如可能）"

msgctxt "support_tree_rest_preference option graceful"
msgid "On model if required"
msgstr "在模型上（如需要）"

msgctxt "print_sequence option one_at_a_time"
msgid "One at a Time"
msgstr "排队打印"

msgctxt "retraction_hop_only_when_collides description"
msgid "Only perform a Z Hop when moving over printed parts which cannot be avoided by horizontal motion by Avoid Printed Parts when Traveling."
msgstr "仅在移动到无法通过“空驶时避开已打印部分”选项的水平操作避开的已打印部分上方时执行 Z 抬升。"

msgctxt "ironing_only_highest_layer description"
msgid "Only perform ironing on the very last layer of the mesh. This saves time if the lower layers don't need a smooth surface finish."
msgstr "仅在网格的最后一层执行熨平。 如果较低的层不需要平滑的表面效果，这将节省时间。"

msgctxt "ooze_shield_angle label"
msgid "Ooze Shield Angle"
msgstr "渗出罩角度"

msgctxt "ooze_shield_dist label"
msgid "Ooze Shield Distance"
msgstr "渗出罩距离"

msgctxt "support_tree_branch_reach_limit label"
msgid "Optimal Branch Range"
msgstr "最佳分支范围"

msgctxt "optimize_wall_printing_order label"
msgid "Optimize Wall Printing Order"
msgstr "优化壁打印顺序"

msgctxt "optimize_wall_printing_order description"
msgid "Optimize the order in which walls are printed so as to reduce the number of retractions and the distance travelled. Most parts will benefit from this being enabled but some may actually take longer so please compare the print time estimates with and without optimization. First layer is not optimized when choosing brim as build plate adhesion type."
msgstr "优化墙壁印刷的顺序，以减少回撤的数量和旅行的距离。大多数部件会从启用这个功能中受益，但有些可能会花费更长的时间，所以请将打印时间估算与不优化进行比较。第一层在选择边缘作为构建板附着力类型时没有进行优化。"

msgctxt "machine_nozzle_tip_outer_diameter label"
msgid "Outer Nozzle Diameter"
msgstr "喷嘴外径"

msgctxt "acceleration_wall_0 label"
msgid "Outer Wall Acceleration"
msgstr "外壁加速度"

msgctxt "wall_0_extruder_nr label"
msgid "Outer Wall Extruder"
msgstr "外壁挤出机"

msgctxt "wall_0_material_flow label"
msgid "Outer Wall Flow"
msgstr "外壁流量"

msgctxt "wall_0_inset label"
msgid "Outer Wall Inset"
msgstr "外壁嵌入"

msgctxt "jerk_wall_0 label"
msgid "Outer Wall Jerk"
msgstr "外壁抖动速度"

msgctxt "wall_line_width_0 label"
msgid "Outer Wall Line Width"
msgstr "走线宽度（外壁）"

msgctxt "speed_wall_0 label"
msgid "Outer Wall Speed"
msgstr "速度（外壁）"

msgctxt "wall_0_wipe_dist label"
msgid "Outer Wall Wipe Distance"
msgstr "外壁擦嘴长度"

msgctxt "group_outer_walls description"
msgid "Outer walls of different islands in the same layer are printed in sequence. When enabled the amount of flow changes is limited because walls are printed one type at a time, when disabled the number of travels between islands is reduced because walls in the same islands are grouped."
msgstr "同一层不同组件的外墙依次进行打印。当启用此功能时,由于每次只打印一种类型的外壁,挤出量变化的数量受到限制；当禁用时,由于同一组件内的外壁被分组,组件之间的行进次数会减少。"

msgctxt "brim_location option outside"
msgid "Outside Only"
msgstr "仅外侧"

msgctxt "inset_direction option outside_in"
msgid "Outside To Inside"
msgstr "从外到内"

msgctxt "wall_overhang_angle label"
msgid "Overhanging Wall Angle"
msgstr "悬垂壁角度"

msgctxt "wall_overhang_speed_factor label"
msgid "Overhanging Wall Speed"
msgstr "悬垂壁速度"

msgctxt "wall_overhang_speed_factor description"
msgid "Overhanging walls will be printed at this percentage of their normal print speed."
msgstr "悬垂壁将以其正常打印速度的此百分比打印。"

msgctxt "wipe_pause description"
msgid "Pause after the unretract."
msgstr "在未回抽后暂停。"

msgctxt "bridge_fan_speed description"
msgid "Percentage fan speed to use when printing bridge walls and skin."
msgstr "打印连桥表面和桥壁时使用的风扇百分比速度。"

msgctxt "bridge_fan_speed_2 description"
msgid "Percentage fan speed to use when printing the second bridge skin layer."
msgstr "打印桥梁第二层表面时使用的风扇百分比速度。"

msgctxt "support_supported_skin_fan_speed description"
msgid "Percentage fan speed to use when printing the skin regions immediately above the support. Using a high fan speed can make the support easier to remove."
msgstr "打印支撑正上方表面区域时使用的风扇百分比速度。使用高风扇速度可能使支撑更容易移除。"

msgctxt "bridge_fan_speed_3 description"
msgid "Percentage fan speed to use when printing the third bridge skin layer."
msgstr "打印桥梁第三层表面时使用的风扇百分比速度。"

msgctxt "minimum_polygon_circumference description"
msgid "Polygons in sliced layers that have a circumference smaller than this amount will be filtered out. Lower values lead to higher resolution mesh at the cost of slicing time. It is meant mostly for high resolution SLA printers and very tiny 3D models with a lot of details."
msgstr "切片层中周长小于此数值的多边形将被滤除。以切片时间为代价，较低的值可实现较高分辨率的网格。它主要用于高分辨率 SLA 打印机和包含大量细节的极小 3D 模型。"

msgctxt "support_tree_angle_slow label"
msgid "Preferred Branch Angle"
msgstr "偏好分支角度"

msgctxt "wall_transition_filter_deviation description"
msgid "Prevent transitioning back and forth between one extra wall and one less. This margin extends the range of line widths which follow to [Minimum Wall Line Width - Margin, 2 * Minimum Wall Line Width + Margin]. Increasing this margin reduces the number of transitions, which reduces the number of extrusion starts/stops and travel time. However, large line width variation can lead to under- or overextrusion problems."
msgstr "防止在多一个壁和少一个壁之间来回过渡。此边距扩展走线宽度的范围，介于 [最小壁走线宽度 - 边距，2 * 最小壁走线宽度 + 边距] 之间。增加此边距将减少过渡数量，从而减少挤出启动/停止次数和行程时间。但是，较大的走线宽度变化会导致挤出不足或挤出过多的问题。"

msgctxt "acceleration_prime_tower label"
msgid "Prime Tower Acceleration"
msgstr "装填塔加速度"

msgctxt "prime_tower_brim_enable label"
msgid "Prime Tower Base"
msgstr "底漆塔座"

msgctxt "prime_tower_base_height label"
msgid "Prime Tower Base Height"
msgstr "底漆塔座高度"

msgctxt "prime_tower_base_size label"
msgid "Prime Tower Base Size"
msgstr "底漆塔座尺寸"

msgctxt "prime_tower_base_curve_magnitude label"
msgid "Prime Tower Base Slope"
msgstr "Prime Tower 底座斜度"

msgctxt "prime_tower_flow label"
msgid "Prime Tower Flow"
msgstr "装填塔流量"

msgctxt "jerk_prime_tower label"
msgid "Prime Tower Jerk"
msgstr "装填塔抖动速度"

msgctxt "prime_tower_line_width label"
msgid "Prime Tower Line Width"
msgstr "装填塔走线宽度"

msgctxt "prime_tower_max_bridging_distance label"
msgid "Prime Tower Maximum Bridging Distance"
msgstr "主塔最大桥接距离"

msgctxt "prime_tower_min_volume label"
msgid "Prime Tower Minimum Volume"
msgstr "装填塔最小体积"

msgctxt "prime_tower_raft_base_line_spacing label"
msgid "Prime Tower Raft Line Spacing"
msgstr "底漆塔筏线间距"

msgctxt "prime_tower_size label"
msgid "Prime Tower Size"
msgstr "装填塔尺寸"

msgctxt "speed_prime_tower label"
msgid "Prime Tower Speed"
msgstr "装填塔速度"

msgctxt "prime_tower_mode label"
msgid "Prime Tower Type"
msgstr "主塔类型"

msgctxt "prime_tower_position_x label"
msgid "Prime Tower X Position"
msgstr "装填塔 X 位置"

msgctxt "prime_tower_position_y label"
msgid "Prime Tower Y Position"
msgstr "装填塔 Y 位置"

msgctxt "acceleration_print label"
msgid "Print Acceleration"
msgstr "打印加速度"

msgctxt "jerk_print label"
msgid "Print Jerk"
msgstr "打印抖动速度"

msgctxt "ppr label"
msgid "Print Process Reporting"
msgstr "打印过程报告"

msgctxt "print_sequence label"
msgid "Print Sequence"
msgstr "打印序列"

msgctxt "speed_print label"
msgid "Print Speed"
msgstr "打印速度"

msgctxt "fill_outline_gaps label"
msgid "Print Thin Walls"
msgstr "打印薄壁"

msgctxt "brim_location description"
msgid "Print a brim on the outside of the model, inside, or both. Depending on the model, this helps reducing the amount of brim you need to remove afterwards, while ensuring a proper bed adhesion."
msgstr "在模型的外侧,内侧或两侧同时打印裙边。根据模型的不同,这有助于减少随后需要去除的裙边量,同时保证所需的底座附着力。"

msgctxt "prime_tower_enable description"
msgid "Print a tower next to the print which serves to prime the material after each nozzle switch."
msgstr "在打印品相邻处打印一个塔，用于在每个喷嘴切换后装填材料。"

msgctxt "infill_support_enabled description"
msgid "Print infill structures only where tops of the model should be supported. Enabling this reduces print time and material usage, but leads to ununiform object strength."
msgstr "只在模型的顶部支持打印填充结构。这样可以减少打印时间和材料的使用，但是会导致不一致的对象强度。"

msgctxt "ironing_monotonic description"
msgid "Print ironing lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "按照一定的顺序打印熨平走线，使它们始终在一个方向上与相邻的走线重叠。这需要更长一些的打印时间，但会使平面看起来更一致。"

msgctxt "mold_enabled description"
msgid "Print models as a mold, which can be cast in order to get a model which resembles the models on the build plate."
msgstr "将模型作为模具打印，可进行铸造，以便获取与打印平台上的模型类似的模型。"

msgctxt "fill_outline_gaps description"
msgid "Print pieces of the model which are horizontally thinner than the nozzle size."
msgstr "打印在水平面上比喷嘴尺寸更薄的模型部件。"

msgctxt "raft_surface_monotonic description"
msgid "Print raft top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes the surface look more consistent, which is also visible on the model bottom surface."
msgstr "按顺序打印筏层顶段表面线,使它们始终与单个方向上的相邻线重叠。这会花费稍微长一点的打印时间,但会使表面看起来更加一致,在模型底面上可以看出。"

msgctxt "bridge_skin_speed_2 description"
msgid "Print speed to use when printing the second bridge skin layer."
msgstr "打印桥梁第二层表面时使用的打印速度。"

msgctxt "bridge_skin_speed_3 description"
msgid "Print speed to use when printing the third bridge skin layer."
msgstr "打印桥梁第三层表面时使用的打印速度。"

msgctxt "print_temp_anomaly_limit label"
msgid "Print temperature Limit"
msgstr "打印温度限制"

msgctxt "print_temp_warn_limit label"
msgid "Print temperature Warning"
msgstr "打印温度警告"

msgctxt "infill_before_walls description"
msgid "Print the infill before printing the walls. Printing the walls first may lead to more accurate walls, but overhangs print worse. Printing the infill first leads to sturdier walls, but the infill pattern might sometimes show through the surface."
msgstr "打印壁前先打印填充物。 先打印壁可能产生更精确的壁，但悬垂打印质量会较差。 先打印填充会产生更牢固的壁，但有时候填充图案会透过表面显现出来。"

msgctxt "roofing_monotonic description"
msgid "Print top surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "按照一定的顺序打印顶部表面走线，使它们始终在一个方向上与相邻的走线重叠。这需要更长一些的打印时间，但会使平面看起来更一致。"

msgctxt "skin_monotonic description"
msgid "Print top/bottom lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "按照一定的顺序打印顶部/底部走线，使它们始终在一个方向上与相邻的走线重叠。这需要更长一些的打印时间，但会使平面看起来更一致。"

msgctxt "material_print_temperature label"
msgid "Printing Temperature"
msgstr "打印温度"

msgctxt "material_print_temperature_layer_0 label"
msgid "Printing Temperature Initial Layer"
msgstr "打印温度起始层"

msgctxt "skirt_height description"
msgid "Printing the innermost skirt line with multiple layers makes it easy to remove the skirt."
msgstr "多层打印最内层裙边走线，便于移除裙边。"

msgctxt "alternate_extra_perimeter description"
msgid "Prints an extra wall at every other layer. This way infill gets caught between these extra walls, resulting in stronger prints."
msgstr "每隔一层打印一个额外的壁。 通过这种方法，填充物会卡在这些额外的壁之间，从而产生更强韧的打印质量。"

msgctxt "resolution label"
msgid "Quality"
msgstr "质量"

msgctxt "infill_pattern option quarter_cubic"
msgid "Quarter Cubic"
msgstr "四面体"

msgctxt "adhesion_type option raft"
msgid "Raft"
msgstr "Raft"

msgctxt "raft_airgap label"
msgid "Raft Air Gap"
msgstr "Raft 空隙"

msgctxt "raft_base_margin label"
msgid "Raft Base Extra Margin"
msgstr "筏层基段额外边距"

msgctxt "raft_base_extruder_nr label"
msgid "Raft Base Extruder"
msgstr "Raft 底层挤出器"

msgctxt "raft_base_fan_speed label"
msgid "Raft Base Fan Speed"
msgstr "Raft 基础风扇速度"

msgctxt "raft_base_line_spacing label"
msgid "Raft Base Line Spacing"
msgstr "Raft 基础走线间距"

msgctxt "raft_base_line_width label"
msgid "Raft Base Line Width"
msgstr "Raft 基础走线宽度"

msgctxt "raft_base_acceleration label"
msgid "Raft Base Print Acceleration"
msgstr "Raft 基础打印加速度"

msgctxt "raft_base_jerk label"
msgid "Raft Base Print Jerk"
msgstr "Raft 基础打印抖动速度"

msgctxt "raft_base_speed label"
msgid "Raft Base Print Speed"
msgstr "Raft 基础打印速度"

msgctxt "raft_base_smoothing label"
msgid "Raft Base Smoothing"
msgstr "平滑筏层基段"

msgctxt "raft_base_thickness label"
msgid "Raft Base Thickness"
msgstr "Raft 基础厚度"

msgctxt "raft_base_wall_count label"
msgid "Raft Base Wall Count"
msgstr "Raft 底板壁数"

msgctxt "raft_margin label"
msgid "Raft Extra Margin"
msgstr "Raft 留白"

msgctxt "raft_fan_speed label"
msgid "Raft Fan Speed"
msgstr "Raft 风扇速度"

msgctxt "raft_interface_margin label"
msgid "Raft Middle Extra Margin"
msgstr "筏层中段额外边距"

msgctxt "raft_interface_extruder_nr label"
msgid "Raft Middle Extruder"
msgstr "Raft 中间挤出器"

msgctxt "raft_interface_fan_speed label"
msgid "Raft Middle Fan Speed"
msgstr "Raft 中间风扇速度"

msgctxt "raft_interface_layers label"
msgid "Raft Middle Layers"
msgstr "Raft 中间层"

msgctxt "raft_interface_line_width label"
msgid "Raft Middle Line Width"
msgstr "Raft 中间线宽度"

msgctxt "raft_interface_acceleration label"
msgid "Raft Middle Print Acceleration"
msgstr "Raft 中间打印加速度"

msgctxt "raft_interface_jerk label"
msgid "Raft Middle Print Jerk"
msgstr "Raft 中间打印抖动速度"

msgctxt "raft_interface_speed label"
msgid "Raft Middle Print Speed"
msgstr "Raft 中间打印速度"

msgctxt "raft_interface_smoothing label"
msgid "Raft Middle Smoothing"
msgstr "平滑筏层中段"

msgctxt "raft_interface_line_spacing label"
msgid "Raft Middle Spacing"
msgstr "Raft 中间间距"

msgctxt "raft_interface_thickness label"
msgid "Raft Middle Thickness"
msgstr "Raft 中间厚度"

msgctxt "raft_interface_wall_count label"
msgid "Raft Middle Wall Count"
msgstr "筏层中段壁圈数"

msgctxt "raft_acceleration label"
msgid "Raft Print Acceleration"
msgstr "Raft 打印加速度"

msgctxt "raft_jerk label"
msgid "Raft Print Jerk"
msgstr "Raft 打印抖动速度"

msgctxt "raft_speed label"
msgid "Raft Print Speed"
msgstr "Raft 打印速度"

msgctxt "raft_smoothing label"
msgid "Raft Smoothing"
msgstr "Raft 平滑度"

msgctxt "raft_surface_margin label"
msgid "Raft Top Extra Margin"
msgstr "筏层顶段额外边距"

msgctxt "raft_surface_extruder_nr label"
msgid "Raft Top Extruder"
msgstr "Raft 顶层挤出器"

msgctxt "raft_surface_fan_speed label"
msgid "Raft Top Fan Speed"
msgstr "Raft 顶部风扇速度"

msgctxt "raft_surface_thickness label"
msgid "Raft Top Layer Thickness"
msgstr "Raft 顶层厚度"

msgctxt "raft_surface_layers label"
msgid "Raft Top Layers"
msgstr "Raft 顶层"

msgctxt "raft_surface_line_width label"
msgid "Raft Top Line Width"
msgstr "Raft 顶线宽度"

msgctxt "raft_surface_acceleration label"
msgid "Raft Top Print Acceleration"
msgstr "Raft 顶部打印加速度"

msgctxt "raft_surface_jerk label"
msgid "Raft Top Print Jerk"
msgstr "Raft 顶部打印抖动速度"

msgctxt "raft_surface_speed label"
msgid "Raft Top Print Speed"
msgstr "Raft 顶部打印速度"

msgctxt "raft_surface_smoothing label"
msgid "Raft Top Smoothing"
msgstr "平滑筏层顶段"

msgctxt "raft_surface_line_spacing label"
msgid "Raft Top Spacing"
msgstr "Raft 顶部间距"

msgctxt "raft_surface_wall_count label"
msgid "Raft Top Wall Count"
msgstr "筏层顶段壁圈数"

msgctxt "raft_wall_count label"
msgid "Raft Wall Count"
msgstr "筏层壁圈数"

msgctxt "z_seam_type option random"
msgid "Random"
msgstr "随机"

msgctxt "infill_randomize_start_location label"
msgid "Randomize Infill Start"
msgstr "开始随机化填充"

msgctxt "infill_randomize_start_location description"
msgid "Randomize which infill line is printed first. This prevents one segment becoming the strongest, but it does so at the cost of an additional travel move."
msgstr "先随机化打印哪条填充线。这可以防止一个部分变强，但会导致一次额外的空驶。"

msgctxt "magic_fuzzy_skin_enabled description"
msgid "Randomly jitter while printing the outer wall, so that the surface has a rough and fuzzy look."
msgstr "在打印外墙时随机抖动，使表面具有粗糙和模糊的外观。"

msgctxt "machine_shape option rectangular"
msgid "Rectangular"
msgstr "矩形"

msgctxt "cool_fan_speed_min label"
msgid "Regular Fan Speed"
msgstr "正常风扇速度"

msgctxt "cool_fan_full_at_height label"
msgid "Regular Fan Speed at Height"
msgstr "正常风扇速度（高度）"

msgctxt "cool_fan_full_layer label"
msgid "Regular Fan Speed at Layer"
msgstr "正常风扇速度（层）"

msgctxt "cool_min_layer_time_fan_speed_max label"
msgid "Regular/Maximum Fan Speed Threshold"
msgstr "正常/最大风扇速度阈值"

msgctxt "relative_extrusion label"
msgid "Relative Extrusion"
msgstr "相对挤出"

msgctxt "meshfix_union_all_remove_holes label"
msgid "Remove All Holes"
msgstr "移除所有孔洞"

msgctxt "remove_empty_first_layers label"
msgid "Remove Empty First Layers"
msgstr "移除空白第一层"

msgctxt "carve_multiple_volumes label"
msgid "Remove Mesh Intersection"
msgstr "移除网格交叉"

msgctxt "raft_base_remove_inside_corners label"
msgid "Remove Raft Base Inside Corners"
msgstr "移除筏层基段内角"

msgctxt "raft_remove_inside_corners label"
msgid "Remove Raft Inside Corners"
msgstr "移除 Raft 内侧角"

msgctxt "raft_interface_remove_inside_corners label"
msgid "Remove Raft Middle Inside Corners"
msgstr "移除中段内角"

msgctxt "raft_surface_remove_inside_corners label"
msgid "Remove Raft Top Inside Corners"
msgstr "移除筏层顶段内角"

msgctxt "carve_multiple_volumes description"
msgid "Remove areas where multiple meshes are overlapping with each other. This may be used if merged dual material objects overlap with each other."
msgstr "移除多个网格互相重叠的区域。 如果合并的双材料模型彼此重叠，此选项可能适用。"

msgctxt "remove_empty_first_layers description"
msgid "Remove empty layers beneath the first printed layer if they are present. Disabling this setting can cause empty first layers if the Slicing Tolerance setting is set to Exclusive or Middle."
msgstr "移除第一个打印层下方的空白层（如果存在）。如果“切片公差”设置被设为“独占”或“中间”，禁用此设置可能导致空白第一层。"

msgctxt "raft_base_remove_inside_corners description"
msgid "Remove inside corners from the raft base, causing the raft to become convex."
msgstr "移除筏层基段的内角,会使筏层凸起"

msgctxt "raft_interface_remove_inside_corners description"
msgid "Remove inside corners from the raft middle part, causing the raft to become convex."
msgstr "移除筏层中段的内角,会使筏层凸起。"

msgctxt "raft_surface_remove_inside_corners description"
msgid "Remove inside corners from the raft top part, causing the raft to become convex."
msgstr "移除筏层顶段的内角,会使筏层凸起。"

msgctxt "raft_remove_inside_corners description"
msgid "Remove inside corners from the raft, causing the raft to become convex."
msgstr "从 Raft 上移除内侧角，这会使 Raft 变得凸出。"

msgctxt "meshfix_union_all_remove_holes description"
msgid "Remove the holes in each layer and keep only the outside shape. This will ignore any invisible internal geometry. However, it also ignores layer holes which can be viewed from above or below."
msgstr "移除每层的孔洞，仅保留外部形状。 这会忽略任何不可见的内部几何。 但是，也会忽略可从上方或下方看到的层孔洞。"

msgctxt "machine_gcode_flavor option RepRap (RepRap)"
msgid "RepRap"
msgstr "RepRap"

msgctxt "machine_gcode_flavor option Repetier"
msgid "Repetier"
msgstr "Repetier"

msgctxt "skin_outline_count description"
msgid "Replaces the outermost part of the top/bottom pattern with a number of concentric lines. Using one or two lines improves roofs that start on infill material."
msgstr "用多个同心线代替顶部/底部图案的最外面部分。 使用一条或两条线改善从填充材料开始的顶板。"

msgctxt "ppr description"
msgid "Reporting events that go out of set thresholds"
msgstr "报告超出设定阈值的事件"

msgctxt "support_tree_rest_preference label"
msgid "Rest Preference"
msgstr "停留偏好"

msgctxt "travel_retract_before_outer_wall label"
msgid "Retract Before Outer Wall"
msgstr "在外壁前回抽"

msgctxt "retract_at_layer_change label"
msgid "Retract at Layer Change"
msgstr "层变化时回抽"

msgctxt "retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "当喷嘴移动到非打印区域上方时回抽耗材。"

msgctxt "wipe_retraction_enable description"
msgid "Retract the filament when the nozzle is moving over a non-printed area."
msgstr "当喷嘴移动到非打印区域上方时回抽耗材。"

msgctxt "retract_at_layer_change description"
msgid "Retract the filament when the nozzle is moving to the next layer."
msgstr "当喷嘴移动到下一层时回抽耗材。"

msgctxt "retraction_amount label"
msgid "Retraction Distance"
msgstr "回抽距离"

msgctxt "retraction_extra_prime_amount label"
msgid "Retraction Extra Prime Amount"
msgstr "回抽额外装填量"

msgctxt "retraction_min_travel label"
msgid "Retraction Minimum Travel"
msgstr "回抽最小空驶"

msgctxt "retraction_prime_speed label"
msgid "Retraction Prime Speed"
msgstr "回抽装填速度"

msgctxt "retraction_retract_speed label"
msgid "Retraction Retract Speed"
msgstr "回抽速度"

msgctxt "retraction_speed label"
msgid "Retraction Speed"
msgstr "回抽速度"

msgctxt "z_seam_position option right"
msgid "Right"
msgstr "右侧"

msgctxt "machine_scale_fan_speed_zero_to_one label"
msgid "Scale Fan Speed To 0-1"
msgstr "在 0 到 1 范围内设置风扇速度"

msgctxt "machine_scale_fan_speed_zero_to_one description"
msgid "Scale the fan speed to be between 0 and 1 instead of between 0 and 256."
msgstr "在 0 到 1 范围内设置风扇速度，而不是 0 到 256 之间。"

msgctxt "material_shrinkage_percentage label"
msgid "Scaling Factor Shrinkage Compensation"
msgstr "缩放因子收缩补偿"

msgctxt "support_meshes_present label"
msgid "Scene Has Support Meshes"
msgstr "场景具有支撑网格"

msgctxt "z_seam_corner label"
msgid "Seam Corner Preference"
msgstr "缝隙角偏好设置"

msgctxt "user_defined_print_order_enabled label"
msgid "Set Print Sequence Manually"
msgstr "手动设置打印顺序"

msgctxt "draft_shield_height_limitation description"
msgid "Set the height of the draft shield. Choose to print the draft shield at the full height of the model or at a limited height."
msgstr "设置防风罩的高度。 选择在模型的完整高度或有限高度处打印防风罩。"

msgctxt "dual description"
msgid "Settings used for printing with multiple extruders."
msgstr "利用多个挤出机进行打印所用的设置。"

msgctxt "command_line_settings description"
msgid "Settings which are only used if CuraEngine isn't called from the Cura frontend."
msgstr "未从 Cura 前端调用 CuraEngine 时使用的设置。"

msgctxt "machine_extruders_shared_nozzle_initial_retraction label"
msgid "Shared Nozzle Initial Retraction"
msgstr "共用喷嘴初始缩回"

msgctxt "z_seam_type option sharpest_corner"
msgid "Sharpest Corner"
msgstr "最尖角"

msgctxt "shell description"
msgid "Shell"
msgstr "外壳"

msgctxt "z_seam_type option shortest"
msgid "Shortest"
msgstr "最短"

msgctxt "machine_show_variants label"
msgid "Show Machine Variants"
msgstr "显示打印机变体"

msgctxt "skin_edge_support_layers label"
msgid "Skin Edge Support Layers"
msgstr "皮肤边缘支撑层数"

msgctxt "skin_edge_support_thickness label"
msgid "Skin Edge Support Thickness"
msgstr "皮肤边缘支撑厚度"

msgctxt "expand_skins_expand_distance label"
msgid "Skin Expand Distance"
msgstr "皮肤扩展距离"

msgctxt "skin_overlap_mm label"
msgid "Skin Overlap"
msgstr "皮肤重叠"

msgctxt "skin_overlap label"
msgid "Skin Overlap Percentage"
msgstr "皮肤重叠百分比"

msgctxt "skin_preshrink label"
msgid "Skin Removal Width"
msgstr "肤移除宽度"

msgctxt "min_skin_width_for_expansion description"
msgid "Skin areas narrower than this are not expanded. This avoids expanding the narrow skin areas that are created when the model surface has a slope close to the vertical."
msgstr "如果皮肤区域宽度小于此值，则不会扩展。 这会避免扩展在模型表面的坡度接近垂直时所形成的狭窄皮肤区域。"

msgctxt "support_zag_skip_count description"
msgid "Skip one in every N connection lines to make the support structure easier to break away."
msgstr "每隔 N 个连接线跳过一个连接，让支撑结构更容易脱离。"

msgctxt "support_skip_some_zags description"
msgid "Skip some support line connections to make the support structure easier to break away. This setting is applicable to the Zig Zag support infill pattern."
msgstr "跳过部分支撑线连接，让支撑结构更容易脱离。 此设置适用于锯齿形支撑结构填充图案。"

msgctxt "adhesion_type option skirt"
msgid "Skirt"
msgstr "Skirt"

msgctxt "skirt_gap label"
msgid "Skirt Distance"
msgstr "Skirt 距离"

msgctxt "skirt_height label"
msgid "Skirt Height"
msgstr "裙边高度"

msgctxt "skirt_line_count label"
msgid "Skirt Line Count"
msgstr "Skirt 走线计数"

msgctxt "acceleration_skirt_brim label"
msgid "Skirt/Brim Acceleration"
msgstr "Skirt/Brim 加速度"

msgctxt "skirt_brim_extruder_nr label"
msgid "Skirt/Brim Extruder"
msgstr "Skirt/Brim 挤出器"

msgctxt "skirt_brim_material_flow label"
msgid "Skirt/Brim Flow"
msgstr "裙边/边缘流量"

msgctxt "jerk_skirt_brim label"
msgid "Skirt/Brim Jerk"
msgstr "Skirt/Brim 抖动速度"

msgctxt "skirt_brim_line_width label"
msgid "Skirt/Brim Line Width"
msgstr "走线宽度（Skirt / Brim）"

msgctxt "skirt_brim_minimal_length label"
msgid "Skirt/Brim Minimum Length"
msgstr "Skirt/Brim 最小长度"

msgctxt "skirt_brim_speed label"
msgid "Skirt/Brim Speed"
msgstr "Skirt/Brim 速度"

msgctxt "slicing_tolerance label"
msgid "Slicing Tolerance"
msgstr "切片公差"

msgctxt "small_feature_speed_factor_0 label"
msgid "Small Feature Initial Layer Speed"
msgstr "微小特征初始层速度"

msgctxt "small_feature_max_length label"
msgid "Small Feature Max Length"
msgstr "微小特征最大长度"

msgctxt "small_feature_speed_factor label"
msgid "Small Feature Speed"
msgstr "微小特征速度"

msgctxt "small_hole_max_size label"
msgid "Small Hole Max Size"
msgstr "小孔最大尺寸"

msgctxt "cool_min_temperature label"
msgid "Small Layer Printing Temperature"
msgstr "小型层打印温度"

msgctxt "small_skin_on_surface label"
msgid "Small Top/Bottom On Surface"
msgstr "表面顶部/底部小区域"

msgctxt "small_skin_width label"
msgid "Small Top/Bottom Width"
msgstr "顶宽/底宽较小"

msgctxt "small_feature_speed_factor_0 description"
msgid "Small features on the first layer will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "第一层的微小特征将按正常打印速度的百分比进行打印。缓慢打印有助于粘合和提高准确性。"

msgctxt "small_feature_speed_factor description"
msgid "Small features will be printed at this percentage of their normal print speed. Slower printing can help with adhesion and accuracy."
msgstr "微小特征将按正常打印速度的百分比进行打印。缓慢打印有助于粘合和提高准确性。"

msgctxt "small_skin_width description"
msgid "Small top/bottom regions are filled with walls instead of the default top/bottom pattern. This helps to avoids jerky motions. Off for the topmost (air-exposed) layer by default (see 'Small Top/Bottom On Surface')."
msgstr "顶部/底部小区域用墙壁填充，而不是默认顶部/底部图案。这有助于避免剧烈运动。最顶层（暴露在空气中）默认关闭此选项（见“表面顶部/底部小区域”）。"

msgctxt "brim_smart_ordering label"
msgid "Smart Brim"
msgstr "智能边缘"

msgctxt "z_seam_corner option z_seam_corner_weighted"
msgid "Smart Hiding"
msgstr "智能隐藏"

msgctxt "smooth_spiralized_contours label"
msgid "Smooth Spiralized Contours"
msgstr "平滑螺旋轮廓"

msgctxt "smooth_spiralized_contours description"
msgid "Smooth the spiralized contours to reduce the visibility of the Z seam (the Z seam should be barely visible on the print but will still be visible in the layer view). Note that smoothing will tend to blur fine surface details."
msgstr "平滑螺旋轮廓以减少 Z 缝的可见性（Z 缝于打印品上几乎不可见，但在层视图中仍然可见）。注意：平滑操作将模糊精细的表面细节。"

msgctxt "retraction_extra_prime_amount description"
msgid "Some material can ooze away during a travel move, which can be compensated for here."
msgstr "有些材料可能会在空驶过程中渗出，可以在这里对其进行补偿。"

msgctxt "wipe_retraction_extra_prime_amount description"
msgid "Some material can ooze away during a wipe travel moves, which can be compensated for here."
msgstr "有些材料可能会在擦拭空驶过程中渗出，可以在这里进行补偿。"

msgctxt "blackmagic label"
msgid "Special Modes"
msgstr "特殊模式"

msgctxt "speed description"
msgid "Speed"
msgstr "速度"

msgctxt "speed label"
msgid "Speed"
msgstr "速度"

msgctxt "wipe_hop_speed description"
msgid "Speed to move the z-axis during the hop."
msgstr "抬升期间移动 Z 轴的速度。"

msgctxt "magic_spiralize label"
msgid "Spiralize Outer Contour"
msgstr "螺旋打印外轮廓"

msgctxt "magic_spiralize description"
msgid "Spiralize smooths out the Z move of the outer edge. This will create a steady Z increase over the whole print. This feature turns a solid model into a single walled print with a solid bottom. This feature should only be enabled when each layer only contains a single part."
msgstr "螺旋打印实现外部边缘的平滑 Z 移动。 这会在整个打印上建立一个稳定的 Z 增量。 该功能会将一个实心模型转变为具有实体底部的单壁打印。 只有在当每一层仅包含一个部分时才应启用此功能。"

msgctxt "material_standby_temperature label"
msgid "Standby Temperature"
msgstr "待机温度"

msgctxt "machine_start_gcode label"
msgid "Start G-code"
msgstr "开始 G-code"

msgctxt "z_seam_type description"
msgid "Starting point of each path in a layer. When paths in consecutive layers start at the same point a vertical seam may show on the print. When aligning these near a user specified location, the seam is easiest to remove. When placed randomly the inaccuracies at the paths' start will be less noticeable. When taking the shortest path the print will be quicker."
msgstr "一层中每条路径的起点。 当连续多层的路径从相同点开始时，则打印物上会显示一条垂直缝隙。 如果将这些路径靠近一个用户指定的位置对齐，则缝隙最容易移除。 如果随机放置，则路径起点的不精准度将较不明显。 采用最短的路径时，打印将更为快速。"

msgctxt "machine_steps_per_mm_e label"
msgid "Steps per Millimeter (E)"
msgstr "每毫米步数 (E)"

msgctxt "machine_steps_per_mm_x label"
msgid "Steps per Millimeter (X)"
msgstr "每毫米步数 (X)"

msgctxt "machine_steps_per_mm_y label"
msgid "Steps per Millimeter (Y)"
msgstr "每毫米步数 (Y)"

msgctxt "machine_steps_per_mm_z label"
msgid "Steps per Millimeter (Z)"
msgstr "每毫米步数 (Z)"

msgctxt "support description"
msgid "Support"
msgstr "支撑"

msgctxt "support label"
msgid "Support"
msgstr "支撑"

msgctxt "acceleration_support label"
msgid "Support Acceleration"
msgstr "支撑加速度"

msgctxt "support_bottom_distance label"
msgid "Support Bottom Distance"
msgstr "支撑底部距离"

msgctxt "support_bottom_wall_count label"
msgid "Support Bottom Wall Line Count"
msgstr "支撑底层墙线条数"

msgctxt "support_brim_line_count label"
msgid "Support Brim Line Count"
msgstr "支撑 Brim 走线次数"

msgctxt "support_brim_width label"
msgid "Support Brim Width"
msgstr "支撑 Brim 宽度"

msgctxt "support_zag_skip_count label"
msgid "Support Chunk Line Count"
msgstr "支撑块走线数"

msgctxt "support_skip_zag_per_mm label"
msgid "Support Chunk Size"
msgstr "支撑块大小"

msgctxt "support_infill_rate label"
msgid "Support Density"
msgstr "支撑密度"

msgctxt "support_xy_overrides_z label"
msgid "Support Distance Priority"
msgstr "支撑距离优先级"

msgctxt "support_extruder_nr label"
msgid "Support Extruder"
msgstr "支撑用挤出机"

msgctxt "acceleration_support_bottom label"
msgid "Support Floor Acceleration"
msgstr "支撑底板加速度"

msgctxt "support_bottom_density label"
msgid "Support Floor Density"
msgstr "支撑底板密度"

msgctxt "support_bottom_extruder_nr label"
msgid "Support Floor Extruder"
msgstr "支撑底板挤出机"

msgctxt "support_bottom_material_flow label"
msgid "Support Floor Flow"
msgstr "支撑底板流量"

msgctxt "support_bottom_offset label"
msgid "Support Floor Horizontal Expansion"
msgstr "支撑底板水平扩展"

msgctxt "jerk_support_bottom label"
msgid "Support Floor Jerk"
msgstr "支撑底板抖动速度"

msgctxt "support_bottom_angles label"
msgid "Support Floor Line Directions"
msgstr "支撑底板走线方向"

msgctxt "support_bottom_line_distance label"
msgid "Support Floor Line Distance"
msgstr "支撑底板走线距离"

msgctxt "support_bottom_line_width label"
msgid "Support Floor Line Width"
msgstr "支撑底板走线宽度"

msgctxt "support_bottom_pattern label"
msgid "Support Floor Pattern"
msgstr "支撑底板图案"

msgctxt "speed_support_bottom label"
msgid "Support Floor Speed"
msgstr "支撑底板速度"

msgctxt "support_bottom_height label"
msgid "Support Floor Thickness"
msgstr "支撑底板厚度"

msgctxt "support_material_flow label"
msgid "Support Flow"
msgstr "支撑流量"

msgctxt "support_offset label"
msgid "Support Horizontal Expansion"
msgstr "支撑水平扩展"

msgctxt "acceleration_support_infill label"
msgid "Support Infill Acceleration"
msgstr "支撑填充加速度"

msgctxt "support_infill_extruder_nr label"
msgid "Support Infill Extruder"
msgstr "支撑填充挤出机"

msgctxt "jerk_support_infill label"
msgid "Support Infill Jerk"
msgstr "支撑填充抖动速度"

msgctxt "support_infill_sparse_thickness label"
msgid "Support Infill Layer Thickness"
msgstr "支撑填充层厚度"

msgctxt "support_infill_angles label"
msgid "Support Infill Line Directions"
msgstr "支撑填充走线方向"

msgctxt "speed_support_infill label"
msgid "Support Infill Speed"
msgstr "速度（支撑填充）"

msgctxt "acceleration_support_interface label"
msgid "Support Interface Acceleration"
msgstr "支撑接触面加速度"

msgctxt "support_interface_density label"
msgid "Support Interface Density"
msgstr "支撑接触面密度"

msgctxt "support_interface_extruder_nr label"
msgid "Support Interface Extruder"
msgstr "支撑接触面挤出机"

msgctxt "support_interface_material_flow label"
msgid "Support Interface Flow"
msgstr "支撑接触面流量"

msgctxt "support_interface_offset label"
msgid "Support Interface Horizontal Expansion"
msgstr "支撑接触面水平扩展"

msgctxt "jerk_support_interface label"
msgid "Support Interface Jerk"
msgstr "支撑接触面抖动速度"

msgctxt "support_interface_angles label"
msgid "Support Interface Line Directions"
msgstr "支撑接触面走线方向"

msgctxt "support_interface_line_width label"
msgid "Support Interface Line Width"
msgstr "走线宽度（支撑接触面）"

msgctxt "support_interface_pattern label"
msgid "Support Interface Pattern"
msgstr "支撑接触面图案"

msgctxt "support_interface_priority label"
msgid "Support Interface Priority"
msgstr "支撑接触面优先级"

msgctxt "speed_support_interface label"
msgid "Support Interface Speed"
msgstr "支撑接触面速度"

msgctxt "support_interface_height label"
msgid "Support Interface Thickness"
msgstr "支撑接触面厚度"

msgctxt "support_interface_wall_count label"
msgid "Support Interface Wall Line Count"
msgstr "支撑接触面墙线条数"

msgctxt "jerk_support label"
msgid "Support Jerk"
msgstr "支撑抖动速度"

msgctxt "support_join_distance label"
msgid "Support Join Distance"
msgstr "支撑结合部距离"

msgctxt "support_line_distance label"
msgid "Support Line Distance"
msgstr "支撑走线距离"

msgctxt "support_line_width label"
msgid "Support Line Width"
msgstr "走线宽度（支撑结构）"

msgctxt "support_mesh label"
msgid "Support Mesh"
msgstr "支撑网格"

msgctxt "support_angle label"
msgid "Support Overhang Angle"
msgstr "支撑悬垂角度"

msgctxt "support_pattern label"
msgid "Support Pattern"
msgstr "支撑图案"

msgctxt "support_type label"
msgid "Support Placement"
msgstr "支撑放置"

msgctxt "acceleration_support_roof label"
msgid "Support Roof Acceleration"
msgstr "支撑顶板加速度"

msgctxt "support_roof_density label"
msgid "Support Roof Density"
msgstr "支撑顶板密度"

msgctxt "support_roof_extruder_nr label"
msgid "Support Roof Extruder"
msgstr "支撑顶板挤出机"

msgctxt "support_roof_material_flow label"
msgid "Support Roof Flow"
msgstr "支撑顶板流量"

msgctxt "support_roof_offset label"
msgid "Support Roof Horizontal Expansion"
msgstr "支撑顶板水平扩展"

msgctxt "jerk_support_roof label"
msgid "Support Roof Jerk"
msgstr "支撑顶板抖动速度"

msgctxt "support_roof_angles label"
msgid "Support Roof Line Directions"
msgstr "支撑顶板走线方向"

msgctxt "support_roof_line_distance label"
msgid "Support Roof Line Distance"
msgstr "支撑顶板走线距离"

msgctxt "support_roof_line_width label"
msgid "Support Roof Line Width"
msgstr "支撑顶板走线宽度"

msgctxt "support_roof_pattern label"
msgid "Support Roof Pattern"
msgstr "支撑顶板图案"

msgctxt "speed_support_roof label"
msgid "Support Roof Speed"
msgstr "支撑顶板速度"

msgctxt "support_roof_height label"
msgid "Support Roof Thickness"
msgstr "支撑顶板厚度"

msgctxt "support_roof_wall_count label"
msgid "Support Roof Wall Line Count"
msgstr "支撑顶板墙线条数"

msgctxt "speed_support label"
msgid "Support Speed"
msgstr "速度（支撑结构）"

msgctxt "support_bottom_stair_step_height label"
msgid "Support Stair Step Height"
msgstr "支撑梯步阶高度"

msgctxt "support_bottom_stair_step_width label"
msgid "Support Stair Step Maximum Width"
msgstr "支撑梯步阶最大宽度"

msgctxt "support_bottom_stair_step_min_slope label"
msgid "Support Stair Step Minimum Slope Angle"
msgstr "支撑阶梯最小坡度角"

msgctxt "support_structure label"
msgid "Support Structure"
msgstr "支撑结构"

msgctxt "support_top_distance label"
msgid "Support Top Distance"
msgstr "支撑顶部距离"

msgctxt "support_wall_count label"
msgid "Support Wall Line Count"
msgstr "支撑墙行数"

msgctxt "support_xy_distance label"
msgid "Support X/Y Distance"
msgstr "支撑 X/Y 距离"

msgctxt "support_z_distance label"
msgid "Support Z Distance"
msgstr "支撑 Z 距离"

msgctxt "support_interface_priority option support_lines_overwrite_interface_area"
msgid "Support lines preferred"
msgstr "偏好支撑线"

msgctxt "support_interface_priority option support_area_overwrite_interface_area"
msgid "Support preferred"
msgstr "偏好支撑"

msgctxt "support_supported_skin_fan_speed label"
msgid "Supported Skin Fan Speed"
msgstr "支撑的表面风扇速度"

msgctxt "magic_mesh_surface_mode option surface"
msgid "Surface"
msgstr "表面"

msgctxt "material_surface_energy label"
msgid "Surface Energy"
msgstr "表面能"

msgctxt "magic_mesh_surface_mode label"
msgid "Surface Mode"
msgstr "表面模式"

msgctxt "material_adhesion_tendency description"
msgid "Surface adhesion tendency."
msgstr "表面附着倾向。"

msgctxt "material_surface_energy description"
msgid "Surface energy."
msgstr "表面能。"

msgctxt "brim_smart_ordering description"
msgid "Swap print order of the innermost and second innermost brim lines. This improves brim removal."
msgstr "变换最内层和第二内层侧裙走线的打印顺序。这样可改善侧裙移除。"

msgctxt "alternate_carve_order description"
msgid "Switch to which mesh intersecting volumes will belong with every layer, so that the overlapping meshes become interwoven. Turning this setting off will cause one of the meshes to obtain all of the volume in the overlap, while it is removed from the other meshes."
msgstr "切换为与每个层相交的网格相交体积，以便重叠的网格交织在一起。 关闭此设置将使其中一个网格获得重叠中的所有体积，同时将其从其他网格中移除。"

msgctxt "adaptive_layer_height_threshold description"
msgid "Target horizontal distance between two adjacent layers. Reducing this setting causes thinner layers to be used to bring the edges of the layers closer together."
msgstr "两个相邻图层之间的目标水平距离。减小此设置的值会使要使用的图层变薄，从而使图层的边缘距离更近。"

msgctxt "layer_start_x description"
msgid "The X coordinate of the position near where to find the part to start printing each layer."
msgstr "位置的 X 轴坐标，在该位置附近找到开始打印每层的部分。"

msgctxt "z_seam_x description"
msgid "The X coordinate of the position near where to start printing each part in a layer."
msgstr "位置的 X 轴坐标，在该位置附近开始打印层中各个部分。"

msgctxt "extruder_prime_pos_x description"
msgid "The X coordinate of the position where the nozzle primes at the start of printing."
msgstr "打印开始时，喷头在 X 轴上初始位置。"

msgctxt "layer_start_y description"
msgid "The Y coordinate of the position near where to find the part to start printing each layer."
msgstr "位置的 Y 轴坐标，在该位置附近找到开始打印每层的部分。"

msgctxt "z_seam_y description"
msgid "The Y coordinate of the position near where to start printing each part in a layer."
msgstr "位置的 Y 轴坐标，在该位置附近开始打印层中各个部分。"

msgctxt "extruder_prime_pos_y description"
msgid "The Y coordinate of the position where the nozzle primes at the start of printing."
msgstr "打印开始时，喷头在 Y 轴坐标上初始位置。"

msgctxt "extruder_prime_pos_z description"
msgid "The Z coordinate of the position where the nozzle primes at the start of printing."
msgstr "打印开始时，喷头在 Z 轴坐标上的起始位置."

msgctxt "acceleration_print_layer_0 description"
msgid "The acceleration during the printing of the initial layer."
msgstr "打印起始层时的加速度。"

msgctxt "acceleration_layer_0 description"
msgid "The acceleration for the initial layer."
msgstr "起始层的加速度。"

msgctxt "acceleration_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "起始层中的空驶加速度。"

msgctxt "jerk_travel_layer_0 description"
msgid "The acceleration for travel moves in the initial layer."
msgstr "起始层中的空驶加速度。"

msgctxt "acceleration_wall_x description"
msgid "The acceleration with which all inner walls are printed."
msgstr "打印所有内壁的加速度。"

msgctxt "acceleration_infill description"
msgid "The acceleration with which infill is printed."
msgstr "打印填充物的加速度。"

msgctxt "acceleration_ironing description"
msgid "The acceleration with which ironing is performed."
msgstr "执行熨平的加速度。"

msgctxt "acceleration_print description"
msgid "The acceleration with which printing happens."
msgstr "打印发生的加速度。"

msgctxt "raft_base_acceleration description"
msgid "The acceleration with which the base raft layer is printed."
msgstr "打印基础 Raft 层的加速度。"

msgctxt "acceleration_support_bottom description"
msgid "The acceleration with which the floors of support are printed. Printing them at lower acceleration can improve adhesion of support on top of your model."
msgstr "打印支撑底板的加速度。 以较低的加速度打印可以改善支撑在模型顶部的粘着。"

msgctxt "acceleration_support_infill description"
msgid "The acceleration with which the infill of support is printed."
msgstr "打印支撑填充物的加速度。"

msgctxt "raft_interface_acceleration description"
msgid "The acceleration with which the middle raft layer is printed."
msgstr "打印中间 Raft 层的加速度。"

msgctxt "acceleration_wall_0 description"
msgid "The acceleration with which the outermost walls are printed."
msgstr "打印最外壁的加速度。"

msgctxt "acceleration_prime_tower description"
msgid "The acceleration with which the prime tower is printed."
msgstr "打印装填塔的加速度。"

msgctxt "raft_acceleration description"
msgid "The acceleration with which the raft is printed."
msgstr "打印 Raft 的加速度。"

msgctxt "acceleration_support_interface description"
msgid "The acceleration with which the roofs and floors of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "打印支撑顶板和底板的加速度。 以较低的加速度打印可以改善悬垂质量。"

msgctxt "acceleration_support_roof description"
msgid "The acceleration with which the roofs of support are printed. Printing them at lower acceleration can improve overhang quality."
msgstr "打印支撑顶板的加速度。 以较低的加速度打印可以改善悬垂质量。"

msgctxt "acceleration_skirt_brim description"
msgid "The acceleration with which the skirt and brim are printed. Normally this is done with the initial layer acceleration, but sometimes you might want to print the skirt or brim at a different acceleration."
msgstr "打印 skirt 和 brim 的加速度。 一般情况是以起始层加速度打印这些部分，但有时候您可能想要以不同加速度来打印 skirt 或 brim。"

msgctxt "acceleration_support description"
msgid "The acceleration with which the support structure is printed."
msgstr "打印支撑结构的加速度。"

msgctxt "raft_surface_acceleration description"
msgid "The acceleration with which the top raft layers are printed."
msgstr "打印顶部 Raft 层的加速度。"

msgctxt "acceleration_wall_x_roofing description"
msgid "The acceleration with which the top surface inner walls are printed."
msgstr "顶面内壁打印完成后的加速度"

msgctxt "acceleration_wall_0_roofing description"
msgid "The acceleration with which the top surface outermost walls are printed."
msgstr "顶面最外壁打印完成后的加速度"

msgctxt "acceleration_wall description"
msgid "The acceleration with which the walls are printed."
msgstr "打印壁的加速度。"

msgctxt "acceleration_roofing description"
msgid "The acceleration with which top surface skin layers are printed."
msgstr "打印顶部表面皮肤层的加速度。"

msgctxt "acceleration_topbottom description"
msgid "The acceleration with which top/bottom layers are printed."
msgstr "打印顶部/底部层的加速度。"

msgctxt "acceleration_travel description"
msgid "The acceleration with which travel moves are made."
msgstr "进行空驶的加速度。"

msgctxt "ironing_flow description"
msgid "The amount of material, relative to a normal skin line, to extrude during ironing. Keeping the nozzle filled helps filling some of the crevices of the top surface, but too much results in overextrusion and blips on the side of the surface."
msgstr "熨平期间相对于正常皮肤走线的挤出材料量。 保持喷嘴填充状态有助于填充顶层表面的一些缝隙，但如填充过多则会导致表面上过度挤出和光点。"

msgctxt "infill_overlap description"
msgid "The amount of overlap between the infill and the walls as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物和壁之间的重叠量占填充走线宽度的百分比。稍微重叠可让各个壁与填充物牢固连接。"

msgctxt "infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物和壁之间的重叠量。 稍微重叠可让各个壁与填充物牢固连接。"

msgctxt "switch_extruder_retraction_amount description"
msgid "The amount of retraction when switching extruders. Set to 0 for no retraction at all. This should generally be the same as the length of the heat zone."
msgstr "切换挤出机时的回抽量。设为 0，不进行任何回抽。该值通常应与加热区的长度相同。"

msgctxt "machine_nozzle_expansion_angle description"
msgid "The angle between the horizontal plane and the conical part right above the tip of the nozzle."
msgstr "水平面与喷嘴尖端上部圆锥形之间的角度。"

msgctxt "support_tower_roof_angle description"
msgid "The angle of a rooftop of a tower. A higher value results in pointed tower roofs, a lower value results in flattened tower roofs."
msgstr "塔顶角度。 该值越高，塔顶越尖，值越低，塔顶越平。"

msgctxt "mold_angle description"
msgid "The angle of overhang of the outer walls created for the mold. 0° will make the outer shell of the mold vertical, while 90° will make the outside of the model follow the contour of the model."
msgstr "为模具创建的外壁的悬垂角度。 0° 将使模具的外壳垂直，而 90° 将使模型的外部遵循模型的轮廓。"

msgctxt "support_tree_branch_diameter_angle description"
msgid "The angle of the branches' diameter as they gradually become thicker towards the bottom. An angle of 0 will cause the branches to have uniform thickness over their length. A bit of an angle can increase stability of the tree support."
msgstr "随着分支朝底部逐渐变粗，分支直径的角度。角度为 0 表明分支全长具有均匀的粗细度。稍微有些角度可以增加树形支撑的稳定性。"

msgctxt "support_conical_angle description"
msgid "The angle of the tilt of conical support. With 0 degrees being vertical, and 90 degrees being horizontal. Smaller angles cause the support to be more sturdy, but consist of more material. Negative angles cause the base of the support to be wider than the top."
msgstr "锥形支撑的倾斜角度。 角度 0 度时为垂直，角度 90 度时为水平。 较小的角度会让支撑更为牢固，但需要更多材料。 负角会让支撑底座比顶部宽。"

msgctxt "magic_fuzzy_skin_point_density description"
msgid "The average density of points introduced on each polygon in a layer. Note that the original points of the polygon are discarded, so a low density results in a reduction of the resolution."
msgstr "在一层中的每个多边形上引入的点的平均密度。 注意，多边形的原始点被舍弃，因此低密度导致分辨率降低。"

msgctxt "magic_fuzzy_skin_point_dist description"
msgid "The average distance between the random points introduced on each line segment. Note that the original points of the polygon are discarded, so a high smoothness results in a reduction of the resolution. This value must be higher than half the Fuzzy Skin Thickness."
msgstr "在每个走线部分引入的随机点之间的平均距离。 注意，多边形的原始点被舍弃，因此高平滑度导致分辨率降低。 该值必须大于模糊皮肤厚度的一半。"

msgctxt "material_brand description"
msgid "The brand of material used."
msgstr "所用材料的品牌。"

msgctxt "machine_acceleration description"
msgid "The default acceleration of print head movement."
msgstr "打印头移动的默认加速度。"

msgctxt "default_material_print_temperature description"
msgid "The default temperature used for printing. This should be the \"base\" temperature of a material. All other print temperatures should use offsets based on this value"
msgstr "用于打印的默认温度。 应为材料的\"基本\"温度。 所有其他打印温度均应使用基于此值的偏移量"

msgctxt "default_material_bed_temperature description"
msgid "The default temperature used for the heated build plate. This should be the \"base\" temperature of a build plate. All other print temperatures should use offsets based on this value"
msgstr "用于加热打印平台的默认温度。这应该作为打印平台的“基础”温度。所有其他打印温度均应基于此值进行调整"

msgctxt "bridge_skin_density description"
msgid "The density of the bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "连桥表面层的密度。此值若小于 100 则会增大表面线条的缝隙。"

msgctxt "support_bottom_density description"
msgid "The density of the floors of the support structure. A higher value results in better adhesion of the support on top of the model."
msgstr "支撑结构底板的密度。 较高的值会在模型顶部产生更好的支撑粘着。"

msgctxt "support_roof_density description"
msgid "The density of the roofs of the support structure. A higher value results in better overhangs, but the supports are harder to remove."
msgstr "支撑结构顶板的密度。 较高的值会实现更好的悬垂，但支撑将更加难以移除。"

msgctxt "bridge_skin_density_2 description"
msgid "The density of the second bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "连桥第二层表面的密度。此值若小于 100 则会增大表面线条的缝隙。"

msgctxt "bridge_skin_density_3 description"
msgid "The density of the third bridge skin layer. Values less than 100 will increase the gaps between the skin lines."
msgstr "连桥第三层表面的密度。此值若小于 100 则会增大表面线条的缝隙。"

msgctxt "machine_depth description"
msgid "The depth (Y-direction) of the printable area."
msgstr "机器可打印区域深度（Y 坐标）"

msgctxt "support_tower_diameter description"
msgid "The diameter of a special tower."
msgstr "特殊塔的直径。"

msgctxt "support_tree_branch_diameter description"
msgid "The diameter of the thinnest branches of tree support. Thicker branches are more sturdy. Branches towards the base will be thicker than this."
msgstr "树形支撑最细分支的直径。较粗的分支更坚固。接近基础的分支会比这更粗。"

msgctxt "support_tree_tip_diameter description"
msgid "The diameter of the top of the tip of the branches of tree support."
msgstr "树形支撑的分支顶端的直径。"

msgctxt "machine_feeder_wheel_diameter description"
msgid "The diameter of the wheel that drives the material in the feeder."
msgstr "进料装置中材料驱动轮的直径。"

msgctxt "support_tree_max_diameter description"
msgid "The diameter of the widest branches of tree support. A thicker trunk is more sturdy; a thinner trunk takes up less space on the build plate."
msgstr "树形支撑最粗分支的直径。较粗的主干更坚固；较细主干在构建板上占据的空间较小。"

msgctxt "adaptive_layer_height_variation_step description"
msgid "The difference in height of the next layer height compared to the previous one."
msgstr "下一层与前一层的高度差。"

msgctxt "ironing_line_spacing description"
msgid "The distance between the lines of ironing."
msgstr "熨平走线之间的距离。"

msgctxt "travel_avoid_distance description"
msgid "The distance between the nozzle and already printed parts when avoiding during travel moves."
msgstr "喷嘴和已打印部分之间在空驶时避让的距离。"

msgctxt "raft_base_line_spacing description"
msgid "The distance between the raft lines for the base raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "基础 Raft 层的 Raft 走线之间的距离。 宽间距方便将 Raft 从打印平台移除。"

msgctxt "raft_interface_line_spacing description"
msgid "The distance between the raft lines for the middle raft layer. The spacing of the middle should be quite wide, while being dense enough to support the top raft layers."
msgstr "中间 Raft 层的 Raft 走线之间的距离。 中间的间距应足够宽，同时也要足够密集，以便支撑顶部 Raft 层。"

msgctxt "raft_surface_line_spacing description"
msgid "The distance between the raft lines for the top raft layers. The spacing should be equal to the line width, so that the surface is solid."
msgstr "顶部 Raft 层的 Raft 走线之间的距离。 间距应等于走线宽度，以便打造坚固表面。"

msgctxt "prime_tower_raft_base_line_spacing description"
msgid "The distance between the raft lines for the unique prime tower raft layer. Wide spacing makes for easy removal of the raft from the build plate."
msgstr "底漆塔独有的筏层之间筏线的距离。宽间距可以轻松地将筏从打印板上移除。"

msgctxt "interlocking_depth description"
msgid "The distance from the boundary between models to generate interlocking structure, measured in cells. Too few cells will result in poor adhesion."
msgstr "从模型之间的边界到生成互锁结构的距离，以单元格衡量。单元格太少会导致粘附不良。"

msgctxt "brim_width description"
msgid "The distance from the model to the outermost brim line. A larger brim enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "模型到最外侧 brim 线的距离。 较大的 brim 可增强与打印平台的附着，但也会减少有效打印区域。"

msgctxt "interlocking_boundary_avoidance description"
msgid "The distance from the outside of a model where interlocking structures will not be generated, measured in cells."
msgstr "与不会生成互锁结构的模型外部的距离，以单元格衡量。"

msgctxt "machine_heat_zone_length description"
msgid "The distance from the tip of the nozzle in which heat from the nozzle is transferred to the filament."
msgstr "与喷嘴尖端的距离，喷嘴产生的热量在这段距离内传递到耗材中。"

msgctxt "bottom_skin_expand_distance description"
msgid "The distance the bottom skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the skin adhere better to the walls on the layer below. Lower values save amount of material used."
msgstr "底部皮肤扩展到填充物中的距离。 值越大会让皮肤与填充图案更好地附着，并让皮肤与下面层的壁更好地粘着。 较低的值将节省所用的材料量。"

msgctxt "expand_skins_expand_distance description"
msgid "The distance the skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on neighboring layers adhere better to the skin. Lower values save amount of material used."
msgstr "皮肤扩展到填充物中的距离。 值越大会让皮肤与填充图案更好地附着，并让相邻层的层壁与皮肤更好地粘着。 较低的值将节省所用的材料量。"

msgctxt "top_skin_expand_distance description"
msgid "The distance the top skins are expanded into the infill. Higher values makes the skin attach better to the infill pattern and makes the walls on the layer above adhere better to the skin. Lower values save amount of material used."
msgstr "顶部皮肤扩展到填充物中的距离。 值越大会让皮肤与填充图案更好地附着，并让上方层的层壁与皮肤更好地粘着。 较低的值将节省所用的材料量。"

msgctxt "wipe_move_distance description"
msgid "The distance to move the head back and forth across the brush."
msgstr "在擦拭刷上来回移动喷嘴头的距离。"

msgctxt "lightning_infill_prune_angle description"
msgid "The endpoints of infill lines are shortened to save on material. This setting is the angle of overhang of the endpoints of these lines."
msgstr "为节省材料，填充线的端点将被缩短。此设置为这些线的端点形成的悬垂角度。"

msgctxt "material_extrusion_cool_down_speed description"
msgid "The extra speed by which the nozzle cools while extruding. The same value is used to signify the heat up speed lost when heating up while extruding."
msgstr "挤出时喷嘴冷却的额外速度。 使用相同的值表示挤出过程中进行加热时的加热速度损失。"

msgctxt "support_extruder_nr_layer_0 description"
msgid "The extruder train to use for printing the first layer of support infill. This is used in multi-extrusion."
msgstr "用于打印支撑填充物第一层的挤出机组。 用于多重挤出。"

msgctxt "raft_base_extruder_nr description"
msgid "The extruder train to use for printing the first layer of the raft. This is used in multi-extrusion."
msgstr "用于打印 Raft 第一层的挤出器组。用于多重挤出。"

msgctxt "support_bottom_extruder_nr description"
msgid "The extruder train to use for printing the floors of the support. This is used in multi-extrusion."
msgstr "用于打印支撑底板的挤出机组。 用于多重挤出。"

msgctxt "support_infill_extruder_nr description"
msgid "The extruder train to use for printing the infill of the support. This is used in multi-extrusion."
msgstr "用于打印支撑填充物的挤出机组。 用于多重挤出。"

msgctxt "raft_interface_extruder_nr description"
msgid "The extruder train to use for printing the middle layer of the raft. This is used in multi-extrusion."
msgstr "用于打印 Raft 中间层的挤出器组。用于多重挤出。"

msgctxt "support_interface_extruder_nr description"
msgid "The extruder train to use for printing the roofs and floors of the support. This is used in multi-extrusion."
msgstr "用于打印支撑顶板和底板的挤出机组。 用于多重挤出。"

msgctxt "support_roof_extruder_nr description"
msgid "The extruder train to use for printing the roofs of the support. This is used in multi-extrusion."
msgstr "用于打印支撑顶板的挤出机组。 用于多重挤出。"

msgctxt "skirt_brim_extruder_nr description"
msgid "The extruder train to use for printing the skirt or brim. This is used in multi-extrusion."
msgstr "用于打印 Skirt 或 Brim 的挤出机组。用于多重挤出。"

msgctxt "adhesion_extruder_nr description"
msgid "The extruder train to use for printing the skirt/brim/raft. This is used in multi-extrusion."
msgstr "用于打印 skirt/brim/raft 的挤出机组。 用于多重挤出。"

msgctxt "support_extruder_nr description"
msgid "The extruder train to use for printing the support. This is used in multi-extrusion."
msgstr "用于打印支撑的挤出机组。 用于多重挤出。"

msgctxt "raft_surface_extruder_nr description"
msgid "The extruder train to use for printing the top layer(s) of the raft. This is used in multi-extrusion."
msgstr "用于打印 Raft 顶层的挤出器组。用于多重挤出。"

msgctxt "infill_extruder_nr description"
msgid "The extruder train used for printing infill. This is used in multi-extrusion."
msgstr "用于打印填充的挤出机组。 用于多重挤出。"

msgctxt "wall_x_extruder_nr description"
msgid "The extruder train used for printing the inner walls. This is used in multi-extrusion."
msgstr "用于打印内壁的挤出机组。 用于多重挤出。"

msgctxt "wall_0_extruder_nr description"
msgid "The extruder train used for printing the outer wall. This is used in multi-extrusion."
msgstr "用于打印外壁的挤出机组。 用于多重挤出。"

msgctxt "top_bottom_extruder_nr description"
msgid "The extruder train used for printing the top and bottom skin. This is used in multi-extrusion."
msgstr "用于打印顶部和底部皮肤的挤出机组。 用于多重挤出。"

msgctxt "roofing_extruder_nr description"
msgid "The extruder train used for printing the top most skin. This is used in multi-extrusion."
msgstr "用于打印最顶部皮肤的挤出机组。 用于多重挤出。"

msgctxt "wall_extruder_nr description"
msgid "The extruder train used for printing the walls. This is used in multi-extrusion."
msgstr "用于打印壁的挤出机组。 用于多重挤出。"

msgctxt "raft_base_fan_speed description"
msgid "The fan speed for the base raft layer."
msgstr "基础 Raft 层的风扇速度。"

msgctxt "raft_interface_fan_speed description"
msgid "The fan speed for the middle raft layer."
msgstr "中间 Raft 层的风扇速度。"

msgctxt "raft_fan_speed description"
msgid "The fan speed for the raft."
msgstr "Raft 的风扇速度。"

msgctxt "raft_surface_fan_speed description"
msgid "The fan speed for the top raft layers."
msgstr "顶部 Raft 层的风扇速度。"

msgctxt "cross_infill_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the infill of the print."
msgstr "在打印的填充中，亮度值决定了相应位置的最小密度的图像的文件位置。"

msgctxt "cross_support_density_image description"
msgid "The file location of an image of which the brightness values determine the minimal density at the corresponding location in the support."
msgstr "一个图像的文件位置，在这个图像中，亮度值决定了在支持中相应位置的最小密度。"

msgctxt "speed_slowdown_layers description"
msgid "The first few layers are printed slower than the rest of the model, to get better adhesion to the build plate and improve the overall success rate of prints. The speed is gradually increased over these layers."
msgstr "前几层的打印速度比模型的其他层慢，以便实现与打印平台的更好粘着，并改善整体的打印成功率。 该速度在这些层中会逐渐增加。"

msgctxt "raft_airgap description"
msgid "The gap between the final raft layer and the first layer of the model. Only the first layer is raised by this amount to lower the bonding between the raft layer and the model. Makes it easier to peel off the raft."
msgstr "模型最后的 raft 层与第一层之间的间隙。 只有第一层被提高了这个量，以便降低 raft 层和模型之间的附着。 让 raft 更容易剥离。"

msgctxt "machine_height description"
msgid "The height (Z-direction) of the printable area."
msgstr "机器可打印区域高度（Z 坐标）"

msgctxt "mold_roof_height description"
msgid "The height above horizontal parts in your model which to print mold."
msgstr "用于打印模具的模型水平部分上方的高度。"

msgctxt "cool_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "风扇以正常速度旋转的高度。 在下方的层中，风扇速度逐渐从起始风扇速度增加到正常风扇速度。"

msgctxt "gantry_height description"
msgid "The height difference between the tip of the nozzle and the gantry system (X and Y axes)."
msgstr "喷嘴尖端与十字轴系统（X 轴和 Y 轴）之间的高度差。"

msgctxt "machine_nozzle_head_distance description"
msgid "The height difference between the tip of the nozzle and the lowest part of the print head."
msgstr "喷嘴尖端与打印头最低部分之间的高度差。"

msgctxt "retraction_hop_after_extruder_switch_height description"
msgid "The height difference when performing a Z Hop after extruder switch."
msgstr "挤出机切换后执行 Z 抬升的高度差。"

msgctxt "retraction_hop description"
msgid "The height difference when performing a Z Hop."
msgstr "执行 Z 抬升的高度差。"

msgctxt "wipe_hop_amount description"
msgid "The height difference when performing a Z Hop."
msgstr "执行 Z 抬升的高度差。"

msgctxt "layer_height description"
msgid "The height of each layer in mm. Higher values produce faster prints in lower resolution, lower values produce slower prints in higher resolution."
msgstr "每层的高度（以毫米为单位）。值越高，则打印速度越快，分辨率越低；值越低，则打印速度越慢，分辨率越高。"

msgctxt "gradual_infill_step_height description"
msgid "The height of infill of a given density before switching to half the density."
msgstr "在切换至密度的一半前指定密度的填充高度。"

msgctxt "gradual_support_infill_step_height description"
msgid "The height of support infill of a given density before switching to half the density."
msgstr "在切换至密度的一半前指定密度的支撑填充高度。"

msgctxt "interlocking_beam_layer_count description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "互锁结构梁的高度，以层数衡量。层数越少越坚固，但更容易出现缺陷。"

msgctxt "interlocking_orientation description"
msgid "The height of the beams of the interlocking structure, measured in number of layers. Less layers is stronger, but more prone to defects."
msgstr "互锁结构梁的高度，以层数衡量。层数越少越坚固，但更容易出现缺陷。"

msgctxt "layer_height_0 description"
msgid "The height of the initial layer in mm. A thicker initial layer makes adhesion to the build plate easier."
msgstr "起始层高（以毫米为单位）。起始层越厚，与打印平台的粘着越轻松。"

msgctxt "prime_tower_base_height description"
msgid "The height of the prime tower base. Increasing this value will result in a more sturdy prime tower because the base will be wider. If this setting is too low, the prime tower will not have a sturdy base."
msgstr "Prime tower 底座的高度。增加这个值将使 prime tower 更加坚固，因为底座会更宽。如果这个设置过低，prime tower 将没有坚固的底座。"

msgctxt "support_bottom_stair_step_height description"
msgid "The height of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures. Set to zero to turn off the stair-like behaviour."
msgstr "停留在模型上的支撑阶梯状底部的步阶高度。 较低的值会使支撑更难于移除，但过高的值可能导致不稳定的支撑结构。 设为零可以关闭阶梯状行为。"

msgctxt "brim_gap description"
msgid "The horizontal distance between the first brim line and the outline of the first layer of the print. A small gap can make the brim easier to remove while still providing the thermal benefits."
msgstr "第一条边沿线与打印件第一层轮廓之间的水平距离。较小的间隙可使边沿更容易去除，同时在散热方面仍有优势。"

msgctxt "skirt_gap description"
msgid "The horizontal distance between the skirt and the first layer of the print.\nThis is the minimum distance. Multiple skirt lines will extend outwards from this distance."
msgstr "skirt 和打印第一层之间的水平距离。"
"这是最小距离。多个 skirt 走线将从此距离向外延伸。"

msgctxt "lightning_infill_straightening_angle description"
msgid "The infill lines are straightened out to save on printing time. This is the maximum angle of overhang allowed across the length of the infill line."
msgstr "为节省打印时间，填充线将被拉直。这是整条填充线上允许的最大悬垂角度。"

msgctxt "infill_offset_x description"
msgid "The infill pattern is moved this distance along the X axis."
msgstr "填充图案沿 X 轴移动此距离。"

msgctxt "infill_offset_y description"
msgid "The infill pattern is moved this distance along the Y axis."
msgstr "填充图案沿 Y 轴移动此距离。"

msgctxt "machine_nozzle_size description"
msgid "The inner diameter of the nozzle. Change this setting when using a non-standard nozzle size."
msgstr "喷嘴内径，在使用非标准喷嘴尺寸时需更改此设置。"

msgctxt "raft_base_jerk description"
msgid "The jerk with which the base raft layer is printed."
msgstr "打印基础 Raft 层的抖动速度。"

msgctxt "raft_interface_jerk description"
msgid "The jerk with which the middle raft layer is printed."
msgstr "打印中间 Raft 层的抖动速度。"

msgctxt "raft_jerk description"
msgid "The jerk with which the raft is printed."
msgstr "打印 Raft 的抖动速度。"

msgctxt "raft_surface_jerk description"
msgid "The jerk with which the top raft layers are printed."
msgstr "打印顶部 Raft 层的抖动速度。"

msgctxt "bottom_skin_preshrink description"
msgid "The largest width of bottom skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing bottom skin at slanted surfaces in the model."
msgstr "将被移除的底部皮肤区域的最大宽度。 小于此值的所有皮肤区域都将消失。 这有助于限制在模型的倾斜表面打印底部皮肤时所耗用的时间和材料。"

msgctxt "skin_preshrink description"
msgid "The largest width of skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top/bottom skin at slanted surfaces in the model."
msgstr "将被移除的皮肤区域的最大宽度。 小于此值的所有皮肤区域都将消失。 这有助于限制在模型的倾斜表面打印顶部/底部皮肤时所耗用的时间和材料。"

msgctxt "top_skin_preshrink description"
msgid "The largest width of top skin areas which are to be removed. Every skin area smaller than this value will disappear. This can help in limiting the amount of time and material spent on printing top skin at slanted surfaces in the model."
msgstr "将被移除的顶部皮肤区域的最大宽度。 小于此值的所有皮肤区域都将消失。 这有助于限制在模型的倾斜表面打印顶部皮肤时所耗用的时间和材料。"

msgctxt "cool_fan_full_layer description"
msgid "The layer at which the fans spin on regular fan speed. If regular fan speed at height is set, this value is calculated and rounded to a whole number."
msgstr "风扇以正常风扇速度旋转的层。 如果设置了正常风扇速度（高度），则该值将被计算并舍入为整数。"

msgctxt "cool_min_layer_time_fan_speed_max description"
msgid "The layer time which sets the threshold between regular fan speed and maximum fan speed. Layers that print slower than this time use regular fan speed. For faster layers the fan speed gradually increases towards the maximum fan speed."
msgstr "设定正常风扇速度和最大风扇速度之间阈值的层时间。 打印速度低于此时间的层使用正常风扇速度。 对于更快的层，风扇速度逐渐增加到最大风扇速度。"

msgctxt "retraction_amount description"
msgid "The length of material retracted during a retraction move."
msgstr "回抽移动期间回抽的材料长度。"

msgctxt "prime_tower_base_curve_magnitude description"
msgid "The magnitude factor used for the slope of the prime tower base. If you increase this value, the base will become slimmer. If you decrease it, the base will become thicker."
msgstr "用于 prime tower 底座斜度的幅度因子。如果您增加这个值，底座会变得更细。如果减小它，底座会变得更厚。"

msgctxt "machine_buildplate_type description"
msgid "The material of the build plate installed on the printer."
msgstr "打印平台材料已安装在打印机上。"

msgctxt "adaptive_layer_height_variation description"
msgid "The maximum allowed height different from the base layer height."
msgstr "最大允许高度与基层高度不同。"

msgctxt "ooze_shield_angle description"
msgid "The maximum angle a part in the ooze shield will have. With 0 degrees being vertical, and 90 degrees being horizontal. A smaller angle leads to less failed ooze shields, but more material."
msgstr "渗出罩中的一个部件将具备的最大角度。 角度 0 度时为垂直，角度 90 度时为水平。 较小的角度会降低渗出罩失效次数，但会耗费更多材料。"

msgctxt "conical_overhang_angle description"
msgid "The maximum angle of overhangs after the they have been made printable. At a value of 0° all overhangs are replaced by a piece of model connected to the build plate, 90° will not change the model in any way."
msgstr "在悬垂变得可打印后悬垂的最大角度。 当该值为 0° 时，所有悬垂将被与打印平台连接的模型的一个部分替代，如果为 90° 时，不会以任何方式更改模型。"

msgctxt "support_tree_angle description"
msgid "The maximum angle of the branches while they grow around the model. Use a lower angle to make them more vertical and more stable. Use a higher angle to be able to have more reach."
msgstr "围绕模型扩大时，分支的最大角度。使用较小的角度可增加垂直度和稳定性。使用较大的角度可支撑更多结构。"

msgctxt "conical_overhang_hole_size description"
msgid "The maximum area of a hole in the base of the model before it's removed by Make Overhang Printable.  Holes smaller than this will be retained.  A value of 0 mm² will fill all holes in the models base."
msgstr "在“使悬垂对象可打印”将其删除之前，模型底部的孔的最大面积。小于此面积的孔将会保留。值 0 mm² 将填充模型底部的所有孔。"

msgctxt "meshfix_maximum_deviation description"
msgid "The maximum deviation allowed when reducing the resolution for the Maximum Resolution setting. If you increase this, the print will be less accurate, but the g-code will be smaller. Maximum Deviation is a limit for Maximum Resolution, so if the two conflict the Maximum Deviation will always be held true."
msgstr "降低“最大分辨率”设置的分辨率时允许的最大偏移量。如果增加该值，打印作业的准确性将降低，但 g-code 将减小。“最大偏移量”是“最大分辨率”的限制，因此如果两者冲突，则“最大偏移量”将始终保持有效。"

msgctxt "support_join_distance description"
msgid "The maximum distance between support structures in the X/Y directions. When separate structures are closer together than this value, the structures merge into one."
msgstr "支撑结构间在 X/Y 方向的最大距离。当分离结构之间的距离小于此值时，这些结构将合并为一体。"

msgctxt "flow_rate_max_extrusion_offset description"
msgid "The maximum distance in mm to move the filament to compensate for changes in flow rate."
msgstr "移动线材以补偿流量变化的最大距离（以毫米为单位）。"

msgctxt "meshfix_maximum_extrusion_area_deviation description"
msgid "The maximum extrusion area deviation allowed when removing intermediate points from a straight line. An intermediate point may serve as width-changing point in a long straight line. Therefore, if it is removed, it will cause the line to have a uniform width and, as a result, lose (or gain) a bit of extrusion area. If you increase this you may notice slight under- (or over-) extrusion in between straight parallel walls, as more intermediate width-changing points will be allowed to be removed. Your print will be less accurate, but the g-code will be smaller."
msgstr "从直线中移除中间点时允许的最大挤出面积偏移量。在长直线中，中间点可以用作宽度变化点。因此，如果移除该点，这会使得线条具有均匀的宽度，进而导致失去（或增加）一点挤出面积。如果增加此值，您可能会注意到平行直壁之间的挤出不足（或过多），因为将允许移除更多的中间宽度变化点。打印作业的准确性将降低，但 g-code 将减小。"

msgctxt "jerk_print_layer_0 description"
msgid "The maximum instantaneous velocity change during the printing of the initial layer."
msgstr "打印起始层时的最大瞬时速度变化。"

msgctxt "jerk_print description"
msgid "The maximum instantaneous velocity change of the print head."
msgstr "打印头的最大瞬时速度变化。"

msgctxt "jerk_ironing description"
msgid "The maximum instantaneous velocity change while performing ironing."
msgstr "执行熨平时的最大瞬时速度变化。"

msgctxt "jerk_wall_x description"
msgid "The maximum instantaneous velocity change with which all inner walls are printed."
msgstr "打印所有内壁时的最大瞬时速度变化。"

msgctxt "jerk_infill description"
msgid "The maximum instantaneous velocity change with which infill is printed."
msgstr "打印填充物时的最大瞬时速度变化。"

msgctxt "jerk_support_bottom description"
msgid "The maximum instantaneous velocity change with which the floors of support are printed."
msgstr "打印支撑底板时的最大瞬时速度变化。"

msgctxt "jerk_support_infill description"
msgid "The maximum instantaneous velocity change with which the infill of support is printed."
msgstr "打印支撑填充物时的最大瞬时速度变化。"

msgctxt "jerk_wall_0 description"
msgid "The maximum instantaneous velocity change with which the outermost walls are printed."
msgstr "打印最外壁时的最大瞬时速度变化。"

msgctxt "jerk_prime_tower description"
msgid "The maximum instantaneous velocity change with which the prime tower is printed."
msgstr "打印装填塔时的最大瞬时速度变化。"

msgctxt "jerk_support_interface description"
msgid "The maximum instantaneous velocity change with which the roofs and floors of support are printed."
msgstr "打印支撑顶板和底板的最大瞬时速度变化。"

msgctxt "jerk_support_roof description"
msgid "The maximum instantaneous velocity change with which the roofs of support are printed."
msgstr "打印支撑顶板的最大瞬时速度变化。"

msgctxt "jerk_skirt_brim description"
msgid "The maximum instantaneous velocity change with which the skirt and brim are printed."
msgstr "打印 skirt 和 brim 时的最大瞬时速度变化。"

msgctxt "jerk_support description"
msgid "The maximum instantaneous velocity change with which the support structure is printed."
msgstr "打印支撑结构时的最大瞬时速度变化。"

msgctxt "jerk_wall_x_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface inner walls are printed."
msgstr "打印顶面内壁时的最大瞬时速度变化。"

msgctxt "jerk_wall_0_roofing description"
msgid "The maximum instantaneous velocity change with which the top surface outermost walls are printed."
msgstr "打印顶面最外壁时的最大瞬时速度变化。"

msgctxt "jerk_wall description"
msgid "The maximum instantaneous velocity change with which the walls are printed."
msgstr "打印壁时的最大瞬时速度变化。"

msgctxt "jerk_roofing description"
msgid "The maximum instantaneous velocity change with which top surface skin layers are printed."
msgstr "打印顶部表面皮肤层时的最大瞬时速度变化。"

msgctxt "jerk_topbottom description"
msgid "The maximum instantaneous velocity change with which top/bottom layers are printed."
msgstr "打印顶部/底部层时的最大瞬时速度变化。"

msgctxt "jerk_travel description"
msgid "The maximum instantaneous velocity change with which travel moves are made."
msgstr "进行空驶时的最大瞬时速度变化。"

msgctxt "prime_tower_max_bridging_distance description"
msgid "The maximum length of the branches which may be printed over the air."
msgstr "可以跨空打印分支的最大长度。"

msgctxt "machine_max_feedrate_x description"
msgid "The maximum speed for the motor of the X-direction."
msgstr "X 轴方向电机的最大速度。"

msgctxt "machine_max_feedrate_y description"
msgid "The maximum speed for the motor of the Y-direction."
msgstr "Y 轴方向电机的最大速度。"

msgctxt "machine_max_feedrate_z description"
msgid "The maximum speed for the motor of the Z-direction."
msgstr "Z 轴方向电机的最大速度。"

msgctxt "machine_max_feedrate_e description"
msgid "The maximum speed of the filament."
msgstr "耗材的最大速度。"

msgctxt "support_bottom_stair_step_width description"
msgid "The maximum width of the steps of the stair-like bottom of support resting on the model. A low value makes the support harder to remove, but too high values can lead to unstable support structures."
msgstr "停留在模型上的支撑阶梯状底部的最大步阶宽度。 较低的值会使支撑更难于移除，但过高的值可能导致不稳定的支撑结构。"

msgctxt "mold_width description"
msgid "The minimal distance between the outside of the mold and the outside of the model."
msgstr "模具外侧与模型外侧之间的最短距离。"

msgctxt "machine_minimum_feedrate description"
msgid "The minimal movement speed of the print head."
msgstr "打印头的最低移动速度。"

msgctxt "material_initial_print_temperature description"
msgid "The minimal temperature while heating up to the Printing Temperature at which printing can already start."
msgstr "加热到可以开始打印的打印温度时的最低温度。"

msgctxt "machine_min_cool_heat_time_window description"
msgid "The minimal time an extruder has to be inactive before the nozzle is cooled. Only when an extruder is not used for longer than this time will it be allowed to cool down to the standby temperature."
msgstr "挤出机必须保持不活动以便喷嘴冷却的最短时间。 挤出机必须不使用此时间以上，才可以冷却到待机温度。"

msgctxt "infill_support_angle description"
msgid "The minimum angle of internal overhangs for which infill is added. At a value of 0° objects are totally filled with infill, 90° will not provide any infill."
msgstr "添加内填充的内部覆盖的最小角度。在一个0的值中，完全填满了填充，90将不提供任何填充。"

msgctxt "support_angle description"
msgid "The minimum angle of overhangs for which support is added. At a value of 0° all overhangs are supported, 90° will not provide any support."
msgstr "添加支撑的最小悬垂角度。 当角度为 0° 时，将支撑所有悬垂，当角度为 90° 时，不提供任何支撑。"

msgctxt "retraction_min_travel description"
msgid "The minimum distance of travel needed for a retraction to happen at all. This helps to get fewer retractions in a small area."
msgstr "回抽发生所需的最小空驶距离。 这有助于在较小区域内实现更少的回抽。"

msgctxt "skirt_brim_minimal_length description"
msgid "The minimum length of the skirt or brim. If this length is not reached by all skirt or brim lines together, more skirt or brim lines will be added until the minimum length is reached. Note: If the line count is set to 0 this is ignored."
msgstr "skirt 或 brim 的最小长度。 如果所有 skirt 或 brim 走线之和都没有达到此长度，则将添加更多 skirt 或 brim 走线直至达到最小长度。 注意： 如果走线计数设为 0，则将忽略此选项。"

msgctxt "min_odd_wall_line_width description"
msgid "The minimum line width for middle line gap filler polyline walls. This setting determines at which model thickness we switch from printing two wall lines, to printing two outer walls and a single central wall in the middle. A higher Minimum Odd Wall Line Width leads to a higher maximum even wall line width. The maximum odd wall line width is calculated as 2 * Minimum Even Wall Line Width."
msgstr "中间走线空隙填料多线壁的最小走线宽度。此设置确定在什么模型厚度下，我们从打印两根壁走线切换到打印两个外壁并在中间打印一个中心壁。更高的最小奇数壁走线宽度会带来更高的最大偶数壁走线宽度。最大奇数壁走线宽度计算方法是：2 * 最小偶数壁走线宽度。"

msgctxt "min_even_wall_line_width description"
msgid "The minimum line width for normal polygonal walls. This setting determines at which model thickness we switch from printing a single thin wall line, to printing two wall lines. A higher Minimum Even Wall Line Width leads to a higher maximum odd wall line width. The maximum even wall line width is calculated as Outer Wall Line Width + 0.5 * Minimum Odd Wall Line Width."
msgstr "普通多边形墙的最小走线宽度。此设置确定我们从打印单根薄壁走线切换到打印两根壁走线时的模型厚度。更高的最小偶数壁走线宽度会带来更高的最大奇数壁走线宽度。最大偶数壁走线宽度计算方法是：外壁走线宽度 + 0.5 * 最小奇数壁走线宽度。"

msgctxt "cool_min_speed description"
msgid "The minimum print speed, despite slowing down due to the minimum layer time. When the printer would slow down too much, the pressure in the nozzle would be too low and result in bad print quality."
msgstr "最低打印速度，排除因最短层时间而减速。 当打印机减速过多时，喷嘴中的压力将过低并导致较差的打印质量。"

msgctxt "meshfix_maximum_resolution description"
msgid "The minimum size of a line segment after slicing. If you increase this, the mesh will have a lower resolution. This may allow the printer to keep up with the speed it has to process g-code and will increase slice speed by removing details of the mesh that it can't process anyway."
msgstr "走线部分在切片后的最小尺寸。如果提高此值，网格的分辨率将降低。这可让打印机保持处理 g-code 所需的速度，并将通过移除无法处理的网格细节提高切片速度。"

msgctxt "meshfix_maximum_travel_resolution description"
msgid "The minimum size of a travel line segment after slicing. If you increase this, the travel moves will have less smooth corners. This may allow the printer to keep up with the speed it has to process g-code, but it may cause model avoidance to become less accurate."
msgstr "切片后的旅行线路段的最小尺寸。如果你增加了这个，旅行的移动就会变得不那么平滑了。这可能使打印机能够跟上它处理g代码的速度，但是它可能导致模型的避免变得不那么准确。"

msgctxt "support_bottom_stair_step_min_slope description"
msgid "The minimum slope of the area for stair-stepping to take effect. Low values should make support easier to remove on shallower slopes, but really low values may result in some very counter-intuitive results on other parts of the model."
msgstr "使阶梯生效的区域最小坡度。该值较小可在较浅的坡度上更容易去除支撑，但该值过小可能会在模型的其他部分上产生某些很反常的结果。"

msgctxt "cool_min_layer_time description"
msgid "The minimum time spent in a layer. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "在层中花费的最少时间。 这会迫使打印机减速，以便至少在一层中消耗此处所规定的时间。 这会让已打印材料充分冷却后再打印下一层。 如果提升头被禁用，且如果不这么做会违反“最小速度“，则层所花时间可能仍会少于最小层时间。"

msgctxt "prime_tower_min_volume description"
msgid "The minimum volume for each layer of the prime tower in order to purge enough material."
msgstr "为了清除足够的材料，装填塔每层的最小体积。"

msgctxt "support_tree_max_diameter_increase_by_merges_when_support_to_model description"
msgid "The most the diameter of a branch that has to connect to the model may increase by merging with branches that could reach the buildplate. Increasing this reduces print time, but increases the area of support that rests on model"
msgstr "与接触打印平台的分支合并时，模型必连分支的最大直径可能会扩大。扩大直径可减少打印时间，但会增加模型上的支撑区域"

msgctxt "machine_name description"
msgid "The name of your 3D printer model."
msgstr "﻿您的 3D 打印机型号的名称。"

msgctxt "machine_nozzle_id description"
msgid "The nozzle ID for an extruder train, such as \"AA 0.4\" and \"BB 0.8\"."
msgstr "挤出机组的喷嘴 ID，比如\"AA 0.4\"和\"BB 0.8\"。"

msgctxt "travel_avoid_other_parts description"
msgid "The nozzle avoids already printed parts when traveling. This option is only available when combing is enabled."
msgstr "喷嘴会在空驶时避开已打印的部分。 此选项仅在启用梳理功能时可用。"

msgctxt "travel_avoid_supports description"
msgid "The nozzle avoids already printed supports when traveling. This option is only available when combing is enabled."
msgstr "在空走时，喷嘴避免了已打印的支撑。只有在启用了梳理时才可以使用此选项。"

msgctxt "bottom_layers description"
msgid "The number of bottom layers. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "底层的数量。 在按底层厚度计算时，该值舍入为整数。"

msgctxt "raft_base_wall_count description"
msgid "The number of contours to print around the linear pattern in the base layer of the raft."
msgstr "在 Raft 的底板层中，围绕线型图案打印轮廓的次数。"

msgctxt "raft_interface_wall_count description"
msgid "The number of contours to print around the linear pattern in the middle layers of the raft."
msgstr "围绕筏层中段的线性图案打印的轮廓圈数。"

msgctxt "raft_surface_wall_count description"
msgid "The number of contours to print around the linear pattern in the top layers of the raft."
msgstr "围绕筏层顶段的线性图案打印的轮廓圈数。"

msgctxt "raft_wall_count description"
msgid "The number of contours to print around the linear pattern of the raft."
msgstr "围绕筏层的线性图案打印的轮廓圈数。"

msgctxt "skin_edge_support_layers description"
msgid "The number of infill layers that supports skin edges."
msgstr "支撑皮肤边缘的填充物的层数。"

msgctxt "initial_bottom_layers description"
msgid "The number of initial bottom layers, from the build-plate upwards. When calculated by the bottom thickness, this value is rounded to a whole number."
msgstr "从构建板向上算起的初始底层数。在按底层厚度计算时，该值四舍五入为整数。"

msgctxt "raft_interface_layers description"
msgid "The number of layers between the base and the surface of the raft. These comprise the main thickness of the raft. Increasing this creates a thicker, sturdier raft."
msgstr "Raft 的底层和表面之间的层数。这些层组成了 Raft 的主要厚度。增加此值会创建一个更厚、更坚固的 Raft。"

msgctxt "brim_line_count description"
msgid "The number of lines used for a brim. More brim lines enhance adhesion to the build plate, but also reduces the effective print area."
msgstr "brim 所用走线数量。 更多 brim 走线可增强与打印平台的附着，但也会减少有效打印区域。"

msgctxt "support_brim_line_count description"
msgid "The number of lines used for the support brim. More brim lines enhance adhesion to the build plate, at the cost of some extra material."
msgstr "用于支撑 Brim 的走线数量。更多 Brim 走线可增强与打印平台的附着，但也会增加一些额外材料成本。"

msgctxt "raft_surface_layers description"
msgid "The number of top layers on top of the 2nd raft layer. These are fully filled layers that the model sits on. 2 layers result in a smoother top surface than 1."
msgstr "第 2 个 raft 层上方的顶层数量。 这些是模型所在的完全填充层。 第二层会产生比第一层更平滑的顶部表面。"

msgctxt "top_layers description"
msgid "The number of top layers. When calculated by the top thickness, this value is rounded to a whole number."
msgstr "顶层的数量。 在按顶层厚度计算时，该值舍入为整数。"

msgctxt "roofing_layer_count description"
msgid "The number of top most skin layers. Usually only one top most layer is sufficient to generate higher quality top surfaces."
msgstr "最顶部皮肤层数。 通常只需一层最顶层就足以生成较高质量的顶部表面。"

msgctxt "support_wall_count description"
msgid "The number of walls with which to surround support infill. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "包围支撑的墙的数量。添加一堵墙可以使支持打印更加可靠，并且可以更好地支持挂起，但增加了打印时间和使用的材料。"

msgctxt "support_bottom_wall_count description"
msgid "The number of walls with which to surround support interface floor. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "包围支撑接触面底板的墙的数量。添加墙可以使支持打印更加可靠，并且可以更好地支持悬垂对象，但增加了打印时间和使用的材料。"

msgctxt "support_roof_wall_count description"
msgid "The number of walls with which to surround support interface roof. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "包围支撑接触面顶板的墙的数量。添加墙可以使支持打印更加可靠，并且可以更好地支持悬垂对象，但增加了打印时间和使用的材料。"

msgctxt "support_interface_wall_count description"
msgid "The number of walls with which to surround support interface. Adding a wall can make support print more reliably and can support overhangs better, but increases print time and material used."
msgstr "包围支撑接触面的墙的数量。添加墙可以使支持打印更加可靠，并且可以更好地支持悬垂对象，但增加了打印时间和使用的材料。"

msgctxt "wall_distribution_count description"
msgid "The number of walls, counted from the center, over which the variation needs to be spread. Lower values mean that the outer walls don't change in width."
msgstr "从中心开始计数的壁数量，需要在这些壁上传播变化。较小的值意味着不更改外壁的宽度。"

msgctxt "wall_line_count description"
msgid "The number of walls. When calculated by the wall thickness, this value is rounded to a whole number."
msgstr "壁数量。 在按壁厚计算时，该值舍入为整数。"

msgctxt "machine_nozzle_tip_outer_diameter description"
msgid "The outer diameter of the tip of the nozzle."
msgstr "喷嘴尖端的外径。"

msgctxt "infill_pattern description"
msgid "The pattern of the infill material of the print. The line and zig zag infill swap direction on alternate layers, reducing material cost. The grid, triangle, tri-hexagon, cubic, octet, quarter cubic, cross and concentric patterns are fully printed every layer. Gyroid, cubic, quarter cubic and octet infill change with every layer to provide a more equal distribution of strength over each direction. Lightning infill tries to minimize the infill, by only supporting the ceiling of the object."
msgstr "打印的填充材料的图案。直线和锯齿形填充交替在各层上变换方向，从而降低材料成本。每层都完整地打印网格、三角形、三六边形、立方体、八角形、四分之一立方体、十字和同心图案。螺旋二十四面体、立方体、四分之一立方体和八角形填充随每层变化，以使各方向的强度分布更均衡。闪电形填充尝试通过仅支撑物体顶部，将填充程度降至最低。"

msgctxt "support_pattern description"
msgid "The pattern of the support structures of the print. The different options available result in sturdy or easy to remove support."
msgstr "打印品支撑结构的图案。 提供的不同选项可实现或牢固或易于拆除的支撑。"

msgctxt "roofing_pattern description"
msgid "The pattern of the top most layers."
msgstr "最顶层图案。"

msgctxt "top_bottom_pattern description"
msgid "The pattern of the top/bottom layers."
msgstr "顶层/底层图案。"

msgctxt "top_bottom_pattern_0 description"
msgid "The pattern on the bottom of the print on the first layer."
msgstr "打印品底部第一层上的图案。"

msgctxt "ironing_pattern description"
msgid "The pattern to use for ironing top surfaces."
msgstr "用于熨平顶部表面的图案。"

msgctxt "support_bottom_pattern description"
msgid "The pattern with which the floors of the support are printed."
msgstr "打印支撑底板的图案。"

msgctxt "support_interface_pattern description"
msgid "The pattern with which the interface of the support with the model is printed."
msgstr "支撑与模型之间接触面的打印图案。"

msgctxt "support_roof_pattern description"
msgid "The pattern with which the roofs of the support are printed."
msgstr "打印支撑顶板的图案。"

msgctxt "z_seam_position description"
msgid "The position near where to start printing each part in a layer."
msgstr "在该位置附近开始打印层中各个部分。"

msgctxt "support_tree_angle_slow description"
msgid "The preferred angle of the branches, when they do not have to avoid the model. Use a lower angle to make them more vertical and more stable. Use a higher angle for branches to merge faster."
msgstr "不必避开模型时，分支的偏好角度。使用较小的角度可增加垂直度和稳定性。使用较大的角度可以更快合并分支。"

msgctxt "support_tree_rest_preference description"
msgid "The preferred placement of the support structures. If structures can't be placed at the preferred location, they will be place elsewhere, even if that means placing them on the model."
msgstr "支撑结构的偏好位置。只要结构不在偏好位置，它们就可能被放在其他区域，即使这意味着它们可能被放在模型上。"

msgctxt "jerk_layer_0 description"
msgid "The print maximum instantaneous velocity change for the initial layer."
msgstr "起始层的打印最大瞬时速度变化。"

msgctxt "machine_shape description"
msgid "The shape of the build plate without taking unprintable areas into account."
msgstr "打印平台形状（不考虑不可打印区域）。"

msgctxt "machine_head_with_fans_polygon description"
msgid "The shape of the print head. These are coordinates relative to the position of the print head, which is usually the position of its first extruder. The dimensions left and in front of the print head must be negative coordinates."
msgstr "打印头的形状。这些是相对于打印头位置的坐标，打印头通常是其第一个挤出器的位置。打印头左侧和前方的尺寸必须采用负坐标。"

msgctxt "cross_infill_pocket_size description"
msgid "The size of pockets at four-way crossings in the cross 3D pattern at heights where the pattern is touching itself."
msgstr "交叉 3D 图案的四向交叉处的气槽大小，高度为图案与自身接触的位置。"

msgctxt "coasting_min_volume description"
msgid "The smallest volume an extrusion path should have before allowing coasting. For smaller extrusion paths, less pressure has been built up in the bowden tube and so the coasted volume is scaled linearly. This value should always be larger than the Coasting Volume."
msgstr "挤出路径在可以进行滑行前应拥有的最小体积。 对于较小的挤出路径，鲍登管内累积的压力较少，因此滑行空间采用线性扩展。 该值应始终大于滑行空间。"

msgctxt "machine_nozzle_cool_down_speed description"
msgid "The speed (°C/s) by which the nozzle cools down averaged over the window of normal printing temperatures and the standby temperature."
msgstr "喷嘴冷却到平均超过正常打印温度和待机温度范围的速度 (°C/s)。"

msgctxt "machine_nozzle_heat_up_speed description"
msgid "The speed (°C/s) by which the nozzle heats up averaged over the window of normal printing temperatures and the standby temperature."
msgstr "喷嘴升温到平均超过正常打印温度和待机温度范围的速度 (°C/s)。"

msgctxt "speed_wall_x description"
msgid "The speed at which all inner walls are printed. Printing the inner wall faster than the outer wall will reduce printing time. It works well to set this in between the outer wall speed and the infill speed."
msgstr "打印所有内壁的速度。 以比外壁更快的速度打印内壁将减少打印时间。 将该值设为外壁速度和填充速度之间也可行。"

msgctxt "bridge_skin_speed description"
msgid "The speed at which bridge skin regions are printed."
msgstr "打印连桥表面区域的速度。"

msgctxt "speed_infill description"
msgid "The speed at which infill is printed."
msgstr "打印填充的速度。"

msgctxt "speed_print description"
msgid "The speed at which printing happens."
msgstr "打印发生的速度。"

msgctxt "raft_base_speed description"
msgid "The speed at which the base raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "打印基础 Raft 层的速度。 该层应以很慢的速度打印，因为喷嘴所出的材料量非常高。"

msgctxt "bridge_wall_speed description"
msgid "The speed at which the bridge walls are printed."
msgstr "打印桥壁的速度。"

msgctxt "cool_fan_speed_0 description"
msgid "The speed at which the fans spin at the start of the print. In subsequent layers the fan speed is gradually increased up to the layer corresponding to Regular Fan Speed at Height."
msgstr "风扇在打印开始时旋转的速度。 在随后的层中，风扇速度逐渐增加到对应“正常风扇速度（高度）”的水平。"

msgctxt "cool_fan_speed_min description"
msgid "The speed at which the fans spin before hitting the threshold. When a layer prints faster than the threshold, the fan speed gradually inclines towards the maximum fan speed."
msgstr "风扇旋转达到阈值前的速度。 当一层的打印速度超过阈值时，风扇速度逐渐朝最大风扇速度增加。"

msgctxt "cool_fan_speed_max description"
msgid "The speed at which the fans spin on the minimum layer time. The fan speed gradually increases between the regular fan speed and maximum fan speed when the threshold is hit."
msgstr "风扇在最小层时间上旋转的速度。 当达到阈值时，风扇速度在正常风扇速度和最大风扇速度之间逐渐增加。"

msgctxt "retraction_prime_speed description"
msgid "The speed at which the filament is primed during a retraction move."
msgstr "回抽移动期间耗材装填的速度。"

msgctxt "wipe_retraction_prime_speed description"
msgid "The speed at which the filament is primed during a wipe retraction move."
msgstr "擦拭回抽移动期间耗材装填的速度。"

msgctxt "switch_extruder_prime_speed description"
msgid "The speed at which the filament is pushed back after a nozzle switch retraction."
msgstr "喷嘴切换回抽后耗材被推回的速度。"

msgctxt "retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a retraction move."
msgstr "回抽移动期间耗材回抽和装填的速度。"

msgctxt "wipe_retraction_speed description"
msgid "The speed at which the filament is retracted and primed during a wipe retraction move."
msgstr "擦拭回抽移动期间耗材回抽和装填的速度。"

msgctxt "switch_extruder_retraction_speed description"
msgid "The speed at which the filament is retracted during a nozzle switch retract."
msgstr "喷嘴切换回抽期间耗材回抽的速度。"

msgctxt "retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a retraction move."
msgstr "回抽移动期间耗材回抽的速度。"

msgctxt "wipe_retraction_retract_speed description"
msgid "The speed at which the filament is retracted during a wipe retraction move."
msgstr "擦拭回抽移动期间耗材回抽的速度。"

msgctxt "switch_extruder_retraction_speeds description"
msgid "The speed at which the filament is retracted. A higher retraction speed works better, but a very high retraction speed can lead to filament grinding."
msgstr "回抽耗材的速度。 较高的回抽速度效果较好，但回抽速度过高可能导致耗材磨损。"

msgctxt "speed_support_bottom description"
msgid "The speed at which the floor of support is printed. Printing it at lower speed can improve adhesion of support on top of your model."
msgstr "打印支撑底板的速度。 以较低的速度打印可以改善支撑在模型顶部的粘着。"

msgctxt "speed_support_infill description"
msgid "The speed at which the infill of support is printed. Printing the infill at lower speeds improves stability."
msgstr "打印支撑填充物的速度。 以较低的速度打印填充物可改善稳定性。"

msgctxt "raft_interface_speed description"
msgid "The speed at which the middle raft layer is printed. This should be printed quite slowly, as the volume of material coming out of the nozzle is quite high."
msgstr "打印中间 Raft 层的速度。 该层应以很慢的速度打印，因为喷嘴所出的材料量非常高。"

msgctxt "speed_wall_0 description"
msgid "The speed at which the outermost walls are printed. Printing the outer wall at a lower speed improves the final skin quality. However, having a large difference between the inner wall speed and the outer wall speed will affect quality in a negative way."
msgstr "打印最外壁的速度。 以较低速度打印外壁可改善最终皮肤质量。 但是，如果内壁速度和外壁速度差距过大，则将对质量产生负面影响。"

msgctxt "speed_prime_tower description"
msgid "The speed at which the prime tower is printed. Printing the prime tower slower can make it more stable when the adhesion between the different filaments is suboptimal."
msgstr "打印装填塔的速度。 以较慢速度打印装填塔可以在不同耗材之间的粘着欠佳时使其更加稳定。"

msgctxt "cool_fan_speed description"
msgid "The speed at which the print cooling fans spin."
msgstr "打印冷却风扇旋转的速度。"

msgctxt "raft_speed description"
msgid "The speed at which the raft is printed."
msgstr "打印 Raft 的速度。"

msgctxt "speed_support_interface description"
msgid "The speed at which the roofs and floors of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "打印支撑顶板和底板的速度。 以较低的速度打印可以改善悬垂质量。"

msgctxt "speed_support_roof description"
msgid "The speed at which the roofs of support are printed. Printing them at lower speeds can improve overhang quality."
msgstr "打印支撑顶板的速度。 以较低的速度打印可以改善悬垂质量。"

msgctxt "skirt_brim_speed description"
msgid "The speed at which the skirt and brim are printed. Normally this is done at the initial layer speed, but sometimes you might want to print the skirt or brim at a different speed."
msgstr "打印 skirt 和 brim 的速度。 一般情况是以起始层速度打印这些部分，但有时候您可能想要以不同速度来打印 skirt 或 brim。"

msgctxt "speed_support description"
msgid "The speed at which the support structure is printed. Printing support at higher speeds can greatly reduce printing time. The surface quality of the support structure is not important since it is removed after printing."
msgstr "打印支撑结构的速度。 以更高的速度打印支撑可极大地缩短打印时间。 支撑结构的表面质量并不重要，因为在打印后会将其移除。"

msgctxt "raft_surface_speed description"
msgid "The speed at which the top raft layers are printed. These should be printed a bit slower, so that the nozzle can slowly smooth out adjacent surface lines."
msgstr "打印顶部 Raft 层的速度。 这些层应以较慢的速度打印，以便喷嘴缓慢地整平临近的表面走线。"

msgctxt "speed_wall_x_roofing description"
msgid "The speed at which the top surface inner walls are printed."
msgstr "顶面内壁打印完成后的速度"

msgctxt "speed_wall_0_roofing description"
msgid "The speed at which the top surface outermost wall is printed."
msgstr "顶面最外壁打印完成后的速度"

msgctxt "speed_z_hop description"
msgid "The speed at which the vertical Z movement is made for Z Hops. This is typically lower than the print speed since the build plate or machine's gantry is harder to move."
msgstr "Z 垂直移动实现抬升的速度。一般小于打印速度，因为打印平台或打印机的十字轴较难移动。"

msgctxt "speed_wall description"
msgid "The speed at which the walls are printed."
msgstr "打印壁的速度。"

msgctxt "speed_ironing description"
msgid "The speed at which to pass over the top surface."
msgstr "通过顶部表面的速度。"

msgctxt "material_break_speed description"
msgid "The speed at which to retract the filament in order to break it cleanly."
msgstr "为完全脱落耗材而抽回耗材的速度。"

msgctxt "speed_roofing description"
msgid "The speed at which top surface skin layers are printed."
msgstr "打印顶部表面皮肤层的速度。"

msgctxt "speed_topbottom description"
msgid "The speed at which top/bottom layers are printed."
msgstr "打印顶部/底部层的速度。"

msgctxt "speed_travel description"
msgid "The speed at which travel moves are made."
msgstr "进行空驶的速度。"

msgctxt "coasting_speed description"
msgid "The speed by which to move during coasting, relative to the speed of the extrusion path. A value slightly under 100% is advised, since during the coasting move the pressure in the bowden tube drops."
msgstr "滑行期间的移动速度，相对于挤出路径的速度。 建议采用略低于 100% 的值，因为在滑行移动期间鲍登管中的压力会下降。"

msgctxt "speed_layer_0 description"
msgid "The speed for the initial layer. A lower value is advised to improve adhesion to the build plate. Does not affect the build plate adhesion structures themselves, like brim and raft."
msgstr "起始层的速度。建议采用较低的值以便改善与构建板的粘着。不会影响构建板自身的粘着结构，如边沿和筏。"

msgctxt "speed_print_layer_0 description"
msgid "The speed of printing for the initial layer. A lower value is advised to improve adhesion to the build plate."
msgstr "打印起始层的速度。 建议采用较低的值以便改善与打印平台的粘着。"

msgctxt "speed_travel_layer_0 description"
msgid "The speed of travel moves in the initial layer. A lower value is advised to prevent pulling previously printed parts away from the build plate. The value of this setting can automatically be calculated from the ratio between the Travel Speed and the Print Speed."
msgstr "起始层中的空驶速度。 建议采用较低的值，以防止将之前打印的部分从打印平台上拉离。 该设置的值可以根据空驶速度和打印速度的比率自动计算得出。"

msgctxt "material_break_temperature description"
msgid "The temperature at which the filament is broken for a clean break."
msgstr "耗材在完全脱落时的温度。"

msgctxt "build_volume_temperature description"
msgid "The temperature of the environment to print in. If this is 0, the build volume temperature will not be adjusted."
msgstr "打印环境温度。若为 0，将不会调整构建体积温度。"

msgctxt "material_standby_temperature description"
msgid "The temperature of the nozzle when another nozzle is currently used for printing."
msgstr "当另一个喷嘴正用于打印时该喷嘴的温度。"

msgctxt "material_final_print_temperature description"
msgid "The temperature to which to already start cooling down just before the end of printing."
msgstr "打印结束前开始冷却的温度。"

msgctxt "material_print_temperature_layer_0 description"
msgid "The temperature used for printing the first layer."
msgstr "打印第一层时使用的温度。"

msgctxt "material_print_temperature description"
msgid "The temperature used for printing."
msgstr "用于打印的温度。"

msgctxt "material_bed_temperature_layer_0 description"
msgid "The temperature used for the heated build plate at the first layer. If this is 0, the build plate is left unheated during the first layer."
msgstr "打印第一层时用于加热构建板的温度。如果此项为 0，则在打印第一层期间保持不加热构建板。"

msgctxt "material_bed_temperature description"
msgid "The temperature used for the heated build plate. If this is 0, the build plate is left unheated."
msgstr "用于加热构建板的温度。如果此项为 0，则保持不加热构建板。"

msgctxt "material_break_preparation_temperature description"
msgid "The temperature used to purge material, should be roughly equal to the highest possible printing temperature."
msgstr "用于清除材料的温度，应大致等于可达到的最高打印温度。"

msgctxt "bottom_thickness description"
msgid "The thickness of the bottom layers in the print. This value divided by the layer height defines the number of bottom layers."
msgstr "打印品中底层的厚度。 此值除以层高定义底层数量。"

msgctxt "skin_edge_support_thickness description"
msgid "The thickness of the extra infill that supports skin edges."
msgstr "支撑皮肤边缘的额外填充物的厚度。"

msgctxt "support_interface_height description"
msgid "The thickness of the interface of the support where it touches with the model on the bottom or the top."
msgstr "支撑与模型在底部或顶部接触的接触面厚度。"

msgctxt "support_bottom_height description"
msgid "The thickness of the support floors. This controls the number of dense layers that are printed on top of places of a model on which support rests."
msgstr "支撑底板的厚度。 这会控制支撑所停放的模型顶部区域所打印的密集层数量。"

msgctxt "support_roof_height description"
msgid "The thickness of the support roofs. This controls the amount of dense layers at the top of the support on which the model rests."
msgstr "支撑顶板的厚度。 这会控制模型所停放的支撑顶部密集层的数量。"

msgctxt "top_thickness description"
msgid "The thickness of the top layers in the print. This value divided by the layer height defines the number of top layers."
msgstr "打印品中顶层的厚度。 该值除以层高定义顶层的数量。"

msgctxt "top_bottom_thickness description"
msgid "The thickness of the top/bottom layers in the print. This value divided by the layer height defines the number of top/bottom layers."
msgstr "打印品中顶层/底层的厚度。 该值除以层高定义顶层/底层的数量。"

msgctxt "wall_thickness description"
msgid "The thickness of the walls in the horizontal direction. This value divided by the wall line width defines the number of walls."
msgstr "水平方向的壁厚度。 此值除以壁线宽度定义壁数量。"

msgctxt "infill_sparse_thickness description"
msgid "The thickness per layer of infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "填充材料每层的厚度。 该值应始终为层高的乘数，否则应进行舍入。"

msgctxt "support_infill_sparse_thickness description"
msgid "The thickness per layer of support infill material. This value should always be a multiple of the layer height and is otherwise rounded."
msgstr "支撑填充材料每层的厚度。 该值应始终为层高的乘数，否则应进行舍入。"

msgctxt "machine_gcode_flavor description"
msgid "The type of g-code to be generated."
msgstr "需要生成的 G-code 类型。"

msgctxt "material_type description"
msgid "The type of material used."
msgstr "所用的材料类型。"

msgctxt "coasting_volume description"
msgid "The volume otherwise oozed. This value should generally be close to the nozzle diameter cubed."
msgstr "该体积如不进行滑行则会渗出。 该值一般应接近喷嘴立方直径。"

msgctxt "machine_width description"
msgid "The width (X-direction) of the printable area."
msgstr "机器可打印区域宽度（X 坐标）"

msgctxt "support_brim_width description"
msgid "The width of the brim to print underneath the support. A larger brim enhances adhesion to the build plate, at the cost of some extra material."
msgstr "在支撑下方要打印的 Brim 的宽度。较大的 Brim 可增强与打印平台的附着，但也会增加一些额外材料成本。"

msgctxt "interlocking_beam_width description"
msgid "The width of the interlocking structure beams."
msgstr "互锁结构梁的宽度。"

msgctxt "prime_tower_base_size description"
msgid "The width of the prime tower brim/base. A larger base enhances adhesion to the build plate, but also reduces the effective print area."
msgstr "Prime tower 边缘/底座的宽度。更大的底座可以增强对打印板的粘附力，但也会减少有效打印区域。"

msgctxt "prime_tower_size description"
msgid "The width of the prime tower."
msgstr "装填塔的宽度。"

msgctxt "magic_fuzzy_skin_thickness description"
msgid "The width within which to jitter. It's advised to keep this below the outer wall width, since the inner walls are unaltered."
msgstr "在其中进行抖动的宽度。 建议让此值低于外壁宽度，因为内壁不会更改。"

msgctxt "retraction_extrusion_window description"
msgid "The window in which the maximum retraction count is enforced. This value should be approximately the same as the retraction distance, so that effectively the number of times a retraction passes the same patch of material is limited."
msgstr "执行最大回抽计数的范围。 该值应与回抽距离大致相同，以便一次回抽通过同一块材料的次数得到有效限制。"

msgctxt "prime_tower_position_x description"
msgid "The x coordinate of the position of the prime tower."
msgstr "装填塔位置的 X 坐标。"

msgctxt "prime_tower_position_y description"
msgid "The y coordinate of the position of the prime tower."
msgstr "装填塔位置的 y 坐标。"

msgctxt "support_meshes_present description"
msgid "There are support meshes present in the scene. This setting is controlled by Cura."
msgstr "场景中存在支撑网格。此设置受 Cura 控制。"

msgctxt "bridge_wall_coast description"
msgid "This controls the distance the extruder should coast immediately before a bridge wall begins. Coasting before the bridge starts can reduce the pressure in the nozzle and may produce a flatter bridge."
msgstr "此参数用于控制挤出机在开始打印桥壁前应该滑行的距离。在开始打印连桥之前滑行，可以降低喷嘴中的压力，并保证打印出平滑的连桥。"

msgctxt "raft_base_smoothing description"
msgid "This setting controls how much inner corners in the raft base outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "此设置用于调整筏层基段轮廓线内角的倒圆角大小。内角被倒角为半圆,其半径等于此处给出的值。此设置还会移除筏层轮廓线中面积小于此半径值圆的孔。"

msgctxt "raft_interface_smoothing description"
msgid "This setting controls how much inner corners in the raft middle outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "此设置用于调整筏层中段轮廓线内角的倒圆角大小。内角被倒角为半圆,其半径等于此处给出的值。此设置还会移除筏层轮廓线中面积小于此半径值圆的孔。"

msgctxt "raft_smoothing description"
msgid "This setting controls how much inner corners in the raft outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "该设置控制 Raft 轮廓中的内角呈圆形的程度。内向角被设置为半圆形，半径等于此处的值。此设置还会移除 raft 轮廓中小于此半圆形的孔。"

msgctxt "raft_surface_smoothing description"
msgid "This setting controls how much inner corners in the raft top outline are rounded. Inward corners are rounded to a semi circle with a radius equal to the value given here. This setting also removes holes in the raft outline which are smaller than such a circle."
msgstr "此设置用于调整筏层顶段轮廓线内角的倒圆角大小。内角被倒角为半圆,其半径等于此处给出的值。此设置还会移除筏层轮廓线中面积小于此半径值圆的孔。"

msgctxt "retraction_count_max description"
msgid "This setting limits the number of retractions occurring within the minimum extrusion distance window. Further retractions within this window will be ignored. This avoids retracting repeatedly on the same piece of filament, as that can flatten the filament and cause grinding issues."
msgstr "此设置限制在最小挤出距离范围内发生的回抽数。 此范围内的额外回抽将会忽略。 这避免了在同一件耗材上重复回抽，从而导致耗材变扁并引起磨损问题。"

msgctxt "draft_shield_enabled description"
msgid "This will create a wall around the model, which traps (hot) air and shields against exterior airflow. Especially useful for materials which warp easily."
msgstr "这将在模型周围创建一个壁，该壁会吸住（热）空气并遮住外部气流。 对于容易卷曲的材料尤为有用。"

msgctxt "support_tree_tip_diameter label"
msgid "Tip Diameter"
msgstr "顶端直径"

msgctxt "material_shrinkage_percentage_xy description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the XY-direction (horizontally)."
msgstr "为了补偿材料在冷却时的收缩，将用此因子在 XY 方向（水平）上缩放模型。"

msgctxt "material_shrinkage_percentage_z description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor in the Z-direction (vertically)."
msgstr "为了补偿材料在冷却时的收缩，将用此因子在 Z 方向（垂直）上缩放模型。"

msgctxt "material_shrinkage_percentage description"
msgid "To compensate for the shrinkage of the material as it cools down, the model will be scaled with this factor."
msgstr "为了补偿材料在冷却时的收缩，将用此因子缩放模型。"

msgctxt "top_layers label"
msgid "Top Layers"
msgstr "顶部层数"

msgctxt "top_skin_expand_distance label"
msgid "Top Skin Expand Distance"
msgstr "顶部皮肤扩展距离"

msgctxt "top_skin_preshrink label"
msgid "Top Skin Removal Width"
msgstr "顶部皮肤移除宽度"

msgctxt "acceleration_wall_x_roofing label"
msgid "Top Surface Inner Wall Acceleration"
msgstr "顶面内壁加速度"

msgctxt "jerk_wall_x_roofing label"
msgid "Top Surface Inner Wall Jerk"
msgstr "顶面内壁冲击力"

msgctxt "speed_wall_x_roofing label"
msgid "Top Surface Inner Wall Speed"
msgstr "顶面内壁速度"

msgctxt "wall_x_material_flow_roofing label"
msgid "Top Surface Inner Wall(s) Flow"
msgstr "顶面内壁挤出量"

msgctxt "acceleration_wall_0_roofing label"
msgid "Top Surface Outer Wall Acceleration"
msgstr "顶面外壁加速度"

msgctxt "wall_0_material_flow_roofing label"
msgid "Top Surface Outer Wall Flow"
msgstr "顶面外壁挤出量"

msgctxt "jerk_wall_0_roofing label"
msgid "Top Surface Outer Wall Jerk"
msgstr "顶面外壁冲击力"

msgctxt "speed_wall_0_roofing label"
msgid "Top Surface Outer Wall Speed"
msgstr "顶面外壁速度"

msgctxt "acceleration_roofing label"
msgid "Top Surface Skin Acceleration"
msgstr "顶部表面皮肤加速度"

msgctxt "roofing_extruder_nr label"
msgid "Top Surface Skin Extruder"
msgstr "顶部皮肤挤出机"

msgctxt "roofing_material_flow label"
msgid "Top Surface Skin Flow"
msgstr "顶部表层流量"

msgctxt "jerk_roofing label"
msgid "Top Surface Skin Jerk"
msgstr "顶部表面皮肤抖动速度"

msgctxt "roofing_layer_count label"
msgid "Top Surface Skin Layers"
msgstr "顶部表面皮肤层"

msgctxt "roofing_angles label"
msgid "Top Surface Skin Line Directions"
msgstr "顶部表面皮肤走线方向"

msgctxt "roofing_line_width label"
msgid "Top Surface Skin Line Width"
msgstr "顶部表面皮肤线宽"

msgctxt "roofing_pattern label"
msgid "Top Surface Skin Pattern"
msgstr "顶部表面皮肤图案"

msgctxt "speed_roofing label"
msgid "Top Surface Skin Speed"
msgstr "顶部表面皮肤速度"

msgctxt "top_thickness label"
msgid "Top Thickness"
msgstr "顶层厚度"

msgctxt "max_skin_angle_for_expansion description"
msgid "Top and/or bottom surfaces of your object with an angle larger than this setting, won't have their top/bottom skin expanded. This avoids expanding the narrow skin areas that are created when the model surface has a near vertical slope. An angle of 0° is horizontal and will cause no skin to be expanded, while an angle of 90° is vertical and will cause all skin to be expanded."
msgstr "如果对象的顶部和/或底部表面的角度大于此设置，则不要扩展其顶部/底部皮肤。这会避免扩展在模型表面有接近垂直的坡度时所形成的狭窄皮肤区域。0° 的角为水平，将导致不扩展任何皮肤，而 90° 的角为垂直，将导致扩展所有皮肤。"

msgctxt "top_bottom description"
msgid "Top/Bottom"
msgstr "顶 / 底层"

msgctxt "top_bottom label"
msgid "Top/Bottom"
msgstr "顶 / 底层"

msgctxt "acceleration_topbottom label"
msgid "Top/Bottom Acceleration"
msgstr "顶部/底部加速度"

msgctxt "top_bottom_extruder_nr label"
msgid "Top/Bottom Extruder"
msgstr "顶部/底部挤出机"

msgctxt "skin_material_flow label"
msgid "Top/Bottom Flow"
msgstr "顶部/底部流量"

msgctxt "jerk_topbottom label"
msgid "Top/Bottom Jerk"
msgstr "顶部/底部抖动速度"

msgctxt "skin_angles label"
msgid "Top/Bottom Line Directions"
msgstr "顶层/底层走线方向"

msgctxt "skin_line_width label"
msgid "Top/Bottom Line Width"
msgstr "走线宽度（顶层 / 底层）"

msgctxt "top_bottom_pattern label"
msgid "Top/Bottom Pattern"
msgstr "顶部 / 底部走线图案"

msgctxt "speed_topbottom label"
msgid "Top/Bottom Speed"
msgstr "速度（顶部 / 底部）"

msgctxt "top_bottom_thickness label"
msgid "Top/Bottom Thickness"
msgstr "顶层 / 底层厚度"

msgctxt "support_type option buildplate"
msgid "Touching Buildplate"
msgstr "支撑打印平台"

msgctxt "support_tower_diameter label"
msgid "Tower Diameter"
msgstr "塔直径"

msgctxt "support_tower_roof_angle label"
msgid "Tower Roof Angle"
msgstr "塔顶板角度"

msgctxt "mesh_rotation_matrix description"
msgid "Transformation matrix to be applied to the model when loading it from file."
msgstr "在将模型从文件中载入时应用在模型上的转换矩阵。"

msgctxt "travel label"
msgid "Travel"
msgstr "移动"

msgctxt "acceleration_travel label"
msgid "Travel Acceleration"
msgstr "空驶加速度"

msgctxt "travel_avoid_distance label"
msgid "Travel Avoid Distance"
msgstr "空驶避让距离"

msgctxt "jerk_travel label"
msgid "Travel Jerk"
msgstr "空驶抖动速度"

msgctxt "speed_travel label"
msgid "Travel Speed"
msgstr "空驶速度"

msgctxt "magic_mesh_surface_mode description"
msgid "Treat the model as a surface only, a volume, or volumes with loose surfaces. The normal print mode only prints enclosed volumes. \"Surface\" prints a single wall tracing the mesh surface with no infill and no top/bottom skin. \"Both\" prints enclosed volumes like normal and any remaining polygons as surfaces."
msgstr "将模型作为仅表面、一个空间或多个具有松散表面的空间处理。 正常打印模式仅打印封闭的空间。 “表面”打印跟踪网格表面的单个壁，没有填充物，也没有顶部/底部皮肤。 \"两者都\"将封闭空间正常打印，并将任何剩余多边形作为表面打印。"

msgctxt "support_structure option tree"
msgid "Tree"
msgstr "树形"

msgctxt "infill_pattern option trihexagon"
msgid "Tri-Hexagon"
msgstr "内六角"

msgctxt "infill_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_bottom_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_interface_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_roof_pattern option triangles"
msgid "Triangles"
msgstr "三角形"

msgctxt "support_tree_max_diameter label"
msgid "Trunk Diameter"
msgstr "主干直径"

msgctxt "machine_gcode_flavor option UltiGCode"
msgid "Ultimaker 2"
msgstr "Ultimaker 2"

msgctxt "meshfix_union_all label"
msgid "Union Overlapping Volumes"
msgstr "联合覆盖体积"

msgctxt "bridge_wall_min_length description"
msgid "Unsupported walls shorter than this will be printed using the normal wall settings. Longer unsupported walls will be printed using the bridge wall settings."
msgstr "将使用正常壁设置打印短于此长度且没有支撑的壁。将使用桥壁设置打印长于此长度且没有支撑的壁。"

msgctxt "adaptive_layer_height_enabled label"
msgid "Use Adaptive Layers"
msgstr "使用自适应图层"

msgctxt "support_use_towers label"
msgid "Use Towers"
msgstr "使用塔"

msgctxt "acceleration_travel_enabled description"
msgid "Use a separate acceleration rate for travel moves. If disabled, travel moves will use the acceleration value of the printed line at their destination."
msgstr "空驶时使用单独的加速度。如果禁用，空驶将使用打印线在目的地的加速度值。"

msgctxt "jerk_travel_enabled description"
msgid "Use a separate jerk rate for travel moves. If disabled, travel moves will use the jerk value of the printed line at their destination."
msgstr "空驶时使用单独的抖动速度。如果禁用，空驶将使用打印线在目的地的抖动速度值。"

msgctxt "relative_extrusion description"
msgid "Use relative extrusion rather than absolute extrusion. Using relative E-steps makes for easier post-processing of the g-code. However, it's not supported by all printers and it may produce very slight deviations in the amount of deposited material compared to absolute E-steps. Irrespective of this setting, the extrusion mode will always be set to absolute before any g-code script is output."
msgstr "使用相对挤出而不是绝对挤出。使用相对 E 步阶，以便对 G-code 进行更轻松的后期处理。但是，并非所有打印机均支持此功能，而且与绝对 E 步阶相比，此功能在沉积材料量上会产生非常轻微的偏差。不论是否启用此设置，挤出模式将始终在设置为绝对挤出后才输出任何 G-code 脚本。"

msgctxt "support_use_towers description"
msgid "Use specialized towers to support tiny overhang areas. These towers have a larger diameter than the region they support. Near the overhang the towers' diameter decreases, forming a roof."
msgstr "使用专门的塔来支撑较小的悬垂区域。 这些塔的直径比它们所支撑的区域要大。 在靠近悬垂物时，塔的直径减小，形成顶板。"

msgctxt "infill_mesh description"
msgid "Use this mesh to modify the infill of other meshes with which it overlaps. Replaces infill regions of other meshes with regions for this mesh. It's suggested to only print one Wall and no Top/Bottom Skin for this mesh."
msgstr "使用此网格修改与其重叠的其他网格的填充物。 利用此网格的区域替换其他网格的填充区域。 建议仅为此网格打印一个壁，而不打印顶部/底部皮肤。"

msgctxt "support_mesh description"
msgid "Use this mesh to specify support areas. This can be used to generate support structure."
msgstr "使用此网格指定支撑区域。 可用于生成支撑结构。"

msgctxt "anti_overhang_mesh description"
msgid "Use this mesh to specify where no part of the model should be detected as overhang. This can be used to remove unwanted support structure."
msgstr "使用此网格指定模型的任何部分不应被检测为悬垂的区域。 可用于移除不需要的支撑结构。"

msgctxt "z_seam_type option back"
msgid "User Specified"
msgstr "用户指定"

msgctxt "material_shrinkage_percentage_z label"
msgid "Vertical Scaling Factor Shrinkage Compensation"
msgstr "垂直缩放因子收缩补偿"

msgctxt "slicing_tolerance description"
msgid "Vertical tolerance in the sliced layers. The contours of a layer are normally generated by taking cross sections through the middle of each layer's thickness (Middle). Alternatively each layer can have the areas which fall inside of the volume throughout the entire thickness of the layer (Exclusive) or a layer has the areas which fall inside anywhere within the layer (Inclusive). Inclusive retains the most details, Exclusive makes for the best fit and Middle stays closest to the original surface."
msgstr "切片层的垂直公差。一般通过穿过每层厚度的中间截取横截面而产生该层的轮廓（中间）。此外，每层均可有一些区域，这些区域落入体积内部并遍布该层的整个厚度（排除），或层具有一些区域，这些区域落入该层内的任意位置（包含）。“包含”保留最多的细节，“排除”有利于最佳贴合，而“中间”保持最接近原始表面。"

msgctxt "material_bed_temp_wait label"
msgid "Wait for Build Plate Heatup"
msgstr "等待打印平台加热"

msgctxt "material_print_temp_wait label"
msgid "Wait for Nozzle Heatup"
msgstr "等待喷嘴加热"

msgctxt "acceleration_wall label"
msgid "Wall Acceleration"
msgstr "壁加速度"

msgctxt "wall_distribution_count label"
msgid "Wall Distribution Count"
msgstr "壁分派次数"

msgctxt "wall_extruder_nr label"
msgid "Wall Extruder"
msgstr "壁挤出机"

msgctxt "wall_material_flow label"
msgid "Wall Flow"
msgstr "壁流量"

msgctxt "jerk_wall label"
msgid "Wall Jerk"
msgstr "壁抖动速度"

msgctxt "wall_line_count label"
msgid "Wall Line Count"
msgstr "壁走线次数"

msgctxt "wall_line_width label"
msgid "Wall Line Width"
msgstr "走线宽度（壁）"

msgctxt "inset_direction label"
msgid "Wall Ordering"
msgstr "壁顺序"

msgctxt "speed_wall label"
msgid "Wall Speed"
msgstr "速度（壁）"

msgctxt "wall_thickness label"
msgid "Wall Thickness"
msgstr "壁厚"

msgctxt "wall_transition_length label"
msgid "Wall Transition Length"
msgstr "壁过渡长度"

msgctxt "wall_transition_filter_distance label"
msgid "Wall Transitioning Filter Distance"
msgstr "壁过渡筛选距离"

msgctxt "wall_transition_filter_deviation label"
msgid "Wall Transitioning Filter Margin"
msgstr "壁过渡筛选边距"

msgctxt "wall_transition_angle label"
msgid "Wall Transitioning Threshold Angle"
msgstr "壁过渡阈值角度"

msgctxt "shell label"
msgid "Walls"
msgstr "墙"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either."
msgstr "悬垂超过此角度的壁将使用悬垂壁设置打印。该值为 90 时，不会将任何壁视为悬垂。受到支撑支持的悬垂也不会被视为悬垂。"

msgctxt "meshfix_fluid_motion_enabled description"
msgid "When enabled tool paths are corrected for printers with smooth motion planners. Small movements that deviate from the general tool path direction are smoothed to improve fluid motions."
msgstr "当启用时，对具有平滑运动规划器的打印机进行刀具路径校正。对偏离一般刀具轨迹方向的小运动进行平滑，改善流体运动。"

msgctxt "infill_enable_travel_optimization description"
msgid "When enabled, the order in which the infill lines are printed is optimized to reduce the distance travelled. The reduction in travel time achieved very much depends on the model being sliced, infill pattern, density, etc. Note that, for some models that have many small areas of infill, the time to slice the model may be greatly increased."
msgstr "启用后，可优化打印填充走线的顺序，缩短空驶距离。空驶时间的缩短很大程度上取决于被切割的模型、填充图案、密度等。请注意，对于具有许多小填充区域的一些模型，分割模型的时间可能会大幅增加。"

msgctxt "support_fan_enable description"
msgid "When enabled, the print cooling fan speed is altered for the skin regions immediately above the support."
msgstr "启用时，会为支撑正上方的表面区域更改打印冷却风扇速度。"

msgctxt "z_seam_relative description"
msgid "When enabled, the z seam coordinates are relative to each part's centre. When disabled, the coordinates define an absolute position on the build plate."
msgstr "启用时，Z 缝坐标为相对于各个部分中心的值。 禁用时，坐标定义打印平台上的一个绝对位置。"

msgctxt "retraction_combing_max_distance description"
msgid "When greater than zero, combing travel moves that are longer than this distance will use retraction. If set to zero, there is no maximum and combing moves will not use retraction."
msgstr "当大于零时，比这段距离更长的梳理空驶将会使用回抽。如果设置为零，则没有最大值，梳理空驶将不会使用回抽。"

msgctxt "hole_xy_offset_max_diameter description"
msgid "When greater than zero, the Hole Horizontal Expansion is gradually applied on small holes (small holes are expanded more). When set to zero the Hole Horizontal Expansion will be applied to all holes. Holes larger than the Hole Horizontal Expansion Max Diameter are not expanded."
msgstr "大于零时，孔洞水平扩展会逐渐适应小孔洞（小孔洞可以扩展更多）。设为零时，孔洞水平扩展可以应用于所有孔洞。大于孔洞水平扩展最大直径时，孔洞不会被扩展。"

msgctxt "hole_xy_offset description"
msgid "When greater than zero, the Hole Horizontal Expansion is the amount of offset applied to all holes in each layer. Positive values increase the size of the holes, negative values reduce the size of the holes. When this setting is enabled it can be further tuned with Hole Horizontal Expansion Max Diameter."
msgstr "当大于零时，“孔水平膨胀”是应用于每层所有孔的偏移量。正值会增加孔的大小，负值会减少孔的大小。当此设置启用时，可以使用“孔水平膨胀最大直径”进一步细化。"

msgctxt "bridge_skin_material_flow description"
msgid "When printing bridge skin regions, the amount of material extruded is multiplied by this value."
msgstr "打印连桥表面区域时，将挤出的材料量乘以此值。"

msgctxt "bridge_wall_material_flow description"
msgid "When printing bridge walls, the amount of material extruded is multiplied by this value."
msgstr "打印桥壁时，将挤出的材料量乘以此值。"

msgctxt "bridge_skin_material_flow_2 description"
msgid "When printing the second bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "打印连桥第二层表面时，将挤出的材料量乘以此值。"

msgctxt "bridge_skin_material_flow_3 description"
msgid "When printing the third bridge skin layer, the amount of material extruded is multiplied by this value."
msgstr "打印连桥第三层表面时，将挤出的材料量乘以此值。"

msgctxt "cool_lift_head description"
msgid "When the minimum speed is hit because of minimum layer time, lift the head away from the print and wait the extra time until the minimum layer time is reached."
msgstr "当因最低层时间达到最低速度时，将打印头从打印品上提升，并等候达到最低层时间。"

msgctxt "skin_no_small_gaps_heuristic description"
msgid "When the model has small vertical gaps of only a few layers, there should normally be skin around those layers in the narrow space. Enable this setting to not generate skin if the vertical gap is very small. This improves printing time and slicing time, but technically leaves infill exposed to the air."
msgstr "当模型中只有几个分层有微小垂直间隙时，通常狭窄空间的分层周围应有表层。如果垂直间隙非常小，则启用此设置不生成表层。这缩短了打印时间和切片时间，但从技术方面看，会使填充物暴露在空气中。"

msgctxt "wall_transition_angle description"
msgid "When to create transitions between even and odd numbers of walls. A wedge shape with an angle greater than this setting will not have transitions and no walls will be printed in the center to fill the remaining space. Reducing this setting reduces the number and length of these center walls, but may leave gaps or overextrude."
msgstr "在奇数和偶数壁之间创建过渡时。角度大于此设置的楔形将没有过渡，并且不会在中心打印壁来填充剩余空间。减少此设置会减少这些中心壁的数量和长度，但可能会留下空隙或挤出过多。"

msgctxt "wall_transition_length description"
msgid "When transitioning between different numbers of walls as the part becomes thinner, a certain amount of space is allotted to split or join the wall lines."
msgstr "当随着零件变薄而在不同数量的壁之间过渡时，会分配一定数量的间距来分割或连接壁走线。"

msgctxt "wipe_hop_enable description"
msgid "When wiping, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "在擦拭时，构建板会降低以在喷嘴与打印件之间形成间隙。这样可防止喷嘴在行程中撞击打印件，降低从构建板上撞掉打印件的可能性。"

msgctxt "retraction_hop_enabled description"
msgid "Whenever a retraction is done, the build plate is lowered to create clearance between the nozzle and the print. It prevents the nozzle from hitting the print during travel moves, reducing the chance to knock the print from the build plate."
msgstr "每当回抽完成时，打印平台会降低以便在喷嘴和打印品之间形成空隙。 它可以防止喷嘴在空驶过程中撞到打印品，降低将打印品从打印平台撞掉的几率。"

msgctxt "support_xy_overrides_z description"
msgid "Whether the Support X/Y Distance overrides the Support Z Distance or vice versa. When X/Y overrides Z the X/Y distance can push away the support from the model, influencing the actual Z distance to the overhang. We can disable this by not applying the X/Y distance around overhangs."
msgstr "支撑 X/Y 距离是否覆盖支撑 Z 距离或反之。 当 X/Y 覆盖 Z 时，X/Y 距离可将支撑从模型上推离，影响与悬垂之间的实际 Z 距离。 我们可以通过不在悬垂周围应用 X/Y 距离来禁用此选项。"

msgctxt "machine_center_is_zero description"
msgid "Whether the X/Y coordinates of the zero position of the printer is at the center of the printable area."
msgstr "打印机零位的 X/Y 坐标是否位于可打印区域的中心。"

msgctxt "machine_endstop_positive_direction_x description"
msgid "Whether the endstop of the X axis is in the positive direction (high X coordinate) or negative (low X coordinate)."
msgstr "指定 X 轴的限位开关位于正向（高 X 轴坐标）还是负向（低 X 轴坐标）。"

msgctxt "machine_endstop_positive_direction_y description"
msgid "Whether the endstop of the Y axis is in the positive direction (high Y coordinate) or negative (low Y coordinate)."
msgstr "指定 Y 轴的限位开关位于正向（高 Y 轴坐标）还是负向（低 Y 轴坐标）。"

msgctxt "machine_endstop_positive_direction_z description"
msgid "Whether the endstop of the Z axis is in the positive direction (high Z coordinate) or negative (low Z coordinate)."
msgstr "指定 Z 轴的限位开关位于正向（高 Z 轴坐标）还是负向（低 Z 轴坐标）。"

msgctxt "machine_extruders_share_heater description"
msgid "Whether the extruders share a single heater rather than each extruder having its own heater."
msgstr "挤出器是否共用一个加热器，而不是每个挤出器都有自己的加热器。"

msgctxt "machine_extruders_share_nozzle description"
msgid "Whether the extruders share a single nozzle rather than each extruder having its own nozzle. When set to true, it is expected that the printer-start gcode script properly sets up all extruders in an initial retraction state that is known and mutually compatible (either zero or one filament not retracted); in that case the initial retraction status is described, per extruder, by the 'machine_extruders_shared_nozzle_initial_retraction' parameter."
msgstr "挤出器是否共用一个喷嘴，而不是每个挤出器都有自己的喷嘴。当设置为 true 时，预计打印机启动 gcode 脚本会将所有挤出器正确设置为已知且相互兼容的初始缩回状态 (零根或一根细丝未缩回)；在这种情况下，会通过“machine_extruders_shared_nozzle_initial_retraction”参数描述每个挤出器的初始缩回状态。"

msgctxt "machine_heated_bed description"
msgid "Whether the machine has a heated build plate present."
msgstr "机器是否有加热打印平台。"

msgctxt "machine_heated_build_volume description"
msgid "Whether the machine is able to stabilize the build volume temperature."
msgstr "机器是否能够稳定构建体积温度。"

msgctxt "center_object description"
msgid "Whether to center the object on the middle of the build platform (0,0), instead of using the coordinate system in which the object was saved."
msgstr "是否将模型放置在打印平台中心 (0,0)，而不是使用模型在其中保存的坐标系统。"

msgctxt "machine_nozzle_temp_enabled description"
msgid "Whether to control temperature from Cura. Turn this off to control nozzle temperature from outside of Cura."
msgstr "是否从 Cura 控制温度。 关闭此选项，从 Cura 外部控制喷嘴温度。"

msgctxt "material_bed_temp_prepend description"
msgid "Whether to include build plate temperature commands at the start of the gcode. When the start_gcode already contains build plate temperature commands Cura frontend will automatically disable this setting."
msgstr "是否需要在 G-code 开始部分包含检查热床温度的命令。当 start_gcode 包含热床温度命令时，Cura 前端将自动禁用此设置。"

msgctxt "material_print_temp_prepend description"
msgid "Whether to include nozzle temperature commands at the start of the gcode. When the start_gcode already contains nozzle temperature commands Cura frontend will automatically disable this setting."
msgstr "是否在 gcode 开始部分包含喷嘴温度命令。 当 start_gcode 已包含喷嘴温度命令时，Cura 前端将自动禁用此设置。"

msgctxt "clean_between_layers description"
msgid "Whether to include nozzle wipe G-Code between layers (maximum 1 per layer). Enabling this setting could influence behavior of retract at layer change. Please use Wipe Retraction settings to control retraction at layers where the wipe script will be working."
msgstr "是否包括图层切换后擦拭喷嘴的 G-Code（每层最多 1 个）。启用此设置可能会影响图层变化时的回抽。请使用“擦拭回抽”设置来控制擦拭脚本将在其中工作的图层回抽。"

msgctxt "material_bed_temp_wait description"
msgid "Whether to insert a command to wait until the build plate temperature is reached at the start."
msgstr "是否插入一条命令，等待开始时达到打印平台温度。"

msgctxt "prime_blob_enable description"
msgid "Whether to prime the filament with a blob before printing. Turning this setting on will ensure that the extruder will have material ready at the nozzle before printing. Printing Brim or Skirt can act like priming too, in which case turning this setting off saves some time."
msgstr "打印前是否装填有光点的耗材。 开启此设置将确保打印前挤出机的喷嘴处已准备好材料。 打印 Brim 或 Skirt 也可作为装填用途，这种情况下关闭此设置可以节省时间。"

msgctxt "print_sequence description"
msgid "Whether to print all models one layer at a time or to wait for one model to finish, before moving on to the next. One at a time mode is possible if a) only one extruder is enabled and b) all models are separated in such a way that the whole print head can move in between and all models are lower than the distance between the nozzle and the X/Y axes."
msgstr "是要一次一层地打印所有模型，还是要等待打印完一个模型后再继续打印下一个。如果 a) 仅启用了一个挤出器，并且 b) 分离所有模型的方式使得整个打印头可在这些模型间移动，并且所有模型都低于喷嘴与 X/Y 轴之间的距离，则可使用排队打印模式。"

msgctxt "machine_show_variants description"
msgid "Whether to show the different variants of this machine, which are described in separate json files."
msgstr "这台打印机是否需要显示它在不同的 JSON 文件中所描述的不同变化。"

msgctxt "machine_firmware_retract description"
msgid "Whether to use firmware retract commands (G10/G11) instead of using the E property in G1 commands to retract the material."
msgstr "是否使用固件收回命令 (G10/G11) 而不是使用 G1 命令中的 E 属性来收回材料。"

msgctxt "material_print_temp_wait description"
msgid "Whether to wait until the nozzle temperature is reached at the start."
msgstr "是否等待开始时达到喷嘴温度。"

msgctxt "infill_line_width description"
msgid "Width of a single infill line."
msgstr "单一填充走线宽度。"

msgctxt "support_interface_line_width description"
msgid "Width of a single line of support roof or floor."
msgstr "支撑顶板或底板单一走线宽度。"

msgctxt "roofing_line_width description"
msgid "Width of a single line of the areas at the top of the print."
msgstr "打印顶部区域单一走线宽度。"

msgctxt "line_width description"
msgid "Width of a single line. Generally, the width of each line should correspond to the width of the nozzle. However, slightly reducing this value could produce better prints."
msgstr "单一走线宽度。 一般而言，每条走线的宽度应与喷嘴的宽度对应。 但是，稍微降低此值可以产生更好的打印成果。"

msgctxt "prime_tower_line_width description"
msgid "Width of a single prime tower line."
msgstr "单一装填走线宽度。"

msgctxt "skirt_brim_line_width description"
msgid "Width of a single skirt or brim line."
msgstr "单一 skirt（裙摆）或 brim（边缘）走线宽度。"

msgctxt "support_bottom_line_width description"
msgid "Width of a single support floor line."
msgstr "单一支撑底板走线宽度。"

msgctxt "support_roof_line_width description"
msgid "Width of a single support roof line."
msgstr "单一支撑顶板走线宽度。"

msgctxt "support_line_width description"
msgid "Width of a single support structure line."
msgstr "单一支撑结构走线宽度。"

msgctxt "skin_line_width description"
msgid "Width of a single top/bottom line."
msgstr "单一顶层/底层走线宽度。"

msgctxt "wall_line_width_x description"
msgid "Width of a single wall line for all wall lines except the outermost one."
msgstr "适用于所有壁线（最外壁线除外）的单一壁线宽度。"

msgctxt "wall_line_width description"
msgid "Width of a single wall line."
msgstr "单一壁线宽度。"

msgctxt "raft_base_line_width description"
msgid "Width of the lines in the base raft layer. These should be thick lines to assist in build plate adhesion."
msgstr "基础 Raft 层的走线宽度。 这些走线应该是粗线，以便协助打印平台附着。"

msgctxt "raft_interface_line_width description"
msgid "Width of the lines in the middle raft layer. Making the second layer extrude more causes the lines to stick to the build plate."
msgstr "中间 Raft 层的走线宽度。 让第二层挤出更多会导致走线粘着在打印平台上。"

msgctxt "raft_surface_line_width description"
msgid "Width of the lines in the top surface of the raft. These can be thin lines so that the top of the raft becomes smooth."
msgstr "Raft 顶部表面的走线宽度。 这些走线可以是细线，以便实现平滑的 Raft 顶部。"

msgctxt "wall_line_width_0 description"
msgid "Width of the outermost wall line. By lowering this value, higher levels of detail can be printed."
msgstr "最外壁线宽度。 降低此值，可打印出更高水平的细节。"

msgctxt "min_bead_width description"
msgid "Width of the wall that will replace thin features (according to the Minimum Feature Size) of the model. If the Minimum Wall Line Width is thinner than the thickness of the feature, the wall will become as thick as the feature itself."
msgstr "用于替换模型薄特征（根据最小特征尺寸）的壁的宽度。如果最小壁走线宽度比特征的厚度要薄，则壁将与特征本身一样厚。"

msgctxt "wipe_brush_pos_x label"
msgid "Wipe Brush X Position"
msgstr "擦拭刷 X 轴坐标"

msgctxt "wipe_hop_speed label"
msgid "Wipe Hop Speed"
msgstr "擦拭抬升速度"

msgctxt "prime_tower_wipe_enabled label"
msgid "Wipe Inactive Nozzle on Prime Tower"
msgstr "擦拭装填塔上的不活动喷嘴"

msgctxt "wipe_move_distance label"
msgid "Wipe Move Distance"
msgstr "擦拭移动距离"

msgctxt "clean_between_layers label"
msgid "Wipe Nozzle Between Layers"
msgstr "图层切换后擦拭喷嘴"

msgctxt "wipe_pause label"
msgid "Wipe Pause"
msgstr "擦拭暂停"

msgctxt "wipe_repeat_count label"
msgid "Wipe Repeat Count"
msgstr "擦拭重复计数"

msgctxt "wipe_retraction_amount label"
msgid "Wipe Retraction Distance"
msgstr "擦拭回抽距离"

msgctxt "wipe_retraction_enable label"
msgid "Wipe Retraction Enable"
msgstr "启用擦拭回抽"

msgctxt "wipe_retraction_extra_prime_amount label"
msgid "Wipe Retraction Extra Prime Amount"
msgstr "擦拭回抽额外装填量"

msgctxt "wipe_retraction_prime_speed label"
msgid "Wipe Retraction Prime Speed"
msgstr "擦拭回抽装填速度"

msgctxt "wipe_retraction_retract_speed label"
msgid "Wipe Retraction Retract Speed"
msgstr "擦拭回抽期间的回抽速度"

msgctxt "wipe_retraction_speed label"
msgid "Wipe Retraction Speed"
msgstr "擦拭回抽速度"

msgctxt "wipe_hop_enable label"
msgid "Wipe Z Hop"
msgstr "擦拭 Z 抬升"

msgctxt "wipe_hop_amount label"
msgid "Wipe Z Hop Height"
msgstr "擦拭 Z 抬升高度"

msgctxt "retraction_combing option infill"
msgid "Within Infill"
msgstr "在填充物内"

msgctxt "machine_always_write_active_tool description"
msgid "Write active tool after sending temp commands to inactive tool. Required for Dual Extruder printing with Smoothie or other firmware with modal tool commands."
msgstr "将临时命令发送到非活动工具后写入活动工具。用 Smoothie 或其他具有模态工具命令的固件进行的双挤出器打印需要此项。"

msgctxt "machine_endstop_positive_direction_x label"
msgid "X Endstop in Positive Direction"
msgstr "正向 X 限位开关"

msgctxt "wipe_brush_pos_x description"
msgid "X location where wipe script will start."
msgstr "擦拭开始处的 X 轴坐标。"

msgctxt "support_xy_overrides_z option xy_overrides_z"
msgid "X/Y overrides Z"
msgstr "X/Y 覆盖 Z"

msgctxt "machine_endstop_positive_direction_y label"
msgid "Y Endstop in Positive Direction"
msgstr "正向 Y 限位开关"

msgctxt "machine_endstop_positive_direction_z label"
msgid "Z Endstop in Positive Direction"
msgstr "正向 Z 限位开关"

msgctxt "retraction_hop_after_extruder_switch label"
msgid "Z Hop After Extruder Switch"
msgstr "挤出机切换后的 Z 抬升"

msgctxt "retraction_hop_after_extruder_switch_height label"
msgid "Z Hop After Extruder Switch Height"
msgstr "挤出机切换后的 Z 抬升高度"

msgctxt "retraction_hop label"
msgid "Z Hop Height"
msgstr "Z 抬升高度"

msgctxt "retraction_hop_only_when_collides label"
msgid "Z Hop Only Over Printed Parts"
msgstr "仅在已打印部分上 Z 抬升"

msgctxt "speed_z_hop label"
msgid "Z Hop Speed"
msgstr "Z 抬升速度"

msgctxt "retraction_hop_enabled label"
msgid "Z Hop When Retracted"
msgstr "回抽时 Z 抬升"

msgctxt "z_seam_type label"
msgid "Z Seam Alignment"
msgstr "Z 缝对齐"

msgctxt "z_seam_position label"
msgid "Z Seam Position"
msgstr "Z 缝位置"

msgctxt "z_seam_relative label"
msgid "Z Seam Relative"
msgstr "Z 缝相对"

msgctxt "z_seam_x label"
msgid "Z Seam X"
msgstr "Z 缝 X"

msgctxt "z_seam_y label"
msgid "Z Seam Y"
msgstr "Z 缝 Y"

msgctxt "support_xy_overrides_z option z_overrides_xy"
msgid "Z overrides X/Y"
msgstr "Z 覆盖 X/Y"

msgctxt "infill_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿状"

msgctxt "ironing_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"

msgctxt "roofing_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"

msgctxt "support_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"

msgctxt "support_interface_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"

msgctxt "support_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"

msgctxt "support_roof_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿状"

msgctxt "top_bottom_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿状"

msgctxt "top_bottom_pattern_0 option zigzag"
msgid "Zig Zag"
msgstr "锯齿状"

msgctxt "travel description"
msgid "travel"
msgstr "空驶"

msgctxt "cool_during_extruder_switch description"
msgid "<html>Whether to activate the cooling fans during a nozzle switch. This can help reducing oozing by cooling the nozzle faster:<ul><li><b>Unchanged:</b> keep the fans as they were previously</li><li><b>Only last extruder:</b> turn on the fan of the last used extruder, but turn the others off (if any). This is useful if you have completely separate extruders.</li><li><b>All fans:</b> turn on all fans during nozzle switch. This is useful if you have a single cooling fan, or multiple fans that stay close to each other.</li></ul></html>"
msgstr "<html>是否在喷嘴切换期间启动冷却风扇。这可以通过更快地冷却喷嘴来帮助减少滴漏:<ul><li><b>不变:</b>保持风扇原状</li><li><b>仅最后一个挤出机:</b> 启动最后一个使用的挤出机的风扇,但关闭其他风扇（如果有）。如果您有完全独立的挤出机,这非常有用。</li><li><b>所有风扇:</b> 在喷嘴开关期间打开所有风扇。如果您有一个单独的冷却风扇或多个彼此靠近的风扇,这非常有用。</li></ul></html>"

msgctxt "cool_during_extruder_switch option all_fans"
msgid "All fans"
msgstr "所有风扇"

msgctxt "cool_during_extruder_switch label"
msgid "Cooling during extruder switch"
msgstr "挤出机切换期间的冷却"

msgctxt "support_z_seam_away_from_model description"
msgid "Manage the spatial relationship between the z seam of the support structure and the actual 3D model. This control is crucial as it allows users to ensure the seamless removal of support structures post-printing, without inflicting damage or leaving marks on the printed model."
msgstr "管理支撑结构的 z 形接缝与实际 3D 模型之间的空间关系。这个控制非常关键,因为它允许用户在打印后确保无缝去除支撑结构,而不会对打印模型造成损坏或留下痕迹。"

msgctxt "support_z_seam_min_distance label"
msgid "Min Z Seam Distance from Model"
msgstr "模型的最小 Z 形接缝距离"

msgctxt "support_infill_density_multiplier_initial_layer description"
msgid "Multiplier for the infill on the initial layers of the support. Increasing this may help for bed adhesion."
msgstr "支撑初始层填充的倍数。增加这个值可能有助于床附着力。"

msgctxt "cool_during_extruder_switch option only_last_extruder"
msgid "Only last extruder"
msgstr "仅最后一台挤出机"

msgctxt "z_seam_on_vertex description"
msgid "Place the z-seam on a polygon vertex. Switching this off can place the seam between vertices as well. (Keep in mind that this won't override the restrictions on placing the seam on an unsupported overhang.)"
msgstr "将 z 形接缝放置在多边形顶点上。关闭此功能也可以在顶点之间放置接缝。（请注意,这不会覆盖在未支撑悬垂上放置接缝的限制。）"

msgctxt "prime_tower_min_shell_thickness label"
msgid "Prime Tower Minimum Shell Thickness"
msgstr "引导塔最小外壳厚度"

msgctxt "raft_base_flow label"
msgid "Raft Base Flow"
msgstr "筏底流量"

msgctxt "raft_base_infill_overlap_mm label"
msgid "Raft Base Infill Overlap"
msgstr "筏底填充重叠"

msgctxt "raft_base_infill_overlap label"
msgid "Raft Base Infill Overlap Percentage"
msgstr "筏底填充重叠百分比"

msgctxt "raft_flow label"
msgid "Raft Flow"
msgstr "木筏流量"

msgctxt "raft_interface_flow label"
msgid "Raft Interface Flow"
msgstr "筏板界面层流量"

msgctxt "raft_interface_infill_overlap_mm label"
msgid "Raft Interface Infill Overlap"
msgstr "筏板界面层填充重叠"

msgctxt "raft_interface_infill_overlap label"
msgid "Raft Interface Infill Overlap Percentage"
msgstr "筏板界面层填充重叠百分比"

msgctxt "raft_interface_z_offset label"
msgid "Raft Interface Z Offset"
msgstr "筏板界面层 Z 偏移"

msgctxt "raft_surface_flow label"
msgid "Raft Surface Flow"
msgstr "木筏表面流量"

msgctxt "raft_surface_infill_overlap_mm label"
msgid "Raft Surface Infill Overlap"
msgstr "木筏表面填充重叠"

msgctxt "raft_surface_infill_overlap label"
msgid "Raft Surface Infill Overlap Percentage"
msgstr "木筏表面填充重叠百分比"

msgctxt "raft_surface_z_offset label"
msgid "Raft Surface Z Offset"
msgstr "木筏表面 Z 偏移"

msgctxt "seam_overhang_angle label"
msgid "Seam Overhanging Wall Angle"
msgstr "接缝悬垂墙角度"

msgctxt "support_infill_density_multiplier_initial_layer label"
msgid "Support Infill Density Multiplier Initial Layer"
msgstr "支持填充密度乘数初始层"

msgctxt "support_z_seam_away_from_model label"
msgid "Support Z Seam Away from Model"
msgstr "支持远离模型的 Z 形接缝"

msgctxt "raft_base_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft base printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "相对于正常挤出线,在筏底打印过程中挤出的材料量。增加流量可能会改善附着力和木筏结构强度。"

msgctxt "raft_interface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft interface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "相对于正常挤出线,在筏板界面打印期间挤出的材料量。增加流量可能会改善附着力和木筏结构强度。"

msgctxt "raft_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "相对于正常挤出线,木筏打印过程中挤出的材料量。增加流量可能会改善附着力和木筏结构强度。"

msgctxt "raft_surface_flow description"
msgid "The amount of material, relative to a normal extrusion line, to extrude during raft surface printing. Having an increased flow may improve adhesion and raft structural strength."
msgstr "相对于正常挤出线,在木筏表面打印期间挤出的材料量。增加流量可能会改善附着力和木筏结构强度。"

msgctxt "raft_base_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft base, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物与筏底墙壁之间的重叠量,以填充线宽度的百分比表示。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "raft_base_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft base. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物与筏底壁之间的重叠量。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "raft_interface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft interface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充与筏板界面墙壁之间的重叠量,以填充线宽度的百分比表示。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "raft_interface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft interface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物与筏板界面的墙壁之间的重叠量。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "raft_surface_infill_overlap description"
msgid "The amount of overlap between the infill and the walls of the raft surface, as a percentage of the infill line width. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充与木筏表面壁之间的重叠量,以填充线宽度的百分比表示。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "raft_surface_infill_overlap_mm description"
msgid "The amount of overlap between the infill and the walls of the raft surface. A slight overlap allows the walls to connect firmly to the infill."
msgstr "填充物与木筏表面的墙壁之间的重叠量。轻微重叠可以使墙壁牢固地连接到填充物上。"

msgctxt "support_z_seam_min_distance description"
msgid "The distance between the model and its support structure at the z-axis seam."
msgstr "模型与其支撑结构在z 轴接缝处的距离。"

msgctxt "prime_tower_min_shell_thickness description"
msgid "The minimum thickness of the prime tower shell. You may increase it to make the prime tower stronger."
msgstr "引导塔壳的最小厚度。您可以增加它以使引导塔更坚固。"

msgctxt "seam_overhang_angle description"
msgid "Try to prevent seams on walls that overhang more than this angle. When the value is 90, no walls will be treated as overhanging."
msgstr "应尽量避免墙壁上的接缝伸出超过此角度。当数值为 90 时,没有墙壁将被视为悬垂。"

msgctxt "cool_during_extruder_switch option unchanged"
msgid "Unchanged"
msgstr "不变"

msgctxt "raft_interface_z_offset description"
msgid "When printing the first layer of the raft interface, translate by this offset to customize the adhesion between base and interface. A negative offset should improve the adhesion."
msgstr "打印第一层的筏板界面时,通过这个偏移来自定义筏底和筏板界面之间的附着力。负偏移量应该可以提高附着力。"

msgctxt "raft_surface_z_offset description"
msgid "When printing the first layer of the raft surface, translate by this offset to customize the adhesion between interface and surface. A negative offset should improve the adhesion."
msgstr "打印第一层木筏表面时,通过这个偏移来自定义筏底和筏板界面之间的附着力。负偏移量应该可以提高附着力。"

msgctxt "z_seam_on_vertex label"
msgid "Z Seam On Vertex"
msgstr "顶点上的 Z 形接缝"

msgctxt "extra_infill_lines_to_support_skins description"
msgid "Add extra lines into the infill pattern to support skins above.   This option prevents holes or plastic blobs that sometime show in complex shaped skins due to the infill below not correctly supporting the skin layer being printed above.  'Walls' supports just the outlines of the skin, whereas 'Walls and Lines' also supports the ends of the lines that make up the skin."
msgstr "在填充模式中添加额外的线条以支撑上面的表皮。这一选项可以防止因下面的填充未能正确支撑上面打印的表皮层而导致的孔洞或塑料块,这在复杂形状的表皮中常见。\"墙\"仅支持表皮的轮廓,而\"墙和线\"还支持构成表皮的线条之末端。"

msgctxt "build_fan_full_at_height label"
msgid "Build Fan Speed at Height"
msgstr "在高度处的风扇速度"

msgctxt "build_fan_full_layer label"
msgid "Build Fan Speed at Layer"
msgstr "在层上的风扇速度"

msgctxt "build_volume_fan_nr label"
msgid "Build volume fan number"
msgstr "构建体积风扇编号"

msgctxt "scarf_split_distance description"
msgid "Determines the length of each step in the flow change when extruding along the scarf seam. A smaller distance will result in a more precise but also more complex G-code."
msgstr "确定在沿着缝合接缝挤出时流量变化中每一步的长度。较小的距离会导致更精确但也更复杂的 G-code。"

msgctxt "scarf_joint_seam_length description"
msgid "Determines the length of the scarf seam, a seam type that should make the Z seam less visible. Must be higher than 0 to be effective."
msgstr "确定缝合接缝的长度,这是一种应使 Z 接缝不那么明显的接缝类型。必须大于 0 才能有效。"

msgctxt "gradual_flow_discretisation_step_size description"
msgid "Duration of each step in the gradual flow change"
msgstr "渐变流量变化中每个步骤的持续时间"

msgctxt "gradual_flow_enabled description"
msgid "Enable gradual flow changes. When enabled, the flow is gradually increased/decreased to the target flow. This is useful for printers with a bowden tube where the flow is not immediately changed when the extruder motor starts/stops."
msgstr "启用渐变流量变化。启用之后,流量将会逐渐增加/减少到目标流量。这对于带有波登管的打印机非常有用,因为当挤出机电机启动/停止时,流量并不会立即改变。"

msgctxt "extra_infill_lines_to_support_skins label"
msgid "Extra Infill Lines To Support Skins"
msgstr "额外填充线以支撑表皮"

msgctxt "reset_flow_duration description"
msgid "For any travel move longer than this value, the material flow is reset to the paths target flow"
msgstr "对于任何长于此值的移动,材料流量将被重置为路径的目标流量"

msgctxt "gradual_flow_discretisation_step_size label"
msgid "Gradual flow discretisation step size"
msgstr "渐变流量离散化步长"

msgctxt "gradual_flow_enabled label"
msgid "Gradual flow enabled"
msgstr "启用渐变流量"

msgctxt "max_flow_acceleration label"
msgid "Gradual flow max acceleration"
msgstr "渐变流量最大加速度"

msgctxt "layer_0_max_flow_acceleration label"
msgid "Initial layer max flow acceleration"
msgstr "初始层最大流量加速"

msgctxt "max_flow_acceleration description"
msgid "Maximum acceleration for gradual flow changes"
msgstr "渐变流量变化的最大加速度"

msgctxt "layer_0_max_flow_acceleration description"
msgid "Minimum speed for gradual flow changes for the first layer"
msgstr "第一层渐变流量变化的最小速度"

msgctxt "extra_infill_lines_to_support_skins option none"
msgid "None"
msgstr "无"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Acceleration"
msgstr "外墙加速"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall Deceleration"
msgstr "外墙减速"

msgctxt "wall_0_end_speed_ratio label"
msgid "Outer Wall End Speed Ratio"
msgstr "外墙结束速度比率"

msgctxt "wall_0_speed_split_distance label"
msgid "Outer Wall Speed Split Distance"
msgstr "外墙速度分割距离"

msgctxt "wall_0_start_speed_ratio label"
msgid "Outer Wall Start Speed Ratio"
msgstr "外墙起始速度比率"

msgctxt "reset_flow_duration label"
msgid "Reset flow duration"
msgstr "重置流量持续时间"

msgctxt "scarf_joint_seam_length label"
msgid "Scarf Seam Length"
msgstr "缝合接缝长度"

msgctxt "scarf_joint_seam_start_height_ratio label"
msgid "Scarf Seam Start Height"
msgstr "缝合接缝起始高度"

msgctxt "scarf_split_distance label"
msgid "Scarf Seam Step Length"
msgstr "缝合接缝步长"

msgctxt "build_fan_full_at_height description"
msgid "The height at which the fans spin on regular fan speed. At the layers below the fan speed gradually increases from Initial Fan Speed to Regular Fan Speed."
msgstr "风扇在常规风速下旋转的高度。在下面的层中,风扇速度会逐渐从初始风速增加到常规风速。"

msgctxt "build_fan_full_layer description"
msgid "The layer at which the build fans spin on full fan speed. This value is calculated and rounded to a whole number."
msgstr "构建风扇以全速旋转的层数。此值经过计算并四舍五入为整数。"

msgctxt "build_volume_fan_nr description"
msgid "The number of the fan that cools the build volume. If this is set to 0, it's means that there is no build volume fan"
msgstr "冷却构建体积的风扇编号。如果设置为 0,则表示没有构建体积风扇"

msgctxt "scarf_joint_seam_start_height_ratio description"
msgid "The ratio of the selected layer height at which the scarf seam will begin. A lower number will result in a larger seam height. Must be lower than 100 to be effective."
msgstr "缝合接缝开始的层高比率。较低的数字将导致更大的缝合高度。必须低于 100 才能有效。"

msgctxt "wall_0_acceleration description"
msgid "This is the acceleration with which to reach the top speed when printing an outer wall."
msgstr "这是打印外墙时达到最高速度的加速度。"

msgctxt "wall_0_deceleration description"
msgid "This is the deceleration with which to end printing an outer wall."
msgstr "这是结束打印外墙时的减速度。"

msgctxt "wall_0_speed_split_distance description"
msgid "This is the maximum length of an extrusion path when splitting a longer path to apply the outer wall acceleration/deceleration. A smaller distance will create a more precise but also more verbose G-Code."
msgstr "这是在拆分更长路径以应用外墙加速/减速时挤出路径的最大长度。较小的距离将创建更精确但也更冗长的 G-Code。"

msgctxt "wall_0_end_speed_ratio description"
msgid "This is the ratio of the top speed to end with when printing an outer wall."
msgstr "这是打印外墙时在结束时的最高速度比率。"

msgctxt "wall_0_start_speed_ratio description"
msgid "This is the ratio of the top speed to start with when printing an outer wall."
msgstr "这是打印外墙时在开始时的最高速度比率。"

msgctxt "extra_infill_lines_to_support_skins option walls"
msgid "Walls Only"
msgstr "仅墙体"

msgctxt "extra_infill_lines_to_support_skins option walls_and_lines"
msgid "Walls and Lines"
msgstr "墙体和线条"

msgctxt "wall_overhang_angle description"
msgid "Walls that overhang more than this angle will be printed using overhanging wall settings. When the value is 90, no walls will be treated as overhanging. Overhang that gets supported by support will not be treated as overhang either. Furthermore, any line that's less than half overhanging will also not be treated as overhang."
msgstr "超过此角度的悬垂墙将使用悬垂墙设置打印。当值为 90 时,没有墙会被视为悬垂。得到支撑的悬垂也不会被视为悬垂。此外,任何少于一半悬垂的线也不会被视为悬垂。"

msgctxt "flooring_angles description"
msgid "A list of integer line directions to use when the bottom surface skin layers use the lines or zig zag pattern. Elements from the list are used sequentially as the layers progress and when the end of the list is reached, it starts at the beginning again. The list items are separated by commas and the whole list is contained in square brackets. Default is an empty list which means use the traditional default angles (45 and 135 degrees)."
msgstr "底部表面皮肤层使用线条或锯齿图案时使用的整数线条方向列表。随着层数的增加,列表中的元素按顺序使用,当到达列表末尾时,从头开始。列表项用逗号分隔,整个列表用方括号括起来。默认值为空列表,表示使用传统的默认角度（45度和135度）。"

msgctxt "acceleration_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Acceleration"
msgstr "底部表面内壁加速度"

msgctxt "jerk_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Jerk"
msgstr "底部表面内壁急动度"

msgctxt "speed_wall_x_flooring label"
msgid "Bottom Surface Inner Wall Speed"
msgstr "底部表面内壁速度"

msgctxt "wall_x_material_flow_flooring label"
msgid "Bottom Surface Inner Wall(s) Flow"
msgstr "底部表面内壁流量"

msgctxt "acceleration_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Acceleration"
msgstr "底部表面外壁加速度"

msgctxt "wall_0_material_flow_flooring label"
msgid "Bottom Surface Outer Wall Flow"
msgstr "底部表面外壁流量"

msgctxt "jerk_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Jerk"
msgstr "底部表面外壁急动度"

msgctxt "speed_wall_0_flooring label"
msgid "Bottom Surface Outer Wall Speed"
msgstr "底部表面外壁速度"

msgctxt "acceleration_flooring label"
msgid "Bottom Surface Skin Acceleration"
msgstr "底部表面皮肤加速度"

msgctxt "flooring_extruder_nr label"
msgid "Bottom Surface Skin Extruder"
msgstr "底部表面皮肤挤出机"

msgctxt "flooring_material_flow label"
msgid "Bottom Surface Skin Flow"
msgstr "底部表面皮肤流量"

msgctxt "jerk_flooring label"
msgid "Bottom Surface Skin Jerk"
msgstr "底部表面皮肤急动度"

msgctxt "flooring_layer_count label"
msgid "Bottom Surface Skin Layers"
msgstr "底部表面皮肤层数"

msgctxt "flooring_angles label"
msgid "Bottom Surface Skin Line Directions"
msgstr "底部表面皮肤线条方向"

msgctxt "flooring_line_width label"
msgid "Bottom Surface Skin Line Width"
msgstr "底部表面皮肤线条宽度"

msgctxt "flooring_pattern label"
msgid "Bottom Surface Skin Pattern"
msgstr "底部表面皮肤图案"

msgctxt "speed_flooring label"
msgid "Bottom Surface Skin Speed"
msgstr "底部表面皮肤速度"

msgctxt "flooring_pattern option concentric"
msgid "Concentric"
msgstr "同心"

msgctxt "variant_name"
msgid "Extruder"
msgstr "挤出机"

msgctxt "wall_x_material_flow_flooring description"
msgid "Flow compensation on bottom surface wall lines for all wall lines except the outermost one."
msgstr "底部表面壁线的流量补偿（除最外层外的所有壁线）。"

msgctxt "flooring_material_flow description"
msgid "Flow compensation on lines of the areas at the bottom of the print."
msgstr "打印底部区域线条的流量补偿"

msgctxt "wall_0_material_flow_flooring description"
msgid "Flow compensation on the bottom surface outermost wall line."
msgstr "打印底部表面最外层壁线的流量补偿"

msgctxt "machine_gcode_flavor option Cheetah"
msgid "Griffin+Cheetah"
msgstr "Griffin+Cheetah"

msgctxt "retraction_combing_avoid_distance label"
msgid "Inside Travel Avoid Distance"
msgstr "内部移动避让距离"

msgctxt "flooring_pattern option lines"
msgid "Lines"
msgstr "线条"

msgctxt "cool_min_layer_time_overhang label"
msgid "Minimum Layer Time with Overhang"
msgstr "带悬垂的最小层时间"

msgctxt "cool_min_layer_time_overhang_min_segment_length label"
msgid "Minimum Overhang Segment Length"
msgstr "最小悬垂段长度"

msgctxt "flooring_monotonic label"
msgid "Monotonic Bottom Surface Order"
msgstr "单调底部表面顺序"

msgctxt "wall_0_deceleration label"
msgid "Outer Wall End Deceleration"
msgstr "外壁结束减速"

msgctxt "wall_0_acceleration label"
msgid "Outer Wall Start Acceleration"
msgstr "外壁起始加速度"

msgctxt "wall_overhang_speed_factors label"
msgid "Overhanging Wall Speeds"
msgstr "悬垂壁速度"

msgctxt "wall_overhang_speed_factors description"
msgid "Overhanging walls will be printed at a percentage of their normal print speed. You can specify multiple values, so that even more overhanging walls will be printed even slower, e.g. by setting [75, 50, 25]"
msgstr "悬垂壁将以正常打印速度的百分比进行打印。您可以指定多个值,以便更悬垂的壁以更慢的速度打印,例如通过设置[75, 50, 25]。"

msgctxt "material_pressure_advance_factor label"
msgid "Pressure advance factor"
msgstr "压力推进因子"

msgctxt "variant_name"
msgid "Print Core"
msgstr "打印核心"

msgctxt "flooring_monotonic description"
msgid "Print bottom surface lines in an ordering that causes them to always overlap with adjacent lines in a single direction. This takes slightly more time to print, but makes flat surfaces look more consistent."
msgstr "以单方向重叠相邻线条的顺序打印底部表面线条。这需要稍长的打印时间,但可以使平坦表面看起来更加一致。"

msgctxt "machine_start_gcode_first label"
msgid "Start GCode must be first"
msgstr "启动G代码必须放在首位"

msgctxt "acceleration_flooring description"
msgid "The acceleration with which bottom surface skin layers are printed."
msgstr "打印底部表面皮肤层的加速度"

msgctxt "acceleration_wall_x_flooring description"
msgid "The acceleration with which the bottom surface inner walls are printed."
msgstr "打印底部表面内壁的加速度"

msgctxt "acceleration_wall_0_flooring description"
msgid "The acceleration with which the bottom surface outermost walls are printed."
msgstr "打印底部表面最外层壁的加速度"

msgctxt "machine_head_with_fans_polygon description"
msgid "The dimensions of the print head used to determine 'Safe Model Distance' when printing 'One at a Time'. These numbers relate to the centerline of the first extruder nozzle. Left of the nozzle is 'X Min' and must be negative.  Rear of the nozzle is 'Y Min' and must be negative.  X Max (right) and Y Max (front) are positive numbers.  Gantry height is the dimension from the build plate to the X gantry beam."
msgstr "用于在\"一次打印一个\"时确定\"安全模型距离\"的打印头尺寸。这些数字与第一个挤出机喷嘴的中心线相关。喷嘴左侧为\"X最小值\",必须为负数。喷嘴后方为\"Y最小值\",必须为负数。X最大值（右侧）和Y最大值（前方）为正数。龙门高度是从构建板到X龙门梁的尺寸。"

msgctxt "retraction_combing_avoid_distance description"
msgid "The distance between the nozzle and already printed outer walls when travelling inside a model."
msgstr "喷嘴与已打印外壁之间的内部移动距离"

msgctxt "flooring_extruder_nr description"
msgid "The extruder train used for printing the bottom most skin. This is used in multi-extrusion."
msgstr "用于打印最底部皮肤的挤出机轨道。这用于多挤出机设置。"

msgctxt "jerk_flooring description"
msgid "The maximum instantaneous velocity change with which bottom surface skin layers are printed."
msgstr "打印底部表面皮肤层的最大瞬时速度变化"

msgctxt "jerk_wall_x_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface inner walls are printed."
msgstr "打印底部表面内壁的最大瞬时速度变化"

msgctxt "jerk_wall_0_flooring description"
msgid "The maximum instantaneous velocity change with which the bottom surface outermost walls are printed."
msgstr "打印底部表面最外层壁的最大瞬时速度变化"

msgctxt "cool_min_layer_time_overhang description"
msgid "The minimum time spent in a layer that contains overhanging extrusions. This forces the printer to slow down, to at least spend the time set here in one layer. This allows the printed material to cool down properly before printing the next layer. Layers may still take shorter than the minimal layer time if Lift Head is disabled and if the Minimum Speed would otherwise be violated."
msgstr "包含悬垂挤出的层的最小时间。这迫使打印机减速,至少在此处设置的时间内完成一层打印。这允许打印材料在打印下一层之前适当冷却。如果\"提升打印头\"被禁用且最小速度未被违反,则层可能仍然比最小层时间短。"

msgctxt "flooring_layer_count description"
msgid "The number of bottom most skin layers. Usually only one bottom most layer is sufficient to generate higher quality bottom surfaces."
msgstr "最底部皮肤层的数量。通常只需一层最底部皮肤即可生成更高质量的底部表面。"

msgctxt "flooring_pattern description"
msgid "The pattern of the bottom most layers."
msgstr "最底部层的图案"

msgctxt "speed_flooring description"
msgid "The speed at which bottom surface skin layers are printed."
msgstr "打印底部表面皮肤层的速度"

msgctxt "speed_wall_x_flooring description"
msgid "The speed at which the bottom surface inner walls are printed."
msgstr "打印底部表面内壁的速度"

msgctxt "speed_wall_0_flooring description"
msgid "The speed at which the bottom surface outermost wall is printed."
msgstr "打印底部表面最外层壁的速度"

msgctxt "machine_start_gcode_first description"
msgid "This setting controls if the start-gcode is forced to always be the first g-code. Without this option other g-code, such as a T0 can be inserted before the start g-code."
msgstr "此设置控制启动G代码是否始终强制为第一个G代码。如果没有此选项,其他G代码（如T0）可能会插入到启动G代码之前。"

msgctxt "material_pressure_advance_factor description"
msgid "Tuning factor for pressure advance, which is meant to synchronize extrusion with motion"
msgstr "压力推进的调整因子,旨在将挤出与运动同步"

msgctxt "cool_min_layer_time_overhang_min_segment_length description"
msgid "When trying to apply the minimum layer time specific for overhanging layers, it will be applied only if at least one consecutive overhanging extrusion move is longer than this value."
msgstr "当尝试应用特定于悬垂层的最小层时间时,仅当至少一个连续的悬垂挤出移动长度超过此值时才会应用。"

msgctxt "flooring_line_width description"
msgid "Width of a single line of the areas at the bottom of the print."
msgstr "打印底部区域的单线宽度"

msgctxt "flooring_pattern option zigzag"
msgid "Zig Zag"
msgstr "锯齿形"
