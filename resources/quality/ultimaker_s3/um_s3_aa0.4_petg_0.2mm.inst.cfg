[general]
definition = ultimaker_s3
name = Fast
version = 4

[metadata]
material = generic_petg
quality_type = draft
setting_version = 25
type = quality
variant = AA 0.4
weight = -2

[values]
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'triangles'
material_print_temperature = =default_material_print_temperature + 5
speed_infill = =math.ceil(speed_print * 50 / 60)
speed_print = 60
speed_topbottom = =math.ceil(speed_print * 35 / 60)
speed_wall = =math.ceil(speed_print * 45 / 60)
speed_wall_0 = =math.ceil(speed_wall * 35 / 45)
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height

