[general]
definition = ultimaker_s5
name = Fine
version = 4

[metadata]
material = generic_pp
quality_type = normal
setting_version = 25
type = quality
variant = AA 0.4
weight = 0

[values]
brim_width = 20
infill_pattern = ='zigzag' if infill_sparse_density > 80 else 'tetrahedral'
infill_wipe_dist = 0.1
machine_min_cool_heat_time_window = 15
machine_nozzle_cool_down_speed = 0.85
machine_nozzle_heat_up_speed = 1.5
material_final_print_temperature = =material_print_temperature - 10
material_initial_print_temperature = =material_print_temperature - 10
material_print_temperature = =default_material_print_temperature - 2
multiple_mesh_overlap = 0
prime_tower_enable = False
prime_tower_size = 16
prime_tower_wipe_enabled = True
retraction_count_max = 15
retraction_extra_prime_amount = 0.8
retraction_hop = 2
retraction_hop_only_when_collides = True
speed_print = 25
speed_topbottom = =math.ceil(speed_print * 25 / 25)
speed_wall = =math.ceil(speed_print * 25 / 25)
speed_wall_0 = =math.ceil(speed_wall * 25 / 25)
support_angle = 50
support_bottom_distance = =support_z_distance
support_interface_enable = True
support_top_distance = =support_z_distance
support_z_distance = =math.ceil(0.3/layer_height)*layer_height
switch_extruder_prime_speed = 15
switch_extruder_retraction_amount = 20
switch_extruder_retraction_speeds = 35
top_bottom_thickness = 1
wall_0_inset = 0

